<template>
  <div class="accident-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>事故检测</h2>
      <a-space>
        <a-button @click="refreshData" :loading="isRefreshing">
          <template #icon><icon-refresh /></template>
          刷新数据
        </a-button>

        <!-- 紧急停止按钮，仅在开发环境显示 -->
        <a-button
          v-if="isDev"
          @click="emergencyStop"
          type="outline"
          status="danger"
          size="small"
        >
          紧急停止
        </a-button>
        <a-button @click="showConfigModal = true" v-if="userStore.user?.grade === 'admin'">
          <template #icon><icon-settings /></template>
          检测配置
        </a-button>
      </a-space>
    </div>

    <!-- 实时警报面板 -->
    <a-card title="实时警报" class="alert-panel">
      <div class="alert-controls">
        <a-space>
          <a-select
            v-model="alertFilter.monitor_id"
            placeholder="选择监控点"
            style="width: 200px;"
            allow-clear
            @change="fetchRealTimeAlerts"
          >
            <a-option
              v-for="monitor in monitors"
              :key="monitor.id"
              :value="monitor.id"
              :label="monitor.name"
            >
              {{ monitor.name }} - {{ monitor.location }}
            </a-option>
          </a-select>

          <a-select
            v-model="alertFilter.severity"
            placeholder="警报级别"
            style="width: 120px;"
            allow-clear
            @change="fetchRealTimeAlerts"
          >
            <a-option value="high">高</a-option>
            <a-option value="medium">中</a-option>
            <a-option value="low">低</a-option>
          </a-select>

          <a-select
            v-model="alertFilter.status"
            placeholder="状态"
            style="width: 120px;"
            allow-clear
            @change="fetchRealTimeAlerts"
          >
            <a-option value="active">活跃</a-option>
            <a-option value="acknowledged">已确认</a-option>
            <a-option value="resolved">已解决</a-option>
          </a-select>
        </a-space>
      </div>

      <div class="alerts-container">
        <a-spin :loading="alertsLoading">
          <div v-if="realTimeAlerts.length === 0" class="no-alerts">
            <a-empty description="暂无实时警报" />
          </div>
          <div v-else class="alerts-list">
            <div
              v-for="alert in realTimeAlerts"
              :key="alert.alert_id"
              class="alert-item"
              :class="`alert-${alert.severity}`"
            >
              <div class="alert-header">
                <div class="alert-info">
                  <h4>{{ alert.message }}</h4>
                  <p>监控点: {{ getMonitorName(alert.monitor_id) }}</p>
                </div>
                <div class="alert-meta">
                  <a-tag :color="getSeverityColor(alert.severity)">
                    {{ getSeverityName(alert.severity) }}
                  </a-tag>
                  <a-tag :color="getStatusColor(alert.status)">
                    {{ getStatusName(alert.status) }}
                  </a-tag>
                </div>
              </div>

              <div class="alert-details">
                <p>类型: {{ alert.alert_type }}</p>
                <p>置信度: {{ alert.confidence.toFixed(2) }}</p>
                <p>时间: {{ formatDateTime(alert.timestamp) }}</p>
                <p>位置: ({{ alert.location.x }}, {{ alert.location.y }})</p>
              </div>

              <div class="alert-actions">
                <a-space>
                  <a-button
                    size="small"
                    @click="acknowledgeAlert(alert.alert_id)"
                    v-if="alert.status === 'active'"
                  >
                    确认
                  </a-button>
                  <a-button
                    size="small"
                    type="primary"
                    @click="resolveAlert(alert.alert_id)"
                    v-if="alert.status === 'acknowledged'"
                  >
                    解决
                  </a-button>
                  <a-button
                    size="small"
                    @click="viewAlertDetails(alert)"
                  >
                    详情
                  </a-button>
                </a-space>
              </div>
            </div>
          </div>
        </a-spin>
      </div>
    </a-card>

    <!-- 检测控制和事故记录 -->
    <a-row :gutter="24">
      <a-col :span="8">
        <a-card title="检测控制" class="control-card">
          <a-form layout="vertical">
            <a-form-item label="选择监控点">
              <a-select
                v-model="selectedMonitorId"
                placeholder="请选择监控点"
                :loading="monitorLoading"
                @change="onMonitorChange"
              >
                <a-option
                  v-for="monitor in monitors"
                  :key="monitor.id"
                  :value="monitor.id"
                  :label="monitor.name"
                >
                  {{ monitor.name }} - {{ monitor.location }}
                </a-option>
              </a-select>
            </a-form-item>

            <a-form-item>
              <a-space direction="vertical" fill>
                <a-checkbox v-model="detectionConfig.enable_collision_detection">
                  碰撞检测
                </a-checkbox>
                <a-checkbox v-model="detectionConfig.enable_congestion_detection">
                  拥堵检测
                </a-checkbox>
                <a-checkbox v-model="detectionConfig.enable_abnormal_behavior_detection">
                  异常行为检测
                </a-checkbox>
              </a-space>
            </a-form-item>

            <a-form-item label="碰撞阈值">
              <a-slider
                v-model="detectionConfig.collision_threshold"
                :min="0.1"
                :max="1"
                :step="0.1"
                :format-tooltip="(value) => `${value}`"
              />
            </a-form-item>

            <a-form-item label="拥堵阈值">
              <a-slider
                v-model="detectionConfig.congestion_threshold"
                :min="0.1"
                :max="1"
                :step="0.1"
                :format-tooltip="(value) => `${value}`"
              />
            </a-form-item>

            <a-form-item>
              <a-space>
                <a-button
                  type="primary"
                  @click="startDetection"
                  :loading="detectionLoading"
                  :disabled="!selectedMonitorId || isDetecting"
                >
                  开始检测
                </a-button>
                <a-button
                  @click="stopDetection"
                  :disabled="!isDetecting"
                >
                  停止检测
                </a-button>
              </a-space>
            </a-form-item>
          </a-form>

          <!-- 检测状态 -->
          <div class="detection-status" v-if="isDetecting">
            <a-divider />
            <h4>检测状态</h4>
            <a-space direction="vertical" fill>
              <div class="status-item">
                <a-tag color="green">检测中</a-tag>
                <span>{{ getMonitorName(selectedMonitorId) }}</span>
              </div>
              <div class="status-item">
                <span>任务ID: {{ currentTaskId }}</span>
              </div>
            </a-space>
          </div>
        </a-card>
      </a-col>

      <a-col :span="16">
        <a-card title="事故记录" class="records-card">
          <!-- 搜索筛选 -->
          <div class="records-filter">
            <a-form layout="inline">
              <a-form-item label="事故类型">
                <a-select
                  v-model="recordFilter.accident_type"
                  placeholder="选择类型"
                  style="width: 150px;"
                  allow-clear
                >
                  <a-option value="collision">碰撞</a-option>
                  <a-option value="congestion">拥堵</a-option>
                  <a-option value="abnormal_behavior">异常行为</a-option>
                  <a-option value="emergency_stop">紧急停车</a-option>
                </a-select>
              </a-form-item>

              <a-form-item label="严重程度">
                <a-select
                  v-model="recordFilter.severity"
                  placeholder="选择程度"
                  style="width: 120px;"
                  allow-clear
                >
                  <a-option value="high">高</a-option>
                  <a-option value="medium">中</a-option>
                  <a-option value="low">低</a-option>
                </a-select>
              </a-form-item>

              <a-form-item label="状态">
                <a-select
                  v-model="recordFilter.status"
                  placeholder="选择状态"
                  style="width: 120px;"
                  allow-clear
                >
                  <a-option value="pending">待处理</a-option>
                  <a-option value="confirmed">已确认</a-option>
                  <a-option value="resolved">已解决</a-option>
                  <a-option value="false_alarm">误报</a-option>
                </a-select>
              </a-form-item>

              <a-form-item>
                <a-button type="primary" @click="searchRecords">
                  <template #icon><icon-search /></template>
                  搜索
                </a-button>
              </a-form-item>
            </a-form>
          </div>

          <!-- 事故记录表格 -->
          <a-table
            :columns="recordColumns"
            :data="accidentRecords"
            :loading="recordsLoading"
            :pagination="recordPagination"
            @page-change="handleRecordPageChange"
            size="small"
          >
            <template #accident_type="{ record }">
              <a-tag :color="getAccidentTypeColor(record.accident_type)">
                {{ getAccidentTypeName(record.accident_type) }}
              </a-tag>
            </template>

            <template #severity="{ record }">
              <a-tag :color="getSeverityColor(record.severity)">
                {{ getSeverityName(record.severity) }}
              </a-tag>
            </template>

            <template #status="{ record }">
              <a-tag :color="getRecordStatusColor(record.status)">
                {{ getRecordStatusName(record.status) }}
              </a-tag>
            </template>

            <template #actions="{ record }">
              <a-space>
                <a-button type="text" size="small" @click="viewRecord(record)">
                  查看
                </a-button>
                <a-button
                  type="text"
                  size="small"
                  @click="updateRecordStatus(record)"
                  v-if="record.status === 'pending'"
                >
                  处理
                </a-button>
              </a-space>
            </template>
          </a-table>
        </a-card>
      </a-col>
    </a-row>

    <!-- 检测配置弹窗 -->
    <a-modal
      v-model:visible="showConfigModal"
      title="事故检测配置"
      width="800px"
      @ok="saveConfig"
      @cancel="showConfigModal = false"
    >
      <div class="config-content">
        <h4>警报级别配置</h4>
        <a-form layout="vertical">
          <div v-for="level in ['low', 'medium', 'high']" :key="level" class="alert-level-config">
            <h5>{{ getSeverityName(level) }}级警报</h5>
            <a-checkbox v-model="configForm.alert_levels[level].enabled">
              启用{{ getSeverityName(level) }}级警报
            </a-checkbox>
            <a-form-item label="通知方式" v-if="configForm.alert_levels[level].enabled">
              <a-checkbox-group v-model="configForm.alert_levels[level].notification_methods">
                <a-checkbox value="email">邮件</a-checkbox>
                <a-checkbox value="sms">短信</a-checkbox>
                <a-checkbox value="push">推送</a-checkbox>
                <a-checkbox value="sound">声音</a-checkbox>
              </a-checkbox-group>
            </a-form-item>
          </div>

          <a-divider />

          <h4>自动录像配置</h4>
          <a-checkbox v-model="configForm.auto_recording.enabled">
            启用自动录像
          </a-checkbox>
          <div v-if="configForm.auto_recording.enabled">
            <a-form-item label="事前录制时长(秒)">
              <a-input-number
                v-model="configForm.auto_recording.pre_record_seconds"
                :min="0"
                :max="60"
              />
            </a-form-item>
            <a-form-item label="事后录制时长(秒)">
              <a-input-number
                v-model="configForm.auto_recording.post_record_seconds"
                :min="0"
                :max="300"
              />
            </a-form-item>
          </div>
        </a-form>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import {
  IconRefresh,
  IconSettings,
  IconSearch
} from '@arco-design/web-vue/es/icon'
import { accidentApi, type AccidentRecord, type AccidentConfig, type RealTimeAlert } from '@/api/accident'
import { monitorApi, type Monitor } from '@/api/monitor'
import { useAuthStore } from '@/stores/auth'

const userStore = useAuthStore()

// 开发环境检测
const isDev = import.meta.env.DEV

// 响应式数据
const selectedMonitorId = ref<number | null>(null)
const isDetecting = ref(false)
const currentTaskId = ref<string | null>(null)
const detectionLoading = ref(false)
const monitorLoading = ref(false)
const alertsLoading = ref(false)
const recordsLoading = ref(false)
const showConfigModal = ref(false)

const monitors = ref<Monitor[]>([])
const realTimeAlerts = ref<RealTimeAlert[]>([])
const accidentRecords = ref<AccidentRecord[]>([])

// 检测配置
const detectionConfig = reactive({
  enable_collision_detection: true,
  enable_congestion_detection: true,
  enable_abnormal_behavior_detection: true,
  collision_threshold: 0.7,
  congestion_threshold: 0.6
})

// 警报筛选
const alertFilter = reactive({
  monitor_id: null as number | null,
  severity: '',
  status: ''
})

// 记录筛选
const recordFilter = reactive({
  accident_type: '',
  severity: '',
  status: ''
})

// 记录分页
const recordPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showTotal: true
})

// 配置表单
const configForm = reactive({
  monitor_id: 0,
  enable_collision_detection: true,
  enable_congestion_detection: true,
  enable_abnormal_behavior_detection: true,
  collision_threshold: 0.7,
  congestion_threshold: 0.6,
  min_detection_area: 100,
  alert_levels: {
    low: {
      enabled: true,
      notification_methods: ['push']
    },
    medium: {
      enabled: true,
      notification_methods: ['push', 'sound']
    },
    high: {
      enabled: true,
      notification_methods: ['push', 'sound', 'email']
    }
  },
  auto_recording: {
    enabled: true,
    pre_record_seconds: 10,
    post_record_seconds: 30
  }
})

// 表格列定义
const recordColumns = [
  {
    title: '记录ID',
    dataIndex: 'record_id',
    width: 120,
    ellipsis: true
  },
  {
    title: '监控点',
    dataIndex: 'monitor_id',
    width: 100,
    render: ({ record }: any) => getMonitorName(record.monitor_id)
  },
  {
    title: '事故类型',
    dataIndex: 'accident_type',
    slotName: 'accident_type',
    width: 100
  },
  {
    title: '严重程度',
    dataIndex: 'severity',
    slotName: 'severity',
    width: 100
  },
  {
    title: '状态',
    dataIndex: 'status',
    slotName: 'status',
    width: 100
  },
  {
    title: '置信度',
    dataIndex: 'confidence',
    width: 80,
    render: ({ record }: any) => record.confidence.toFixed(2)
  },
  {
    title: '发生时间',
    dataIndex: 'create_time',
    width: 150,
    render: ({ record }: any) => formatDateTime(record.create_time)
  },
  {
    title: '操作',
    slotName: 'actions',
    width: 120,
    fixed: 'right'
  }
]

// 获取监控点列表
const fetchMonitors = async () => {
  monitorLoading.value = true
  try {
    // 添加超时控制
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('获取监控点列表超时')), 8000) // 8秒超时
    })

    const apiPromise = monitorApi.getMonitorList({ page: 1, size: 100 })
    const response = await Promise.race([apiPromise, timeoutPromise])

    if (response.success) {
      monitors.value = response.data.monitors?.filter(m => m.connection_status === 'online') || []
    } else {
      console.warn('获取监控点列表失败:', response.message)
      monitors.value = []
    }
  } catch (error: any) {
    console.error('获取监控点列表异常:', error)
    monitors.value = []
    Message.error('获取监控点列表失败: ' + (error.message || '未知错误'))
  } finally {
    monitorLoading.value = false
  }
}

// 数据适配函数
const adaptAlertData = (backendData: any) => {
  if (backendData?.alerts) {
    // 后端返回 {alerts: [...]} 格式
    return backendData.alerts.map((alert: any) => ({
      alert_id: alert.id || alert.alert_id,
      alert_type: alert.alarm_type || alert.alert_type || 'unknown',
      monitor_id: alert.monitor_id,
      monitor_name: alert.location || alert.monitor_name || `监控点${alert.monitor_id}`,
      severity: alert.severity || 'medium',
      confidence: alert.confidence_level || alert.confidence || 0,
      description: alert.description || '暂无描述',
      status: alert.status || 'pending',
      create_time: alert.create_time || alert.timestamp || new Date().toISOString(),
      location: alert.location_info || { x: 0, y: 0 }
    }))
  } else if (Array.isArray(backendData)) {
    // 后端直接返回数组格式
    return backendData.map((alert: any) => ({
      alert_id: alert.id || alert.alert_id,
      alert_type: alert.alarm_type || alert.alert_type || 'unknown',
      monitor_id: alert.monitor_id,
      monitor_name: alert.location || alert.monitor_name || `监控点${alert.monitor_id}`,
      severity: alert.severity || 'medium',
      confidence: alert.confidence_level || alert.confidence || 0,
      description: alert.description || '暂无描述',
      status: alert.status || 'pending',
      create_time: alert.create_time || alert.timestamp || new Date().toISOString(),
      location: alert.location_info || { x: 0, y: 0 }
    }))
  }
  return []
}

// 获取实时警报
const fetchRealTimeAlerts = async () => {
  alertsLoading.value = true
  try {
    const params = {
      monitor_id: alertFilter.monitor_id || undefined,
      severity: alertFilter.severity || undefined,
      status: alertFilter.status || undefined,
      limit: 20
    }

    // 添加超时控制
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('请求超时')), 10000) // 10秒超时
    })

    const apiPromise = accidentApi.getRealTimeAlerts(params)
    const response = await Promise.race([apiPromise, timeoutPromise])

    if (response.success) {
      // 使用数据适配函数处理后端返回的数据
      const adaptedData = adaptAlertData(response.data)
      realTimeAlerts.value = adaptedData
      console.log('实时警报数据适配完成:', adaptedData.length, '条记录')
    } else {
      console.warn('获取实时警报失败:', response.message)
      realTimeAlerts.value = []
    }
  } catch (error: any) {
    console.error('获取实时警报异常:', error)
    realTimeAlerts.value = []
    // 只在首次加载时显示错误，避免定时刷新时频繁弹窗
    if (!refreshTimer) {
      Message.error('获取实时警报失败: ' + (error.message || '未知错误'))
    }
  } finally {
    alertsLoading.value = false
  }
}

// 事故记录数据适配函数
const adaptRecordData = (backendData: any) => {
  if (backendData?.records) {
    // 后端返回 {records: [...]} 格式
    return backendData.records.map((record: any) => ({
      record_id: record.id || record.record_id,
      monitor_id: record.monitor_id,
      accident_type: record.accident_type || record.type || 'unknown',
      severity: record.severity || 'medium',
      location: record.location_info || record.location || { x: 0, y: 0 },
      description: record.description || '暂无描述',
      confidence: record.confidence_level || record.confidence || 0,
      status: record.status || 'pending',
      evidence_image: record.evidence_image || record.image_url,
      evidence_video: record.evidence_video || record.video_url,
      create_time: record.create_time || record.timestamp || new Date().toISOString(),
      resolve_time: record.resolve_time || record.resolved_at,
      resolved_by: record.resolved_by || record.resolver,
      notes: record.notes || record.remarks
    }))
  } else if (Array.isArray(backendData)) {
    // 后端直接返回数组格式
    return backendData.map((record: any) => ({
      record_id: record.id || record.record_id,
      monitor_id: record.monitor_id,
      accident_type: record.accident_type || record.type || 'unknown',
      severity: record.severity || 'medium',
      location: record.location_info || record.location || { x: 0, y: 0 },
      description: record.description || '暂无描述',
      confidence: record.confidence_level || record.confidence || 0,
      status: record.status || 'pending',
      evidence_image: record.evidence_image || record.image_url,
      evidence_video: record.evidence_video || record.video_url,
      create_time: record.create_time || record.timestamp || new Date().toISOString(),
      resolve_time: record.resolve_time || record.resolved_at,
      resolved_by: record.resolved_by || record.resolver,
      notes: record.notes || record.remarks
    }))
  }
  return []
}

// 获取事故记录
const fetchAccidentRecords = async () => {
  recordsLoading.value = true
  try {
    const params = {
      accident_type: recordFilter.accident_type || undefined,
      severity: recordFilter.severity || undefined,
      status: recordFilter.status || undefined,
      page: recordPagination.current,
      page_size: recordPagination.pageSize
    }

    // 添加超时控制
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('请求超时')), 10000) // 10秒超时
    })

    const apiPromise = accidentApi.getAccidentRecords(params)
    const response = await Promise.race([apiPromise, timeoutPromise])

    if (response.success) {
      // 使用数据适配函数处理后端返回的数据
      const adaptedData = adaptRecordData(response.data)
      accidentRecords.value = adaptedData
      recordPagination.total = response.data.total || adaptedData.length
      console.log('事故记录数据适配完成:', adaptedData.length, '条记录')
    } else {
      console.warn('获取事故记录失败:', response.message)
      accidentRecords.value = []
      recordPagination.total = 0
    }
  } catch (error: any) {
    console.error('获取事故记录异常:', error)
    accidentRecords.value = []
    recordPagination.total = 0
    // 只在首次加载时显示错误，避免定时刷新时频繁弹窗
    if (!refreshTimer) {
      Message.error('获取事故记录失败: ' + (error.message || '未知错误'))
    }
  } finally {
    recordsLoading.value = false
  }
}

// 开始检测
const startDetection = async () => {
  if (!selectedMonitorId.value) {
    Message.warning('请选择监控点')
    return
  }

  detectionLoading.value = true
  try {
    const response = await accidentApi.startAccidentDetection({
      monitor_id: selectedMonitorId.value,
      config: {
        monitor_id: selectedMonitorId.value,
        enable_collision_detection: detectionConfig.enable_collision_detection,
        enable_congestion_detection: detectionConfig.enable_congestion_detection,
        enable_abnormal_behavior_detection: detectionConfig.enable_abnormal_behavior_detection,
        collision_threshold: detectionConfig.collision_threshold,
        congestion_threshold: detectionConfig.congestion_threshold,
        min_detection_area: 100,
        alert_levels: configForm.alert_levels,
        auto_recording: configForm.auto_recording
      }
    })

    if (response.success) {
      isDetecting.value = true
      currentTaskId.value = response.data.task_id
      Message.success('事故检测已开始')
      startDataRefresh()
    }
  } catch (error: any) {
    Message.error('启动事故检测失败: ' + (error.message || '未知错误'))
  } finally {
    detectionLoading.value = false
  }
}

// 停止检测
const stopDetection = async () => {
  if (!selectedMonitorId.value) return

  try {
    const response = await accidentApi.stopAccidentDetection({
      monitor_id: selectedMonitorId.value,
      task_id: currentTaskId.value || undefined
    })

    if (response.success) {
      isDetecting.value = false
      currentTaskId.value = null
      Message.success('事故检测已停止')
      stopDataRefresh()
    }
  } catch (error: any) {
    Message.error('停止事故检测失败: ' + (error.message || '未知错误'))
  }
}

// 监控点变化
const onMonitorChange = () => {
  if (isDetecting.value) {
    stopDetection()
  }
}

// 确认警报
const acknowledgeAlert = async (alertId: string) => {
  try {
    const response = await accidentApi.acknowledgeAlert(alertId)
    if (response.success) {
      Message.success('警报已确认')
      fetchRealTimeAlerts()
    }
  } catch (error: any) {
    Message.error('确认警报失败: ' + (error.message || '未知错误'))
  }
}

// 解决警报
const resolveAlert = async (alertId: string) => {
  try {
    const response = await accidentApi.resolveAlert(alertId, {
      notes: '已处理'
    })
    if (response.success) {
      Message.success('警报已解决')
      fetchRealTimeAlerts()
    }
  } catch (error: any) {
    Message.error('解决警报失败: ' + (error.message || '未知错误'))
  }
}

// 查看警报详情
const viewAlertDetails = (alert: RealTimeAlert) => {
  Message.info('查看警报详情功能待实现')
}

// 搜索记录
const searchRecords = () => {
  recordPagination.current = 1
  fetchAccidentRecords()
}

// 记录分页变化
const handleRecordPageChange = (page: number) => {
  recordPagination.current = page
  fetchAccidentRecords()
}

// 查看记录
const viewRecord = (record: AccidentRecord) => {
  Message.info('查看记录详情功能待实现')
}

// 更新记录状态
const updateRecordStatus = async (record: AccidentRecord) => {
  try {
    const response = await accidentApi.updateAccidentRecord(record.record_id, {
      status: 'confirmed',
      notes: '已确认处理'
    })
    if (response.success) {
      Message.success('记录状态已更新')
      fetchAccidentRecords()
    }
  } catch (error: any) {
    Message.error('更新记录状态失败: ' + (error.message || '未知错误'))
  }
}

// 保存配置
const saveConfig = async () => {
  if (!selectedMonitorId.value) {
    Message.warning('请先选择监控点')
    return
  }

  try {
    configForm.monitor_id = selectedMonitorId.value
    const response = await accidentApi.updateAccidentConfig(configForm)
    if (response.success) {
      Message.success('配置保存成功')
      showConfigModal.value = false
    }
  } catch (error: any) {
    Message.error('保存配置失败: ' + (error.message || '未知错误'))
  }
}

// 工具方法
const getMonitorName = (monitorId: number | null) => {
  if (!monitorId) return ''
  const monitor = monitors.value.find(m => m.id === monitorId)
  return monitor ? monitor.name : `监控点${monitorId}`
}

const getSeverityColor = (severity: string) => {
  switch (severity) {
    case 'high': return 'red'
    case 'medium': return 'orange'
    case 'low': return 'blue'
    default: return 'gray'
  }
}

const getSeverityName = (severity: string) => {
  switch (severity) {
    case 'high': return '高'
    case 'medium': return '中'
    case 'low': return '低'
    default: return '未知'
  }
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'active': return 'red'
    case 'acknowledged': return 'orange'
    case 'resolved': return 'green'
    default: return 'gray'
  }
}

const getStatusName = (status: string) => {
  switch (status) {
    case 'active': return '活跃'
    case 'acknowledged': return '已确认'
    case 'resolved': return '已解决'
    default: return '未知'
  }
}

const getAccidentTypeColor = (type: string) => {
  switch (type) {
    case 'collision': return 'red'
    case 'congestion': return 'orange'
    case 'abnormal_behavior': return 'blue'
    case 'emergency_stop': return 'purple'
    default: return 'gray'
  }
}

const getAccidentTypeName = (type: string) => {
  switch (type) {
    case 'collision': return '碰撞'
    case 'congestion': return '拥堵'
    case 'abnormal_behavior': return '异常行为'
    case 'emergency_stop': return '紧急停车'
    default: return '未知'
  }
}

const getRecordStatusColor = (status: string) => {
  switch (status) {
    case 'pending': return 'orange'
    case 'confirmed': return 'blue'
    case 'resolved': return 'green'
    case 'false_alarm': return 'gray'
    default: return 'gray'
  }
}

const getRecordStatusName = (status: string) => {
  switch (status) {
    case 'pending': return '待处理'
    case 'confirmed': return '已确认'
    case 'resolved': return '已解决'
    case 'false_alarm': return '误报'
    default: return '未知'
  }
}

const formatDateTime = (dateStr: string) => {
  return new Date(dateStr).toLocaleString()
}

// 数据刷新
let refreshTimer: NodeJS.Timeout | null = null
let isRefreshing = false

const startDataRefresh = () => {
  // 避免重复启动定时器
  if (refreshTimer) {
    stopDataRefresh()
  }

  refreshTimer = setInterval(async () => {
    // 避免并发刷新
    if (isRefreshing) {
      console.log('数据刷新中，跳过本次刷新')
      return
    }

    isRefreshing = true
    try {
      // 只刷新实时警报，减少请求频率
      await fetchRealTimeAlerts()
    } catch (error) {
      console.error('定时刷新失败:', error)
    } finally {
      isRefreshing = false
    }
  }, 10000) // 改为10秒刷新一次，减少频率
}

const stopDataRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
  isRefreshing = false
}

const refreshData = async () => {
  if (isRefreshing) {
    Message.warning('数据刷新中，请稍候...')
    return
  }

  isRefreshing = true
  try {
    await Promise.all([
      fetchRealTimeAlerts(),
      fetchAccidentRecords()
    ])
    Message.success('数据刷新完成')
  } catch (error) {
    console.error('手动刷新失败:', error)
  } finally {
    isRefreshing = false
  }
}

// 紧急停止功能（开发环境）
const emergencyStop = () => {
  console.log('执行紧急停止')

  // 停止所有定时器
  stopDataRefresh()

  // 强制重置所有loading状态
  alertsLoading.value = false
  recordsLoading.value = false
  monitorsLoading.value = false
  isRefreshing = false

  // 清空数据
  realTimeAlerts.value = []
  accidentRecords.value = []

  Message.success('已紧急停止所有加载状态')
}

// 生命周期
onMounted(() => {
  console.log('事故检测页面加载')

  // 不使用await，让页面立即渲染，数据异步加载
  // 这样即使API慢也不会阻塞页面渲染

  // 异步加载监控点列表（优先级最高）
  fetchMonitors().catch(error => {
    console.error('获取监控点失败:', error)
  })

  // 延迟加载其他数据，避免同时请求过多API
  setTimeout(() => {
    fetchRealTimeAlerts().catch(error => {
      console.error('获取实时警报失败:', error)
    })
  }, 500)

  setTimeout(() => {
    fetchAccidentRecords().catch(error => {
      console.error('获取事故记录失败:', error)
    })
  }, 1000)

  // 延迟启动定时刷新，确保页面已完全加载
  setTimeout(() => {
    startDataRefresh()
  }, 2000)
})

onUnmounted(() => {
  console.log('事故检测页面卸载')
  stopDataRefresh()

  // 清理所有loading状态
  alertsLoading.value = false
  recordsLoading.value = false
  monitorsLoading.value = false
})
</script>

<style scoped>
.accident-page {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.page-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.alert-panel {
  margin-bottom: 16px;
}

.alert-controls {
  margin-bottom: 16px;
}

.alerts-container {
  max-height: 400px;
  overflow-y: auto;
}

.no-alerts {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.alerts-list {
  space-y: 12px;
}

.alert-item {
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid;
  background: #fafafa;
  margin-bottom: 12px;
}

.alert-item.alert-high {
  border-left-color: #f5222d;
  background: #fff2f0;
}

.alert-item.alert-medium {
  border-left-color: #faad14;
  background: #fffbe6;
}

.alert-item.alert-low {
  border-left-color: #1890ff;
  background: #f0f8ff;
}

.alert-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.alert-info h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
}

.alert-info p {
  margin: 0;
  font-size: 12px;
  color: #86909c;
}

.alert-meta {
  display: flex;
  gap: 8px;
}

.alert-details {
  margin-bottom: 12px;
}

.alert-details p {
  margin: 2px 0;
  font-size: 12px;
  color: #4e5969;
}

.alert-actions {
  text-align: right;
}

.control-card {
  height: 600px;
}

.detection-status {
  margin-top: 16px;
}

.detection-status h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.records-card {
  height: 600px;
}

.records-filter {
  margin-bottom: 16px;
}

.config-content {
  max-height: 500px;
  overflow-y: auto;
}

.alert-level-config {
  margin-bottom: 24px;
  padding: 16px;
  background: #f7f8fa;
  border-radius: 8px;
}

.alert-level-config h5 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
}
</style>
