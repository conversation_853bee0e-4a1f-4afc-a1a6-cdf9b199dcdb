# -*- coding: utf-8 -*-
# @Description : Supervision库兼容性工具
# @Date : 2025年6月21日

import supervision as sv
import numpy as np

def create_detections_from_result(result):
    """
    从YOLO结果创建supervision.Detections对象，兼容不同版本的supervision库
    
    Args:
        result: YOLO检测结果对象
        
    Returns:
        sv.Detections: supervision检测对象
    """
    try:
        # 尝试新版本API (supervision >= 0.16.0)
        detections = sv.Detections.from_ultralytics(result)
        return detections
    except AttributeError:
        try:
            # 尝试旧版本API (supervision < 0.16.0)
            detections = sv.Detections.from_yolov8(result)
            return detections
        except AttributeError:
            # 手动创建Detections对象
            if result.boxes is not None:
                boxes = result.boxes.xyxy.cpu().numpy()
                confidences = result.boxes.conf.cpu().numpy()
                class_ids = result.boxes.cls.cpu().numpy().astype(int)
                track_ids = result.boxes.id.cpu().numpy().astype(int) if result.boxes.id is not None else None
                
                detections = sv.Detections(
                    xyxy=boxes,
                    confidence=confidences,
                    class_id=class_ids,
                    tracker_id=track_ids
                )
                return detections
            else:
                # 创建空的检测结果
                return sv.Detections.empty()

def get_supervision_version():
    """获取supervision库版本"""
    try:
        return sv.__version__
    except AttributeError:
        return "unknown"

def check_supervision_compatibility():
    """检查supervision库兼容性"""
    version = get_supervision_version()
    print(f"Supervision版本: {version}")
    
    # 检查可用的API
    has_from_ultralytics = hasattr(sv.Detections, 'from_ultralytics')
    has_from_yolov8 = hasattr(sv.Detections, 'from_yolov8')
    
    print(f"支持from_ultralytics: {has_from_ultralytics}")
    print(f"支持from_yolov8: {has_from_yolov8}")
    
    return {
        'version': version,
        'has_from_ultralytics': has_from_ultralytics,
        'has_from_yolov8': has_from_yolov8
    }

if __name__ == "__main__":
    # 测试兼容性
    print("🔍 检查Supervision库兼容性")
    print("="*40)
    
    compatibility = check_supervision_compatibility()
    
    if compatibility['has_from_ultralytics']:
        print("✅ 推荐使用from_ultralytics方法")
    elif compatibility['has_from_yolov8']:
        print("⚠️ 使用旧版from_yolov8方法")
    else:
        print("❌ 需要手动创建Detections对象")
    
    print("\n使用create_detections_from_result函数可以自动处理兼容性问题")
