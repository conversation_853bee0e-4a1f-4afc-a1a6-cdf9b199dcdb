# -*- coding: utf-8 -*-
# u5f3au5236u663eu793au5bf9u8bddu6846u7684u6d4bu8bd5u811au672c

import sys
from PySide6.QtWidgets import QApplication, QMainWindow, QPushButton, QLabel, QDialog, QVBoxLayout

def create_simple_dialog():
    """Guarantee to create a visible dialog"""
    # Create the app
    app = QApplication(sys.argv)
    
    # Create main window
    main_window = QMainWindow()
    main_window.setWindowTitle("u4e3bu7a97u53e3")
    main_window.resize(400, 300)
    
    # Create a button that will show a dialog
    button = QPushButton("u663eu793au5bf9u8bddu6846", main_window)
    button.move(150, 150)
    
    # Create the dialog
    dialog = QDialog(main_window)
    dialog.setWindowTitle("u6d4bu8bd5u5bf9u8bddu6846")
    dialog.resize(300, 200)
    
    # Add content to dialog
    layout = QVBoxLayout(dialog)
    label = QLabel("u8fd9u662fu4e00u4e2au6d4bu8bd5u5bf9u8bddu6846", dialog)
    layout.addWidget(label)
    
    # Connect button to show dialog
    button.clicked.connect(dialog.show)
    
    # Show main window
    main_window.show()
    
    # Run the application
    sys.exit(app.exec())

if __name__ == "__main__":
    create_simple_dialog()
