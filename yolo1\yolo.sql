/*
 基于Yolov8与ByteTrack的高速公路智慧监控平台 - 完整数据库结构
 Date: 2025-06-20
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 删除现有数据库并重新创建
DROP DATABASE IF EXISTS yolo;
CREATE DATABASE yolo CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE yolo;

-- ----------------------------
-- Table structure for user
-- ----------------------------
CREATE TABLE `user` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '用户id',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码(MD5加密)',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮箱',
  `avatar` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '头像',
  `grade` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户级别',
  `status` tinyint(1) DEFAULT 1 COMMENT '用户状态(0:禁用,1:启用)',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_username` (`username`) USING BTREE,
  UNIQUE KEY `uk_email` (`email`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- ----------------------------
-- Table structure for monitor
-- ----------------------------
CREATE TABLE `monitor` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '监控id',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '监控点名称',
  `location` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '监控地点',
  `highway_section` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '高速公路路段',
  `camera_position` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '摄像头位置说明',
  `latitude` decimal(10,7) DEFAULT NULL COMMENT '纬度',
  `longitude` decimal(10,7) DEFAULT NULL COMMENT '经度',
  `threshold` int NOT NULL DEFAULT 15 COMMENT '警报阈值',
  `conf_threshold` float DEFAULT 0.3 COMMENT '置信度阈值',
  `iou_threshold` float DEFAULT 0.5 COMMENT 'IOU阈值',
  `person` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置人员',
  `video` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '视频文件路径',
  `url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'RTSP拉流路径',
  `rtsp_format` varchar(50) DEFAULT 'rtsp' COMMENT 'RTSP路径格式',
  `connection_status` varchar(20) DEFAULT 'unknown' COMMENT 'RTSP连接状态(online,offline,error,unknown)',
  `is_alarm` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '开启' COMMENT '警报状态(开启,关闭)',
  `mode` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'detection' COMMENT '算法模式(detection,tracking)',
  `show_labels` tinyint(1) DEFAULT 1 COMMENT '是否显示标签',
  `enable_tracking` tinyint(1) DEFAULT 0 COMMENT '是否启用多目标追踪',
  `tracker_type` varchar(20) DEFAULT 'bytetrack' COMMENT '追踪器类型(bytetrack,botsort)',
  `status` tinyint(1) DEFAULT 1 COMMENT '监控点状态(0:禁用,1:启用)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_location` (`location`) USING BTREE,
  KEY `idx_highway_section` (`highway_section`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='监控点表';

-- ----------------------------
-- Table structure for alarm
-- ----------------------------
CREATE TABLE `alarm` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '警报id',
  `monitor_id` int NOT NULL COMMENT '监控点ID',
  `location` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '警报地点',
  `highway_section` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '高速公路路段',
  `alarm_type` varchar(50) DEFAULT 'vehicle_count' COMMENT '警报类型(vehicle_count,traffic_jam,accident)',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '警报信息',
  `vehicle_count` int DEFAULT 0 COMMENT '车辆数量',
  `detection_details` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '检测详细数据(JSON格式)',
  `confidence_level` float DEFAULT NULL COMMENT '检测置信度',
  `threshold` int NOT NULL COMMENT '监控阈值',
  `photo` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '照片路径',
  `video_clip` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '视频片段路径',
  `severity` varchar(20) DEFAULT 'medium' COMMENT '严重程度(low,medium,high,critical)',
  `status` varchar(20) DEFAULT 'pending' COMMENT '处理状态(pending,processing,resolved,ignored)',
  `handled_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '处理人',
  `handled_time` datetime DEFAULT NULL COMMENT '处理时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_monitor_id` (`monitor_id`) USING BTREE,
  KEY `idx_alarm_type` (`alarm_type`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE,
  CONSTRAINT `fk_alarm_monitor` FOREIGN KEY (`monitor_id`) REFERENCES `monitor` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='警报表';

-- ----------------------------
-- Table structure for detection_task
-- ----------------------------
CREATE TABLE `detection_task` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `task_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '任务名称',
  `task_type` varchar(20) NOT NULL COMMENT '任务类型(image,video,stream)',
  `file_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '文件路径',
  `file_size` bigint DEFAULT NULL COMMENT '文件大小(字节)',
  `file_format` varchar(20) DEFAULT NULL COMMENT '文件格式',
  `model_name` varchar(50) DEFAULT 'yolov8n.pt' COMMENT '使用的模型',
  `conf_threshold` float DEFAULT 0.3 COMMENT '置信度阈值',
  `iou_threshold` float DEFAULT 0.5 COMMENT 'IOU阈值',
  `enable_tracking` tinyint(1) DEFAULT 0 COMMENT '是否启用追踪',
  `tracker_type` varchar(20) DEFAULT 'bytetrack' COMMENT '追踪器类型',
  `status` varchar(20) DEFAULT 'pending' COMMENT '任务状态(pending,running,completed,failed)',
  `progress` int DEFAULT 0 COMMENT '进度百分比',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `result_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '结果文件路径',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '错误信息',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_task_type` (`task_type`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='检测任务表';

-- ----------------------------
-- Table structure for detection_result
-- ----------------------------
CREATE TABLE `detection_result` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '结果ID',
  `task_id` int NOT NULL COMMENT '任务ID',
  `frame_number` int DEFAULT NULL COMMENT '帧号',
  `timestamp` decimal(10,3) DEFAULT NULL COMMENT '时间戳',
  `object_count` int DEFAULT 0 COMMENT '检测到的对象数量',
  `detection_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '检测数据(JSON格式)',
  `tracking_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '追踪数据(JSON格式)',
  `inference_time` float DEFAULT NULL COMMENT '推理时间(毫秒)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_task_id` (`task_id`) USING BTREE,
  KEY `idx_frame_number` (`frame_number`) USING BTREE,
  CONSTRAINT `fk_result_task` FOREIGN KEY (`task_id`) REFERENCES `detection_task` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='检测结果表';

-- ----------------------------
-- Table structure for system_config
-- ----------------------------
CREATE TABLE `system_config` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置键',
  `config_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '配置值',
  `config_type` varchar(20) DEFAULT 'string' COMMENT '配置类型(string,int,float,bool,json)',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '配置描述',
  `category` varchar(50) DEFAULT 'general' COMMENT '配置分类',
  `is_editable` tinyint(1) DEFAULT 1 COMMENT '是否可编辑',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_config_key` (`config_key`) USING BTREE,
  KEY `idx_category` (`category`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- ----------------------------
-- Table structure for system_log
-- ----------------------------
CREATE TABLE `system_log` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `log_level` varchar(10) NOT NULL COMMENT '日志级别(DEBUG,INFO,WARN,ERROR)',
  `module` varchar(50) DEFAULT NULL COMMENT '模块名称',
  `action` varchar(100) DEFAULT NULL COMMENT '操作动作',
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '日志消息',
  `user_id` int DEFAULT NULL COMMENT '用户ID',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户代理',
  `request_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '请求数据',
  `response_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '响应数据',
  `execution_time` float DEFAULT NULL COMMENT '执行时间(毫秒)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_log_level` (`log_level`) USING BTREE,
  KEY `idx_module` (`module`) USING BTREE,
  KEY `idx_user_id` (`user_id`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统日志表';

-- ----------------------------
-- Table structure for traffic_statistics
-- ----------------------------
CREATE TABLE `traffic_statistics` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `monitor_id` int NOT NULL COMMENT '监控点ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `stat_hour` int DEFAULT NULL COMMENT '统计小时(0-23)',
  `vehicle_count` int DEFAULT 0 COMMENT '车辆数量',
  `avg_speed` float DEFAULT NULL COMMENT '平均速度',
  `max_speed` float DEFAULT NULL COMMENT '最高速度',
  `min_speed` float DEFAULT NULL COMMENT '最低速度',
  `traffic_density` float DEFAULT NULL COMMENT '交通密度',
  `congestion_level` varchar(20) DEFAULT 'normal' COMMENT '拥堵程度(smooth,normal,congested,jammed)',
  `weather_condition` varchar(50) DEFAULT NULL COMMENT '天气状况',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_monitor_date_hour` (`monitor_id`,`stat_date`,`stat_hour`) USING BTREE,
  KEY `idx_stat_date` (`stat_date`) USING BTREE,
  KEY `idx_congestion_level` (`congestion_level`) USING BTREE,
  CONSTRAINT `fk_traffic_monitor` FOREIGN KEY (`monitor_id`) REFERENCES `monitor` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交通统计表';

-- ----------------------------
-- 插入初始数据
-- ----------------------------

-- ----------------------------
-- 插入初始数据
-- ----------------------------

-- 插入用户数据
INSERT INTO `user` (`id`, `username`, `password`, `email`, `avatar`, `grade`, `status`, `create_by`, `remark`) VALUES
(1, 'admin', '21232f297a57a5a743894a0e4a801fc3', '<EMAIL>', '/static/avatars/admin.jpg', '超级管理员', 1, 'system', '系统默认管理员账号'),
(2, 'operator', '4b583376b2767b923c3e1da60d10de59', '<EMAIL>', '/static/avatars/operator.jpg', '操作员', 1, 'admin', '系统操作员账号'),
(3, 'viewer', '5d41402abc4b2a76b9719d911017c592', '<EMAIL>', '/static/avatars/viewer.jpg', '观察员', 1, 'admin', '只读权限账号');

-- 插入监控点数据 (杭州至千岛湖高速公路)
INSERT INTO `monitor` (`id`, `name`, `location`, `highway_section`, `camera_position`, `latitude`, `longitude`, `threshold`, `conf_threshold`, `iou_threshold`, `person`, `url`, `connection_status`, `is_alarm`, `mode`, `enable_tracking`, `tracker_type`, `create_by`, `remark`) VALUES
(1, '杭州收费站监控点', '杭州收费站', 'K0+000', '收费站出入口', 30.2741, 120.1551, 15, 0.4, 0.5, 'admin', 'rtsp://admin:123456@192.168.1.101:554/stream1', 'online', '开启', 'tracking', 1, 'bytetrack', 'admin', '杭州收费站主要监控点'),
(2, '杭州主线监控点', '杭州主线', 'K5+200', '主线车道', 30.2891, 120.1721, 20, 0.3, 0.5, 'admin', 'rtsp://admin:123456@192.168.1.102:554/stream1', 'online', '开启', 'tracking', 1, 'bytetrack', 'admin', '杭州主线交通监控'),
(3, '富阳互通监控点', '富阳互通', 'K18+600', '互通匝道', 30.0498, 119.9528, 18, 0.4, 0.5, 'operator', 'rtsp://admin:123456@192.168.1.103:554/stream1', 'online', '开启', 'detection', 0, 'bytetrack', 'admin', '富阳互通交通监控'),
(4, '桐庐服务区监控点', '桐庐服务区', 'K35+800', '服务区入口', 29.7971, 119.6811, 12, 0.3, 0.5, 'operator', 'rtsp://admin:123456@192.168.1.104:554/stream1', 'online', '开启', 'tracking', 1, 'botsort', 'admin', '桐庐服务区监控'),
(5, '桐庐隧道群监控点', '桐庐隧道群', 'K42+300', '隧道入口', 29.7234, 119.5987, 25, 0.4, 0.5, 'operator', 'rtsp://admin:123456@192.168.1.105:554/stream1', 'online', '开启', 'detection', 0, 'bytetrack', 'admin', '隧道群安全监控'),
(6, '富春江大桥监控点', '富春江大桥', 'K48+100', '大桥中段', 29.6789, 119.5234, 22, 0.3, 0.5, 'admin', 'rtsp://admin:123456@192.168.1.106:554/stream1', 'online', '开启', 'tracking', 1, 'bytetrack', 'admin', '富春江大桥监控'),
(7, '建德互通监控点', '建德互通', 'K58+900', '互通主线', 29.4765, 119.2876, 16, 0.4, 0.5, 'operator', 'rtsp://admin:123456@192.168.1.107:554/stream1', 'online', '开启', 'detection', 0, 'bytetrack', 'admin', '建德互通监控'),
(8, '建德山区弯道监控点', '建德山区弯道', 'K65+400', '急弯路段', 29.3987, 119.1654, 20, 0.4, 0.5, 'operator', 'rtsp://admin:123456@192.168.1.108:554/stream1', 'online', '开启', 'tracking', 1, 'bytetrack', 'admin', '山区弯道安全监控'),
(9, '淳安风景区监控点', '淳安风景区', 'K78+200', '风景区路段', 29.6089, 118.9234, 14, 0.3, 0.5, 'admin', 'rtsp://admin:123456@192.168.1.109:554/stream1', 'online', '开启', 'detection', 0, 'botsort', 'admin', '风景区交通监控'),
(10, '千岛湖收费站监控点', '千岛湖收费站', 'K95+000', '收费站广场', 29.5678, 118.8765, 18, 0.4, 0.5, 'admin', 'rtsp://admin:123456@192.168.1.110:554/stream1', 'online', '开启', 'tracking', 1, 'bytetrack', 'admin', '千岛湖收费站监控'),
(11, '应急车道监控点', '应急车道', 'K25+500', '应急车道', 29.8765, 119.7654, 8, 0.5, 0.5, 'operator', 'rtsp://admin:123456@192.168.1.111:554/stream1', 'online', '开启', 'detection', 0, 'bytetrack', 'admin', '应急车道违停监控'),
(12, '气象监测点', '气象监测站', 'K55+300', '气象站', 29.5432, 119.3456, 30, 0.3, 0.5, 'admin', 'rtsp://admin:123456@192.168.1.112:554/stream1', 'online', '开启', 'detection', 0, 'bytetrack', 'admin', '气象条件监控'),
(13, '施工区域监控点', '施工区域', 'K72+800', '施工路段', 29.4321, 119.0987, 10, 0.4, 0.5, 'operator', 'rtsp://admin:123456@192.168.1.113:554/stream1', 'offline', '开启', 'tracking', 1, 'bytetrack', 'admin', '施工区域安全监控'),
(14, '事故多发段监控点', '事故多发段', 'K38+700', '事故多发路段', 29.7123, 119.6234, 25, 0.4, 0.5, 'admin', 'rtsp://admin:123456@*************:554/stream1', 'online', '开启', 'tracking', 1, 'botsort', 'admin', '事故多发段重点监控'),
(15, '夜间照明监控点', '夜间照明区', 'K88+400', '照明设施', 29.5987, 118.9876, 12, 0.3, 0.5, 'operator', 'rtsp://admin:123456@*************:554/stream1', 'online', '开启', 'detection', 0, 'bytetrack', 'admin', '夜间照明效果监控');

-- 插入警报数据
INSERT INTO `alarm` (`id`, `monitor_id`, `location`, `highway_section`, `alarm_type`, `description`, `vehicle_count`, `detection_details`, `confidence_level`, `threshold`, `photo`, `severity`, `status`, `create_time`, `remark`) VALUES
(1, 1, '杭州收费站', 'K0+000', 'vehicle_count', '车流量超标：检测到17辆车，超过阈值15', 17, '{"total_vehicles":17,"cars":12,"trucks":3,"buses":2,"confidence_avg":0.85}', 0.85, 15, '/static/alarms/2025-06-20/alarm_001.jpg', 'medium', 'resolved', '2025-06-20 08:15:30', '早高峰车流量超标'),
(2, 1, '杭州收费站', 'K0+000', 'vehicle_count', '车流量超标：检测到22辆车，超过阈值15', 22, '{"total_vehicles":22,"cars":16,"trucks":4,"buses":2,"confidence_avg":0.82}', 0.82, 15, '/static/alarms/2025-06-20/alarm_002.jpg', 'high', 'resolved', '2025-06-20 08:30:45', '早高峰持续拥堵'),
(3, 3, '富阳互通', 'K18+600', 'vehicle_count', '车流量超标：检测到20辆车，超过阈值18', 20, '{"total_vehicles":20,"cars":15,"trucks":3,"buses":2,"confidence_avg":0.78}', 0.78, 18, '/static/alarms/2025-06-20/alarm_003.jpg', 'medium', 'pending', '2025-06-20 09:12:15', '互通匝道车辆聚集'),
(4, 5, '桐庐隧道群', 'K42+300', 'vehicle_count', '车流量超标：检测到28辆车，超过阈值25', 28, '{"total_vehicles":28,"cars":20,"trucks":6,"buses":2,"confidence_avg":0.88}', 0.88, 25, '/static/alarms/2025-06-20/alarm_004.jpg', 'high', 'processing', '2025-06-20 10:45:20', '隧道入口车辆排队'),
(5, 6, '富春江大桥', 'K48+100', 'vehicle_count', '车流量超标：检测到25辆车，超过阈值22', 25, '{"total_vehicles":25,"cars":18,"trucks":5,"buses":2,"confidence_avg":0.83}', 0.83, 22, '/static/alarms/2025-06-20/alarm_005.jpg', 'medium', 'resolved', '2025-06-20 11:20:10', '大桥路段车流密集'),
(6, 8, '建德山区弯道', 'K65+400', 'vehicle_count', '车流量超标：检测到23辆车，超过阈值20', 23, '{"total_vehicles":23,"cars":17,"trucks":4,"buses":2,"confidence_avg":0.79}', 0.79, 20, '/static/alarms/2025-06-20/alarm_006.jpg', 'medium', 'resolved', '2025-06-20 14:35:25', '山区弯道车辆缓行'),
(7, 10, '千岛湖收费站', 'K95+000', 'vehicle_count', '车流量超标：检测到21辆车，超过阈值18', 21, '{"total_vehicles":21,"cars":16,"trucks":3,"buses":2,"confidence_avg":0.86}', 0.86, 18, '/static/alarms/2025-06-20/alarm_007.jpg', 'medium', 'pending', '2025-06-20 16:50:40', '千岛湖收费站排队'),
(8, 14, '事故多发段', 'K38+700', 'vehicle_count', '车流量超标：检测到30辆车，超过阈值25', 30, '{"total_vehicles":30,"cars":22,"trucks":6,"buses":2,"confidence_avg":0.81}', 0.81, 25, '/static/alarms/2025-06-20/alarm_008.jpg', 'critical', 'processing', '2025-06-20 17:15:55', '事故多发段严重拥堵'),
(9, 2, '杭州主线', 'K5+200', 'vehicle_count', '车流量超标：检测到24辆车，超过阈值20', 24, '{"total_vehicles":24,"cars":18,"trucks":4,"buses":2,"confidence_avg":0.84}', 0.84, 20, '/static/alarms/2025-06-20/alarm_009.jpg', 'high', 'resolved', '2025-06-20 18:22:30', '晚高峰车流增加'),
(10, 4, '桐庐服务区', 'K35+800', 'vehicle_count', '车流量超标：检测到15辆车，超过阈值12', 15, '{"total_vehicles":15,"cars":11,"trucks":2,"buses":2,"confidence_avg":0.77}', 0.77, 12, '/static/alarms/2025-06-20/alarm_010.jpg', 'low', 'resolved', '2025-06-20 19:08:45', '服务区车辆聚集');

-- ----------------------------
-- Table structure for file_upload
-- ----------------------------
CREATE TABLE `file_upload` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '文件ID',
  `original_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '原始文件名',
  `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '存储文件名',
  `file_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件路径',
  `file_size` bigint NOT NULL COMMENT '文件大小(字节)',
  `file_type` varchar(50) NOT NULL COMMENT '文件类型',
  `mime_type` varchar(100) DEFAULT NULL COMMENT 'MIME类型',
  `file_hash` varchar(64) DEFAULT NULL COMMENT '文件哈希值(MD5)',
  `upload_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '上传用户',
  `upload_ip` varchar(50) DEFAULT NULL COMMENT '上传IP',
  `status` varchar(20) DEFAULT 'active' COMMENT '文件状态(active,deleted,processing)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_file_hash` (`file_hash`) USING BTREE,
  KEY `idx_upload_by` (`upload_by`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件上传表';

-- ----------------------------
-- Table structure for performance_metrics
-- ----------------------------
CREATE TABLE `performance_metrics` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '指标ID',
  `metric_type` varchar(50) NOT NULL COMMENT '指标类型(cpu,memory,disk,network,gpu)',
  `metric_name` varchar(100) NOT NULL COMMENT '指标名称',
  `metric_value` decimal(10,4) NOT NULL COMMENT '指标值',
  `unit` varchar(20) DEFAULT NULL COMMENT '单位',
  `host_name` varchar(100) DEFAULT NULL COMMENT '主机名',
  `collect_time` datetime NOT NULL COMMENT '采集时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_metric_type` (`metric_type`) USING BTREE,
  KEY `idx_collect_time` (`collect_time`) USING BTREE,
  KEY `idx_host_name` (`host_name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='性能指标表';

-- ----------------------------
-- Table structure for realtime_data
-- ----------------------------
CREATE TABLE `realtime_data` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '数据ID',
  `monitor_id` int NOT NULL COMMENT '监控点ID',
  `data_type` varchar(50) NOT NULL COMMENT '数据类型(detection,tracking,status)',
  `data_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '数据内容(JSON格式)',
  `frame_number` int DEFAULT NULL COMMENT '帧号',
  `timestamp` decimal(15,3) NOT NULL COMMENT '时间戳',
  `processing_time` float DEFAULT NULL COMMENT '处理时间(毫秒)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_monitor_id` (`monitor_id`) USING BTREE,
  KEY `idx_data_type` (`data_type`) USING BTREE,
  KEY `idx_timestamp` (`timestamp`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE,
  CONSTRAINT `fk_realtime_monitor` FOREIGN KEY (`monitor_id`) REFERENCES `monitor` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='实时数据表';

-- ----------------------------
-- Table structure for websocket_sessions
-- ----------------------------
CREATE TABLE `websocket_sessions` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '会话ID',
  `session_id` varchar(100) NOT NULL COMMENT 'WebSocket会话ID',
  `user_id` int DEFAULT NULL COMMENT '用户ID',
  `client_ip` varchar(50) DEFAULT NULL COMMENT '客户端IP',
  `user_agent` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户代理',
  `connected_rooms` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '已连接房间(JSON格式)',
  `connect_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '连接时间',
  `last_activity` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后活动时间',
  `disconnect_time` datetime DEFAULT NULL COMMENT '断开时间',
  `status` varchar(20) DEFAULT 'connected' COMMENT '状态(connected,disconnected)',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_session_id` (`session_id`) USING BTREE,
  KEY `idx_user_id` (`user_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_connect_time` (`connect_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='WebSocket会话表';

-- ----------------------------
-- Table structure for model_management
-- ----------------------------
CREATE TABLE `model_management` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '模型ID',
  `model_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模型名称',
  `model_version` varchar(50) DEFAULT '1.0.0' COMMENT '模型版本',
  `model_type` varchar(50) NOT NULL COMMENT '模型类型(yolo,custom)',
  `model_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模型文件路径',
  `model_size` bigint DEFAULT NULL COMMENT '模型文件大小(字节)',
  `input_size` varchar(20) DEFAULT '640x640' COMMENT '输入尺寸',
  `num_classes` int DEFAULT NULL COMMENT '类别数量',
  `class_names` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '类别名称(JSON格式)',
  `performance_metrics` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '性能指标(JSON格式)',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '模型描述',
  `is_default` tinyint(1) DEFAULT 0 COMMENT '是否默认模型',
  `status` varchar(20) DEFAULT 'active' COMMENT '状态(active,inactive,training)',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_model_name_version` (`model_name`,`model_version`) USING BTREE,
  KEY `idx_model_type` (`model_type`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='模型管理表';

-- ----------------------------
-- Table structure for api_access_log
-- ----------------------------
CREATE TABLE `api_access_log` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` int DEFAULT NULL COMMENT '用户ID',
  `api_path` varchar(200) NOT NULL COMMENT 'API路径',
  `http_method` varchar(10) NOT NULL COMMENT 'HTTP方法',
  `request_ip` varchar(50) DEFAULT NULL COMMENT '请求IP',
  `user_agent` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户代理',
  `request_params` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '请求参数',
  `response_status` int DEFAULT NULL COMMENT '响应状态码',
  `response_time` float DEFAULT NULL COMMENT '响应时间(毫秒)',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '错误信息',
  `access_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '访问时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_user_id` (`user_id`) USING BTREE,
  KEY `idx_api_path` (`api_path`) USING BTREE,
  KEY `idx_access_time` (`access_time`) USING BTREE,
  KEY `idx_response_status` (`response_status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='API访问日志表';

-- ----------------------------
-- Table structure for notification
-- ----------------------------
CREATE TABLE `notification` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '通知ID',
  `user_id` int DEFAULT NULL COMMENT '用户ID(NULL表示系统通知)',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '通知标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '通知内容',
  `type` varchar(50) DEFAULT 'info' COMMENT '通知类型(info,warning,error,success)',
  `priority` varchar(20) DEFAULT 'normal' COMMENT '优先级(low,normal,high,urgent)',
  `related_type` varchar(50) DEFAULT NULL COMMENT '关联类型(alarm,monitor,system)',
  `related_id` int DEFAULT NULL COMMENT '关联ID',
  `is_read` tinyint(1) DEFAULT 0 COMMENT '是否已读',
  `read_time` datetime DEFAULT NULL COMMENT '阅读时间',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_user_id` (`user_id`) USING BTREE,
  KEY `idx_type` (`type`) USING BTREE,
  KEY `idx_is_read` (`is_read`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知表';

-- ----------------------------
-- 插入初始数据
-- ----------------------------

-- ----------------------------
-- 插入初始数据
-- ----------------------------

-- 插入用户数据
INSERT INTO `user` (`id`, `username`, `password`, `email`, `avatar`, `grade`, `status`, `create_by`, `remark`) VALUES
(1, 'admin', '21232f297a57a5a743894a0e4a801fc3', '<EMAIL>', '/static/avatars/admin.jpg', '超级管理员', 1, 'system', '系统默认管理员账号'),
(2, 'operator', '4b583376b2767b923c3e1da60d10de59', '<EMAIL>', '/static/avatars/operator.jpg', '操作员', 1, 'admin', '系统操作员账号'),
(3, 'viewer', '5d41402abc4b2a76b9719d911017c592', '<EMAIL>', '/static/avatars/viewer.jpg', '观察员', 1, 'admin', '只读权限账号');

-- 插入监控点数据 (杭州至千岛湖高速公路)
INSERT INTO `monitor` (`id`, `name`, `location`, `highway_section`, `camera_position`, `latitude`, `longitude`, `threshold`, `conf_threshold`, `iou_threshold`, `person`, `url`, `connection_status`, `is_alarm`, `mode`, `enable_tracking`, `tracker_type`, `create_by`, `remark`) VALUES
(1, '杭州收费站监控点', '杭州收费站', 'K0+000', '收费站出入口', 30.2741, 120.1551, 15, 0.4, 0.5, 'admin', 'rtsp://admin:123456@192.168.1.101:554/stream1', 'online', '开启', 'tracking', 1, 'bytetrack', 'admin', '杭州收费站主要监控点'),
(2, '杭州主线监控点', '杭州主线', 'K5+200', '主线车道', 30.2891, 120.1721, 20, 0.3, 0.5, 'admin', 'rtsp://admin:123456@192.168.1.102:554/stream1', 'online', '开启', 'tracking', 1, 'bytetrack', 'admin', '杭州主线交通监控'),
(3, '富阳互通监控点', '富阳互通', 'K18+600', '互通匝道', 30.0498, 119.9528, 18, 0.4, 0.5, 'operator', 'rtsp://admin:123456@192.168.1.103:554/stream1', 'online', '开启', 'detection', 0, 'bytetrack', 'admin', '富阳互通交通监控'),
(4, '桐庐服务区监控点', '桐庐服务区', 'K35+800', '服务区入口', 29.7971, 119.6811, 12, 0.3, 0.5, 'operator', 'rtsp://admin:123456@192.168.1.104:554/stream1', 'online', '开启', 'tracking', 1, 'botsort', 'admin', '桐庐服务区监控'),
(5, '桐庐隧道群监控点', '桐庐隧道群', 'K42+300', '隧道入口', 29.7234, 119.5987, 25, 0.4, 0.5, 'operator', 'rtsp://admin:123456@192.168.1.105:554/stream1', 'online', '开启', 'detection', 0, 'bytetrack', 'admin', '隧道群安全监控'),
(6, '富春江大桥监控点', '富春江大桥', 'K48+100', '大桥中段', 29.6789, 119.5234, 22, 0.3, 0.5, 'admin', 'rtsp://admin:123456@192.168.1.106:554/stream1', 'online', '开启', 'tracking', 1, 'bytetrack', 'admin', '富春江大桥监控'),
(7, '建德互通监控点', '建德互通', 'K58+900', '互通主线', 29.4765, 119.2876, 16, 0.4, 0.5, 'operator', 'rtsp://admin:123456@192.168.1.107:554/stream1', 'online', '开启', 'detection', 0, 'bytetrack', 'admin', '建德互通监控'),
(8, '建德山区弯道监控点', '建德山区弯道', 'K65+400', '急弯路段', 29.3987, 119.1654, 20, 0.4, 0.5, 'operator', 'rtsp://admin:123456@192.168.1.108:554/stream1', 'online', '开启', 'tracking', 1, 'bytetrack', 'admin', '山区弯道安全监控'),
(9, '淳安风景区监控点', '淳安风景区', 'K78+200', '风景区路段', 29.6089, 118.9234, 14, 0.3, 0.5, 'admin', 'rtsp://admin:123456@192.168.1.109:554/stream1', 'online', '开启', 'detection', 0, 'botsort', 'admin', '风景区交通监控'),
(10, '千岛湖收费站监控点', '千岛湖收费站', 'K95+000', '收费站广场', 29.5678, 118.8765, 18, 0.4, 0.5, 'admin', 'rtsp://admin:123456@192.168.1.110:554/stream1', 'online', '开启', 'tracking', 1, 'bytetrack', 'admin', '千岛湖收费站监控'),
(11, '应急车道监控点', '应急车道', 'K25+500', '应急车道', 29.8765, 119.7654, 8, 0.5, 0.5, 'operator', 'rtsp://admin:123456@192.168.1.111:554/stream1', 'online', '开启', 'detection', 0, 'bytetrack', 'admin', '应急车道违停监控'),
(12, '气象监测点', '气象监测站', 'K55+300', '气象站', 29.5432, 119.3456, 30, 0.3, 0.5, 'admin', 'rtsp://admin:123456@192.168.1.112:554/stream1', 'online', '开启', 'detection', 0, 'bytetrack', 'admin', '气象条件监控'),
(13, '施工区域监控点', '施工区域', 'K72+800', '施工路段', 29.4321, 119.0987, 10, 0.4, 0.5, 'operator', 'rtsp://admin:123456@192.168.1.113:554/stream1', 'offline', '开启', 'tracking', 1, 'bytetrack', 'admin', '施工区域安全监控'),
(14, '事故多发段监控点', '事故多发段', 'K38+700', '事故多发路段', 29.7123, 119.6234, 25, 0.4, 0.5, 'admin', 'rtsp://admin:123456@*************:554/stream1', 'online', '开启', 'tracking', 1, 'botsort', 'admin', '事故多发段重点监控'),
(15, '夜间照明监控点', '夜间照明区', 'K88+400', '照明设施', 29.5987, 118.9876, 12, 0.3, 0.5, 'operator', 'rtsp://admin:123456@*************:554/stream1', 'online', '开启', 'detection', 0, 'bytetrack', 'admin', '夜间照明效果监控');

-- 插入警报数据
INSERT INTO `alarm` (`id`, `monitor_id`, `location`, `highway_section`, `alarm_type`, `description`, `vehicle_count`, `detection_details`, `confidence_level`, `threshold`, `photo`, `severity`, `status`, `create_time`, `remark`) VALUES
(1, 1, '杭州收费站', 'K0+000', 'vehicle_count', '车流量超标：检测到17辆车，超过阈值15', 17, '{"total_vehicles":17,"cars":12,"trucks":3,"buses":2,"confidence_avg":0.85}', 0.85, 15, '/static/alarms/2025-06-20/alarm_001.jpg', 'medium', 'resolved', '2025-06-20 08:15:30', '早高峰车流量超标'),
(2, 1, '杭州收费站', 'K0+000', 'vehicle_count', '车流量超标：检测到22辆车，超过阈值15', 22, '{"total_vehicles":22,"cars":16,"trucks":4,"buses":2,"confidence_avg":0.82}', 0.82, 15, '/static/alarms/2025-06-20/alarm_002.jpg', 'high', 'resolved', '2025-06-20 08:30:45', '早高峰持续拥堵'),
(3, 3, '富阳互通', 'K18+600', 'vehicle_count', '车流量超标：检测到20辆车，超过阈值18', 20, '{"total_vehicles":20,"cars":15,"trucks":3,"buses":2,"confidence_avg":0.78}', 0.78, 18, '/static/alarms/2025-06-20/alarm_003.jpg', 'medium', 'pending', '2025-06-20 09:12:15', '互通匝道车辆聚集'),
(4, 5, '桐庐隧道群', 'K42+300', 'vehicle_count', '车流量超标：检测到28辆车，超过阈值25', 28, '{"total_vehicles":28,"cars":20,"trucks":6,"buses":2,"confidence_avg":0.88}', 0.88, 25, '/static/alarms/2025-06-20/alarm_004.jpg', 'high', 'processing', '2025-06-20 10:45:20', '隧道入口车辆排队'),
(5, 6, '富春江大桥', 'K48+100', 'vehicle_count', '车流量超标：检测到25辆车，超过阈值22', 25, '{"total_vehicles":25,"cars":18,"trucks":5,"buses":2,"confidence_avg":0.83}', 0.83, 22, '/static/alarms/2025-06-20/alarm_005.jpg', 'medium', 'resolved', '2025-06-20 11:20:10', '大桥路段车流密集'),
(6, 8, '建德山区弯道', 'K65+400', 'vehicle_count', '车流量超标：检测到23辆车，超过阈值20', 23, '{"total_vehicles":23,"cars":17,"trucks":4,"buses":2,"confidence_avg":0.79}', 0.79, 20, '/static/alarms/2025-06-20/alarm_006.jpg', 'medium', 'resolved', '2025-06-20 14:35:25', '山区弯道车辆缓行'),
(7, 10, '千岛湖收费站', 'K95+000', 'vehicle_count', '车流量超标：检测到21辆车，超过阈值18', 21, '{"total_vehicles":21,"cars":16,"trucks":3,"buses":2,"confidence_avg":0.86}', 0.86, 18, '/static/alarms/2025-06-20/alarm_007.jpg', 'medium', 'pending', '2025-06-20 16:50:40', '千岛湖收费站排队'),
(8, 14, '事故多发段', 'K38+700', 'vehicle_count', '车流量超标：检测到30辆车，超过阈值25', 30, '{"total_vehicles":30,"cars":22,"trucks":6,"buses":2,"confidence_avg":0.81}', 0.81, 25, '/static/alarms/2025-06-20/alarm_008.jpg', 'critical', 'processing', '2025-06-20 17:15:55', '事故多发段严重拥堵'),
(9, 2, '杭州主线', 'K5+200', 'vehicle_count', '车流量超标：检测到24辆车，超过阈值20', 24, '{"total_vehicles":24,"cars":18,"trucks":4,"buses":2,"confidence_avg":0.84}', 0.84, 20, '/static/alarms/2025-06-20/alarm_009.jpg', 'high', 'resolved', '2025-06-20 18:22:30', '晚高峰车流增加'),
(10, 4, '桐庐服务区', 'K35+800', 'vehicle_count', '车流量超标：检测到15辆车，超过阈值12', 15, '{"total_vehicles":15,"cars":11,"trucks":2,"buses":2,"confidence_avg":0.77}', 0.77, 12, '/static/alarms/2025-06-20/alarm_010.jpg', 'low', 'resolved', '2025-06-20 19:08:45', '服务区车辆聚集');

-- ----------------------------
-- Table structure for file_upload
-- ----------------------------
CREATE TABLE `file_upload` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '文件ID',
  `original_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '原始文件名',
  `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '存储文件名',
  `file_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件路径',
  `file_size` bigint NOT NULL COMMENT '文件大小(字节)',
  `file_type` varchar(50) NOT NULL COMMENT '文件类型',
  `mime_type` varchar(100) DEFAULT NULL COMMENT 'MIME类型',
  `file_hash` varchar(64) DEFAULT NULL COMMENT '文件哈希值(MD5)',
  `upload_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '上传用户',
  `upload_ip` varchar(50) DEFAULT NULL COMMENT '上传IP',
  `status` varchar(20) DEFAULT 'active' COMMENT '文件状态(active,deleted,processing)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_file_hash` (`file_hash`) USING BTREE,
  KEY `idx_upload_by` (`upload_by`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件上传表';

-- ----------------------------
-- Table structure for performance_metrics
-- ----------------------------
CREATE TABLE `performance_metrics` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '指标ID',
  `metric_type` varchar(50) NOT NULL COMMENT '指标类型(cpu,memory,disk,network,gpu)',
  `metric_name` varchar(100) NOT NULL COMMENT '指标名称',
  `metric_value` decimal(10,4) NOT NULL COMMENT '指标值',
  `unit` varchar(20) DEFAULT NULL COMMENT '单位',
  `host_name` varchar(100) DEFAULT NULL COMMENT '主机名',
  `collect_time` datetime NOT NULL COMMENT '采集时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_metric_type` (`metric_type`) USING BTREE,
  KEY `idx_collect_time` (`collect_time`) USING BTREE,
  KEY `idx_host_name` (`host_name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='性能指标表';

-- ----------------------------
-- Table structure for realtime_data
-- ----------------------------
CREATE TABLE `realtime_data` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '数据ID',
  `monitor_id` int NOT NULL COMMENT '监控点ID',
  `data_type` varchar(50) NOT NULL COMMENT '数据类型(detection,tracking,status)',
  `data_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '数据内容(JSON格式)',
  `frame_number` int DEFAULT NULL COMMENT '帧号',
  `timestamp` decimal(15,3) NOT NULL COMMENT '时间戳',
  `processing_time` float DEFAULT NULL COMMENT '处理时间(毫秒)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_monitor_id` (`monitor_id`) USING BTREE,
  KEY `idx_data_type` (`data_type`) USING BTREE,
  KEY `idx_timestamp` (`timestamp`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE,
  CONSTRAINT `fk_realtime_monitor` FOREIGN KEY (`monitor_id`) REFERENCES `monitor` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='实时数据表';

-- ----------------------------
-- Table structure for websocket_sessions
-- ----------------------------
CREATE TABLE `websocket_sessions` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '会话ID',
  `session_id` varchar(100) NOT NULL COMMENT 'WebSocket会话ID',
  `user_id` int DEFAULT NULL COMMENT '用户ID',
  `client_ip` varchar(50) DEFAULT NULL COMMENT '客户端IP',
  `user_agent` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户代理',
  `connected_rooms` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '已连接房间(JSON格式)',
  `connect_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '连接时间',
  `last_activity` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后活动时间',
  `disconnect_time` datetime DEFAULT NULL COMMENT '断开时间',
  `status` varchar(20) DEFAULT 'connected' COMMENT '状态(connected,disconnected)',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_session_id` (`session_id`) USING BTREE,
  KEY `idx_user_id` (`user_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_connect_time` (`connect_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='WebSocket会话表';

-- ----------------------------
-- Table structure for model_management
-- ----------------------------
CREATE TABLE `model_management` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '模型ID',
  `model_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模型名称',
  `model_version` varchar(50) DEFAULT '1.0.0' COMMENT '模型版本',
  `model_type` varchar(50) NOT NULL COMMENT '模型类型(yolo,custom)',
  `model_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模型文件路径',
  `model_size` bigint DEFAULT NULL COMMENT '模型文件大小(字节)',
  `input_size` varchar(20) DEFAULT '640x640' COMMENT '输入尺寸',
  `num_classes` int DEFAULT NULL COMMENT '类别数量',
  `class_names` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '类别名称(JSON格式)',
  `performance_metrics` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '性能指标(JSON格式)',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '模型描述',
  `is_default` tinyint(1) DEFAULT 0 COMMENT '是否默认模型',
  `status` varchar(20) DEFAULT 'active' COMMENT '状态(active,inactive,training)',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_model_name_version` (`model_name`,`model_version`) USING BTREE,
  KEY `idx_model_type` (`model_type`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='模型管理表';

-- ----------------------------
-- Table structure for api_access_log
-- ----------------------------
CREATE TABLE `api_access_log` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` int DEFAULT NULL COMMENT '用户ID',
  `api_path` varchar(200) NOT NULL COMMENT 'API路径',
  `http_method` varchar(10) NOT NULL COMMENT 'HTTP方法',
  `request_ip` varchar(50) DEFAULT NULL COMMENT '请求IP',
  `user_agent` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户代理',
  `request_params` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '请求参数',
  `response_status` int DEFAULT NULL COMMENT '响应状态码',
  `response_time` float DEFAULT NULL COMMENT '响应时间(毫秒)',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '错误信息',
  `access_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '访问时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_user_id` (`user_id`) USING BTREE,
  KEY `idx_api_path` (`api_path`) USING BTREE,
  KEY `idx_access_time` (`access_time`) USING BTREE,
  KEY `idx_response_status` (`response_status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='API访问日志表';

-- ----------------------------
-- Table structure for notification
-- ----------------------------
CREATE TABLE `notification` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '通知ID',
  `user_id` int DEFAULT NULL COMMENT '用户ID(NULL表示系统通知)',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '通知标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '通知内容',
  `type` varchar(50) DEFAULT 'info' COMMENT '通知类型(info,warning,error,success)',
  `priority` varchar(20) DEFAULT 'normal' COMMENT '优先级(low,normal,high,urgent)',
  `related_type` varchar(50)
