@echo off
chcp 65001 >nul
echo ================================================================================
echo 正在配置基于Yolov8与ByteTrack的高速公路智慧监控平台 - ByteTrack环境...
echo 当前Python: 3.12.7 → 目标Python: 3.9.16
echo ================================================================================

echo.
echo 🔍 检查Conda是否可用...
conda --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Conda未安装，请先安装Anaconda或Miniconda
    echo 📥 下载地址: https://www.anaconda.com/products/distribution
    echo.
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('conda --version') do set CONDA_VERSION=%%i
echo ✅ Conda已安装: %CONDA_VERSION%

echo.
echo 🔍 检查ByteTrack环境是否存在...
conda env list | findstr "ByteTrack" >nul 2>&1
if not errorlevel 1 (
    echo ⚠️  ByteTrack环境已存在
    echo.
    set /p choice="是否删除现有环境并重新创建? (y/n): "
    if /i "!choice!"=="y" (
        echo 🗑️  删除现有ByteTrack环境...
        conda env remove -n ByteTrack -y
        if errorlevel 1 (
            echo ❌ 删除环境失败
            pause
            exit /b 1
        )
        echo ✅ 环境删除成功
    ) else (
        echo ℹ️  将在现有环境中更新依赖...
        goto :update_env
    )
)

echo.
echo 🏗️  创建ByteTrack环境 (Python 3.9.16)...
echo ⏳ 这可能需要几分钟时间，请耐心等待...

conda create -n ByteTrack python=3.9.16 -y
if errorlevel 1 (
    echo ❌ 环境创建失败
    echo 💡 请检查网络连接或尝试使用国内镜像
    pause
    exit /b 1
)
echo ✅ ByteTrack环境创建成功

echo.
echo 🔄 激活ByteTrack环境...
call conda activate ByteTrack
if errorlevel 1 (
    echo ❌ 环境激活失败
    pause
    exit /b 1
)

echo ✅ 环境已激活
python --version

echo.
echo 📦 安装PyTorch CUDA版本...
echo ⏳ 正在安装PyTorch 2.1.2 + CUDA 11.8...

conda install pytorch==2.1.2 torchvision==0.16.2 torchaudio==2.1.2 pytorch-cuda=11.8 -c pytorch -c nvidia -y
if errorlevel 1 (
    echo ❌ PyTorch安装失败
    echo 💡 尝试备用安装方法...
    
    REM 备用方法：使用pip安装
    pip install torch==2.1.2+cu118 torchvision==0.16.2+cu118 torchaudio==2.1.2+cu118 --index-url https://download.pytorch.org/whl/cu118
    if errorlevel 1 (
        echo ❌ 备用安装也失败
        pause
        exit /b 1
    )
)
echo ✅ PyTorch安装成功

echo.
echo 📦 安装科学计算包...
conda install numpy=1.24.3 scipy=1.10.1 matplotlib=3.7.2 pandas=2.0.3 -y
if errorlevel 1 (
    echo ⚠️  Conda安装失败，尝试pip安装...
    pip install numpy==1.24.3 scipy==1.10.1 matplotlib==3.7.2 pandas==2.0.3
)
echo ✅ 科学计算包安装完成

echo.
echo 📦 安装计算机视觉包...
pip install opencv-python==********
pip install pillow==9.5.0
pip install supervision>=0.16.0
echo ✅ 计算机视觉包安装完成

echo.
echo 📦 安装YOLO和目标检测包...
pip install ultralytics>=8.0.0
echo ✅ YOLO包安装完成

echo.
echo 📦 安装Web框架...
pip install flask==2.3.3 flask-cors==4.0.0 werkzeug>=2.3.0,^<3.0.0
echo ✅ Web框架安装完成

echo.
echo 📦 安装数据库和工具包...
pip install pymysql==1.1.0 python-dotenv==1.0.0
pip install psutil==5.9.6 tqdm requests python-dateutil
echo ✅ 基础工具包安装完成

echo.
echo 📦 安装可选包 (WebSocket, 缓存等)...
pip install flask-socketio==5.3.6 python-socketio>=5.9.0 eventlet>=0.33.3
pip install redis>=5.0.1 apscheduler>=3.10.4
pip install gunicorn>=21.2.0
echo ✅ 可选包安装完成

goto :verify_install

:update_env
echo 🔄 更新现有环境...
call conda activate ByteTrack
pip install --upgrade ultralytics supervision flask flask-cors pymysql python-dotenv

:verify_install
echo.
echo ================================================================================
echo 🧪 验证安装结果
echo ================================================================================

echo.
echo 🐍 Python版本检查...
python --version

echo.
echo 🔥 PyTorch和CUDA检查...
python -c "import torch; print(f'PyTorch版本: {torch.__version__}'); print(f'CUDA可用: {torch.cuda.is_available()}'); print(f'CUDA版本: {torch.version.cuda if torch.cuda.is_available() else \"N/A\"}')"
if errorlevel 1 (
    echo ❌ PyTorch测试失败
    goto :install_failed
)

echo.
echo 🎯 YOLO检查...
python -c "from ultralytics import YOLO; print('✅ Ultralytics YOLO导入成功')"
if errorlevel 1 (
    echo ❌ YOLO测试失败
    goto :install_failed
)

echo.
echo 🌐 Web框架检查...
python -c "import flask, pymysql; print('✅ Flask和PyMySQL导入成功')"
if errorlevel 1 (
    echo ❌ Web框架测试失败
    goto :install_failed
)

echo.
echo 👁️ 计算机视觉检查...
python -c "import cv2, numpy as np; print(f'✅ OpenCV版本: {cv2.__version__}'); print(f'✅ NumPy版本: {np.__version__}')"
if errorlevel 1 (
    echo ❌ 计算机视觉包测试失败
    goto :install_failed
)

echo.
echo ================================================================================
echo 基于Yolov8与ByteTrack的高速公路智慧监控平台 - ByteTrack环境配置完成。
echo ================================================================================

echo.
echo 📋 环境信息:
echo   🏷️  环境名称: ByteTrack
echo   🐍 Python版本: 3.9.16
echo   🔥 PyTorch: 2.1.2 + CUDA 11.8
echo   🎯 YOLO: Ultralytics 8.0+
echo   🌐 Web: Flask 2.3.3

echo.
echo 🚀 使用方法:
echo   1️⃣  激活环境: conda activate ByteTrack
echo   2️⃣  测试CUDA: python test_cuda.py
echo   3️⃣  测试数据库: python test_db.py
echo   4️⃣  启动系统: python start_server.py

echo.
echo 📁 快捷方式:
REM 创建快捷激活脚本
echo @echo off > activate_bytetrack.bat
echo title ByteTrack环境 >> activate_bytetrack.bat
echo echo 🚀 激活ByteTrack Conda环境... >> activate_bytetrack.bat
echo call conda activate ByteTrack >> activate_bytetrack.bat
echo echo. >> activate_bytetrack.bat
echo echo ✅ 环境已激活！ >> activate_bytetrack.bat
echo echo 📋 环境信息: >> activate_bytetrack.bat
echo python --version >> activate_bytetrack.bat
echo python -c "import torch; print(f'PyTorch: {torch.__version__} (CUDA: {torch.cuda.is_available()})')" >> activate_bytetrack.bat
echo echo. >> activate_bytetrack.bat
echo echo 🎯 可用命令: >> activate_bytetrack.bat
echo echo   python start_server.py    - 启动服务器 >> activate_bytetrack.bat
echo echo   python test_cuda.py       - 测试CUDA >> activate_bytetrack.bat
echo echo   python test_db.py         - 测试数据库 >> activate_bytetrack.bat
echo echo   exit                      - 退出环境 >> activate_bytetrack.bat
echo echo. >> activate_bytetrack.bat
echo cmd /k >> activate_bytetrack.bat

echo   📄 activate_bytetrack.bat - 双击激活环境

REM 创建启动脚本
echo @echo off > start_system.bat
echo title 基于Yolov8与ByteTrack的高速公路智慧监控平台 >> start_system.bat
echo call conda activate ByteTrack >> start_system.bat
echo python start_server.py >> start_system.bat
echo pause >> start_system.bat

echo   🚀 start_system.bat - 双击启动系统

echo.
echo 🎯 下一步操作:
echo   1. 配置数据库: 编辑 config/end-back.env
echo   2. 导入数据库: mysql -u root -p yolo ^< yolo.sql
echo   3. 双击 activate_bytetrack.bat 激活环境
echo   4. 运行 python test_cuda.py 测试CUDA
echo   5. 运行 python start_server.py 启动系统

echo.
echo ✨ 安装完成！按任意键退出...
pause >nul
exit /b 0

:install_failed
echo.
echo ================================================================================
echo ❌ 安装验证失败
echo ================================================================================
echo.
echo 🔧 可能的解决方案:
echo   1. 检查网络连接
echo   2. 重新运行此脚本
echo   3. 手动执行 conda_install_commands.txt 中的命令
echo   4. 联系技术支持
echo.
echo 📋 环境信息 (用于故障排除):
conda list | findstr "torch\|numpy\|opencv\|ultralytics\|flask"
echo.
pause
exit /b 1
