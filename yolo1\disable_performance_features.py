#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
禁用yolo1项目中影响性能的功能
恢复基础检测性能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def disable_performance_features():
    """禁用影响性能的功能"""
    print("=" * 60)
    print("禁用yolo1项目中影响性能的功能")
    print("=" * 60)
    
    # 修改classes/yolo.py文件
    yolo_file = "classes/yolo.py"
    
    try:
        with open(yolo_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 备份原文件
        backup_file = yolo_file + ".backup"
        with open(backup_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✓ 已备份原文件到: {backup_file}")
        
        # 禁用性能消耗功能
        modifications = [
            ("self.show_speed = True", "self.show_speed = False"),
            ("self.violation_detection_enabled = True", "self.violation_detection_enabled = False"),
            ("self.multi_tracking = True", "self.multi_tracking = False")
        ]
        
        modified = False
        for old_text, new_text in modifications:
            if old_text in content:
                content = content.replace(old_text, new_text)
                modified = True
                print(f"✓ 修改: {old_text} -> {new_text}")
        
        if modified:
            with open(yolo_file, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✓ 已更新文件: {yolo_file}")
        else:
            print("! 未找到需要修改的内容")
            
    except Exception as e:
        print(f"✗ 修改{yolo_file}时出错: {str(e)}")
        return False
    
    # 修改main.py文件
    main_file = "main.py"
    
    try:
        with open(main_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 备份原文件
        backup_file = main_file + ".backup"
        with open(backup_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✓ 已备份原文件到: {backup_file}")
        
        # 禁用性能消耗功能
        modifications = [
            ("self.yolo_predict.multi_tracking = True", "self.yolo_predict.multi_tracking = False"),
            ("self.yolo_predict.violation_detection_enabled = True", "self.yolo_predict.violation_detection_enabled = False"),
            ("self.yolo_predict.show_speed = True", "self.yolo_predict.show_speed = False")
        ]
        
        modified = False
        for old_text, new_text in modifications:
            if old_text in content:
                content = content.replace(old_text, new_text)
                modified = True
                print(f"✓ 修改: {old_text} -> {new_text}")
        
        if modified:
            with open(main_file, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✓ 已更新文件: {main_file}")
        else:
            print("! 未找到需要修改的内容")
            
    except Exception as e:
        print(f"✗ 修改{main_file}时出错: {str(e)}")
        return False
    
    print("\n" + "=" * 60)
    print("性能优化完成！")
    print("已禁用以下功能:")
    print("- 速度估计 (show_speed)")
    print("- 违规检测 (violation_detection_enabled)")
    print("- 多目标追踪 (multi_tracking)")
    print("\n这些功能在每帧检测时都会执行复杂计算，")
    print("禁用后应该能显著提升检测性能。")
    print("=" * 60)
    
    return True

def test_performance_after_disable():
    """测试禁用功能后的性能"""
    print("\n开始性能测试...")
    
    try:
        import torch
        import cv2
        import numpy as np
        import time
        from ultralytics import YOLO
        import supervision as sv
        
        # 检查CUDA
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        print(f"使用设备: {device}")
        
        if device == 'cuda':
            print(f"GPU: {torch.cuda.get_device_name(0)}")
        
        # 加载模型
        print("加载YOLO模型...")
        model = YOLO('yolov8n.pt')
        model.to(device)
        
        # 创建测试图像
        test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
        
        # GPU预热
        print("GPU预热...")
        for _ in range(5):
            result = model(test_image, device=device, verbose=False)
            detections = sv.Detections.from_yolov8(result[0])
        
        # 性能测试
        print("执行性能测试...")
        num_tests = 50
        times = []
        
        for i in range(num_tests):
            start_time = time.time()
            result = model(test_image, device=device, verbose=False)
            detections = sv.Detections.from_yolov8(result[0])
            end_time = time.time()
            times.append(end_time - start_time)
            
            if (i + 1) % 10 == 0:
                print(f"完成 {i + 1}/{num_tests} 次测试")
        
        # 计算统计信息
        avg_time = np.mean(times)
        fps = 1.0 / avg_time
        
        print(f"\n性能测试结果:")
        print(f"平均耗时: {avg_time:.4f}秒")
        print(f"平均FPS: {fps:.2f}")
        print(f"设备: {device}")
        
        if avg_time < 0.1:  # 小于100ms认为性能正常
            print("✓ 性能正常！")
            return True
        else:
            print("⚠ 性能仍然较慢，可能还有其他问题")
            return False
            
    except Exception as e:
        print(f"✗ 性能测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    # 禁用性能消耗功能
    success = disable_performance_features()
    
    if success:
        # 测试性能
        test_performance_after_disable()
    else:
        print("功能禁用失败，跳过性能测试")