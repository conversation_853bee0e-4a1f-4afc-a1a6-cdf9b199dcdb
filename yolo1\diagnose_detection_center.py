#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断检测中心功能 - YOLO检测
"""

import requests
import json
import time

def test_detection_center_functions():
    """测试检测中心的四大功能"""
    base_url = "http://127.0.0.1:5500"
    
    print("🔐 获取token...")
    
    # 登录
    login_data = {"username": "admin", "password": "123456"}
    try:
        response = requests.post(f"{base_url}/api/v1/auth/login", json=login_data, timeout=5)
        if response.status_code != 200:
            print(f"❌ 登录失败: {response.status_code}")
            return
        
        login_result = response.json()
        if not login_result.get('success'):
            print(f"❌ 登录失败: {login_result.get('message')}")
            return
        
        token = login_result['data']['token']
        headers = {"Authorization": f"Bearer {token}"}
        print("✅ 登录成功")
        
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return
    
    # 检测中心四大功能对应的API
    detection_functions = [
        # 1. 图像检测
        {
            "name": "图像检测",
            "apis": [
                ("/api/v1/detection/upload", "文件上传接口"),
                ("/api/v1/detection/image", "图像检测接口"),
                ("/api/v1/detection/config", "检测配置"),
                ("/api/v1/detection/models", "检测模型列表")
            ]
        },
        # 2. 视频检测  
        {
            "name": "视频检测",
            "apis": [
                ("/api/v1/detection/video", "视频检测接口"),
                ("/api/v1/detection/upload", "视频上传接口"),
                ("/api/v1/detection/progress", "检测进度查询")
            ]
        },
        # 3. RTSP检测
        {
            "name": "RTSP检测", 
            "apis": [
                ("/api/v1/detection/rtsp", "RTSP流检测"),
                ("/api/v1/detection/stream/start", "启动流检测"),
                ("/api/v1/detection/stream/stop", "停止流检测"),
                ("/api/v1/detection/stream/status", "流检测状态")
            ]
        },
        # 4. 任务管理
        {
            "name": "任务管理",
            "apis": [
                ("/api/v1/detection/tasks", "检测任务列表"),
                ("/api/v1/detection/tasks/status", "任务状态查询"),
                ("/api/v1/detection/tasks/results", "任务结果查询"),
                ("/api/v1/detection/tasks/delete", "删除任务")
            ]
        }
    ]
    
    print(f"\n🧪 测试检测中心四大功能...")
    
    all_results = {}
    
    for function in detection_functions:
        function_name = function["name"]
        apis = function["apis"]
        
        print(f"\n📋 测试 {function_name} 功能:")
        
        function_results = {
            "success_count": 0,
            "total_count": len(apis),
            "errors": [],
            "slow_apis": []
        }
        
        for endpoint, api_name in apis:
            try:
                start_time = time.time()
                response = requests.get(f"{base_url}{endpoint}", headers=headers, timeout=10)
                end_time = time.time()
                
                duration = end_time - start_time
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        print(f"   ✅ {api_name}: {duration:.3f}秒")
                        function_results["success_count"] += 1
                        if duration > 2.0:
                            function_results["slow_apis"].append((api_name, duration))
                    else:
                        print(f"   ❌ {api_name}: API失败 - {result.get('message')}")
                        function_results["errors"].append((api_name, result.get('message')))
                elif response.status_code == 404:
                    print(f"   ❌ {api_name}: 接口不存在 (404)")
                    function_results["errors"].append((api_name, "接口不存在"))
                elif response.status_code == 405:
                    print(f"   ⚠️ {api_name}: 方法不允许 (405)")
                    function_results["errors"].append((api_name, "方法不允许"))
                else:
                    print(f"   ❌ {api_name}: HTTP {response.status_code}")
                    function_results["errors"].append((api_name, f"HTTP {response.status_code}"))
                    
            except requests.exceptions.Timeout:
                print(f"   ⏱️ {api_name}: 请求超时")
                function_results["errors"].append((api_name, "请求超时"))
            except Exception as e:
                print(f"   ❌ {api_name}: 异常 - {e}")
                function_results["errors"].append((api_name, str(e)))
        
        all_results[function_name] = function_results
    
    # 生成详细报告
    print(f"\n" + "=" * 80)
    print(f"📊 检测中心功能测试报告")
    print(f"=" * 80)
    
    total_success = 0
    total_apis = 0
    critical_missing = []
    
    for function_name, results in all_results.items():
        success_rate = (results["success_count"] / results["total_count"]) * 100
        total_success += results["success_count"]
        total_apis += results["total_count"]
        
        print(f"\n🎯 {function_name}:")
        print(f"   成功率: {success_rate:.1f}% ({results['success_count']}/{results['total_count']})")
        
        if results["errors"]:
            print(f"   ❌ 失败的API:")
            for api_name, error in results["errors"]:
                print(f"     - {api_name}: {error}")
                if "不存在" in error:
                    critical_missing.append(f"{function_name} - {api_name}")
        
        if results["slow_apis"]:
            print(f"   ⚠️ 慢响应API:")
            for api_name, duration in results["slow_apis"]:
                print(f"     - {api_name}: {duration:.3f}秒")
    
    overall_success_rate = (total_success / total_apis) * 100
    print(f"\n📈 总体成功率: {overall_success_rate:.1f}% ({total_success}/{total_apis})")
    
    if critical_missing:
        print(f"\n🚨 关键缺失API (可能导致页面无法跳转):")
        for missing in critical_missing:
            print(f"   - {missing}")
    
    return all_results, critical_missing

def test_detection_tasks_detail():
    """详细测试检测任务API"""
    base_url = "http://127.0.0.1:5500"
    
    # 登录
    login_data = {"username": "admin", "password": "123456"}
    response = requests.post(f"{base_url}/api/v1/auth/login", json=login_data)
    token = response.json()['data']['token']
    headers = {"Authorization": f"Bearer {token}"}
    
    print(f"\n🔍 详细检查检测任务API...")
    
    # 测试检测任务列表
    response = requests.get(f"{base_url}/api/v1/detection/tasks?page=1&size=10", headers=headers)
    
    if response.status_code == 200:
        result = response.json()
        print(f"检测任务API响应:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        if result.get('success') and result.get('data'):
            data = result['data']
            if 'tasks' in data:
                tasks = data['tasks']
                print(f"\n✅ 检测任务数据正常，共 {len(tasks)} 个任务")
                if tasks:
                    print(f"任务字段: {list(tasks[0].keys())}")
                    print(f"任务类型: {set(task.get('task_type', 'unknown') for task in tasks)}")
            else:
                print(f"\n❌ 检测任务数据格式异常")
        else:
            print(f"\n❌ 检测任务API返回失败")
    else:
        print(f"❌ 检测任务API请求失败: {response.status_code}")

def main():
    """主函数"""
    print("=" * 80)
    print("🚀 检测中心功能诊断 - YOLO检测")
    print("=" * 80)
    
    # 1. 测试四大功能
    all_results, critical_missing = test_detection_center_functions()
    
    # 2. 详细测试检测任务
    test_detection_tasks_detail()
    
    print("\n" + "=" * 80)
    print("💡 检测中心导航问题分析:")
    
    if critical_missing:
        print("🔴 发现关键API缺失，这很可能是导航无法跳转的原因!")
        print("   前端尝试加载检测中心时，请求了不存在的API")
        print("   导致JavaScript错误，阻塞了页面渲染和导航功能")
        
        print(f"\n🔧 需要立即添加的API:")
        for missing in critical_missing:
            print(f"   - {missing}")
    else:
        print("✅ 所有关键API都存在，问题可能在:")
        print("   1. API响应格式与前端期望不匹配")
        print("   2. API响应太慢，前端等待超时")
        print("   3. 前端JavaScript错误")
        print("   4. 浏览器缓存问题")
    
    print("\n🔧 解决步骤:")
    print("1. 如果有缺失API，立即添加到后端")
    print("2. 检查浏览器控制台的JavaScript错误")
    print("3. 清除浏览器缓存并重新登录")
    print("4. 检查Network标签的API请求状态")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
