# -*- coding: utf-8 -*-
# @Description : 基于Yolov8与ByteTrack的高速公路智慧监控平台 - API模块初始化
# @Date : 2025年6月20日

from flask import Blueprint

# 创建API蓝图
api_v1 = Blueprint('api_v1', __name__, url_prefix='/api/v1')

# 导入所有API路由
from . import auth
from . import monitor
from . import detection
from . import analysis
from . import system
from . import realtime
from . import violation
from . import accident
from . import tracking
from . import config