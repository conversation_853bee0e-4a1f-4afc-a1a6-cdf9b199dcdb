# -*- coding: utf-8 -*-
# 性能测试脚本

import time
import cv2
import numpy as np
from ultralytics import YOLO
import supervision as sv

def test_supervision_performance():
    """测试supervision库性能"""
    print("🔍 测试Supervision库性能")
    print("="*50)
    
    # 测试BoxAnnotator创建
    start = time.time()
    box_annotator = sv.BoxAnnotator(thickness=2)
    print(f"BoxAnnotator创建耗时: {time.time() - start:.4f}s")
    
    # 创建测试图像
    test_image = np.zeros((640, 640, 3), dtype=np.uint8)
    
    # 创建测试检测结果
    test_boxes = np.array([[100, 100, 200, 200], [300, 300, 400, 400]])
    test_confidences = np.array([0.9, 0.8])
    test_class_ids = np.array([0, 1])
    test_tracker_ids = np.array([1, 2])
    
    test_detections = sv.Detections(
        xyxy=test_boxes,
        confidence=test_confidences,
        class_id=test_class_ids,
        tracker_id=test_tracker_ids
    )
    
    # 测试绘制性能
    start = time.time()
    for i in range(100):  # 测试100次
        result_image = box_annotator.annotate(scene=test_image.copy(), detections=test_detections)
    avg_time = (time.time() - start) / 100
    print(f"BoxAnnotator绘制平均耗时: {avg_time:.4f}s")
    print(f"预估FPS: {1/avg_time:.1f}")
    
    return avg_time

def test_opencv_performance():
    """测试OpenCV直接绘制性能"""
    print("\n🔍 测试OpenCV直接绘制性能")
    print("="*50)
    
    # 创建测试图像
    test_image = np.zeros((640, 640, 3), dtype=np.uint8)
    
    # 测试数据
    test_boxes = np.array([[100, 100, 200, 200], [300, 300, 400, 400]])
    test_labels = ["car 0.9", "truck 0.8"]
    
    # 测试绘制性能
    start = time.time()
    for i in range(100):  # 测试100次
        img = test_image.copy()
        for j, (box, label) in enumerate(zip(test_boxes, test_labels)):
            x1, y1, x2, y2 = box
            # 绘制边界框
            cv2.rectangle(img, (x1, y1), (x2, y2), (0, 255, 0), 2)
            # 绘制标签
            cv2.putText(img, label, (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
    
    avg_time = (time.time() - start) / 100
    print(f"OpenCV绘制平均耗时: {avg_time:.4f}s")
    print(f"预估FPS: {1/avg_time:.1f}")
    
    return avg_time

def test_yolo_loading():
    """测试YOLO模型加载性能"""
    print("\n🔍 测试YOLO模型加载性能")
    print("="*50)
    
    start = time.time()
    model = YOLO("./models/car.pt")
    load_time = time.time() - start
    print(f"YOLO模型加载耗时: {load_time:.2f}s")
    
    # 测试推理性能
    test_image = np.zeros((640, 640, 3), dtype=np.uint8)
    
    start = time.time()
    for i in range(10):  # 测试10次推理
        results = model(test_image, verbose=False)
    avg_inference_time = (time.time() - start) / 10
    print(f"YOLO推理平均耗时: {avg_inference_time:.4f}s")
    print(f"推理FPS: {1/avg_inference_time:.1f}")
    
    return load_time, avg_inference_time

if __name__ == "__main__":
    print("🚀 高速公路监控系统性能测试")
    print("="*60)
    
    # 测试supervision性能
    sv_time = test_supervision_performance()
    
    # 测试OpenCV性能
    cv_time = test_opencv_performance()
    
    # 性能对比
    print(f"\n📊 性能对比:")
    print(f"Supervision绘制: {sv_time:.4f}s")
    print(f"OpenCV绘制: {cv_time:.4f}s")
    print(f"性能提升: {sv_time/cv_time:.1f}x")
    
    # 测试YOLO性能
    try:
        load_time, inference_time = test_yolo_loading()
        print(f"\n🎯 YOLO性能:")
        print(f"模型加载: {load_time:.2f}s")
        print(f"推理耗时: {inference_time:.4f}s")
    except Exception as e:
        print(f"YOLO测试失败: {e}")
    
    print("\n✅ 性能测试完成")
