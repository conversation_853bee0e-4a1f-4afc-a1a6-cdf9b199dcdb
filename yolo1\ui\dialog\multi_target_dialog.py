# -*- coding: utf-8 -*-
# 简化的多目标追踪对话框实现 - 确保可以显示

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                             QFrame, QGridLayout, QWidget, QSlider, QCheckBox)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QPixmap, QImage, QColor
import random
import cv2
import numpy as np

class SimpleMultiTargetDialog(QDialog):
    """简化版多目标追踪对话框 - 确保基本功能可用"""
    
    def __init__(self, parent=None):
        super(SimpleMultiTargetDialog, self).__init__(parent)
        print("创建简化版多目标追踪对话框")
        
        # 设置窗口标题和大小
        self.setWindowTitle("多目标追踪")
        self.resize(800, 600)
        
        # 创建布局
        self.main_layout = QVBoxLayout(self)
        
        # 创建标题
        self.title_label = QLabel("多目标追踪系统")
        self.title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #00aaff;")
        self.title_label.setAlignment(Qt.AlignCenter)
        self.main_layout.addWidget(self.title_label)
        
        # 主内容区域
        self.content_frame = QFrame()
        self.content_frame.setStyleSheet("background-color: #2c3e50; border-radius: 10px;")
        self.content_layout = QVBoxLayout(self.content_frame)
        
        # 视频显示区
        self.video_label = QLabel()
        self.video_label.setStyleSheet("background-color: #1c2630; min-height: 300px;")
        self.video_label.setAlignment(Qt.AlignCenter)
        self.video_label.setText("视频显示区")
        self.content_layout.addWidget(self.video_label)
        
        # 控制区域
        self.control_widget = QWidget()
        self.control_layout = QHBoxLayout(self.control_widget)
        
        # 创建控制选项
        self.control_grid = QGridLayout()
        
        # 置信度滑块
        self.conf_label = QLabel("置信度:")
        self.conf_label.setStyleSheet("color: #bdc3c7;")
        self.conf_slider = QSlider(Qt.Horizontal)
        self.conf_slider.setMinimum(1)
        self.conf_slider.setMaximum(10)
        self.conf_slider.setValue(4)
        self.conf_value = QLabel("0.4")
        self.conf_value.setStyleSheet("color: #3498db;")
        self.control_grid.addWidget(self.conf_label, 0, 0)
        self.control_grid.addWidget(self.conf_slider, 0, 1)
        self.control_grid.addWidget(self.conf_value, 0, 2)
        
        # IOU滑块
        self.iou_label = QLabel("IOU阈值:")
        self.iou_label.setStyleSheet("color: #bdc3c7;")
        self.iou_slider = QSlider(Qt.Horizontal)
        self.iou_slider.setMinimum(1)
        self.iou_slider.setMaximum(10)
        self.iou_slider.setValue(5)
        self.iou_value = QLabel("0.5")
        self.iou_value.setStyleSheet("color: #3498db;")
        self.control_grid.addWidget(self.iou_label, 1, 0)
        self.control_grid.addWidget(self.iou_slider, 1, 1)
        self.control_grid.addWidget(self.iou_value, 1, 2)
        
        # 显示选项
        self.show_layout = QHBoxLayout()
        self.labels_check = QCheckBox("显示标签")
        self.labels_check.setChecked(True)
        self.labels_check.setStyleSheet("color: #bdc3c7;")
        self.trails_check = QCheckBox("显示轨迹")
        self.trails_check.setChecked(True)
        self.trails_check.setStyleSheet("color: #bdc3c7;")
        self.show_layout.addWidget(self.labels_check)
        self.show_layout.addWidget(self.trails_check)
        
        # 将控制选项添加到布局
        self.control_layout.addLayout(self.control_grid)
        self.control_layout.addLayout(self.show_layout)
        self.content_layout.addWidget(self.control_widget)
        
        # 按钮区域
        self.button_layout = QHBoxLayout()
        
        # 开始按钮
        self.start_button = QPushButton("开始追踪")
        self.start_button.setStyleSheet("""
            QPushButton {
                background-color: #2980b9;
                color: white;
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #3498db;
            }
        """)
        
        # 停止按钮
        self.stop_button = QPushButton("停止追踪")
        self.stop_button.setStyleSheet("""
            QPushButton {
                background-color: #c0392b;
                color: white;
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e74c3c;
            }
        """)
        self.stop_button.setEnabled(False)
        
        # 添加按钮到布局
        self.button_layout.addStretch()
        self.button_layout.addWidget(self.start_button)
        self.button_layout.addWidget(self.stop_button)
        self.button_layout.addStretch()
        
        self.content_layout.addLayout(self.button_layout)
        
        # 状态栏
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("color: #2ecc71; padding: 5px;")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.content_layout.addWidget(self.status_label)
        
        # 添加内容区到主布局
        self.main_layout.addWidget(self.content_frame)
        
        # 连接信号
        self.conf_slider.valueChanged.connect(self.update_conf_value)
        self.iou_slider.valueChanged.connect(self.update_iou_value)
        self.start_button.clicked.connect(self.start_tracking)
        self.stop_button.clicked.connect(self.stop_tracking)
        
        # 定时器用于模拟追踪结果
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_mock_data)
        
        print("简化版多目标追踪对话框初始化完成")
    
    def update_conf_value(self, value):
        self.conf_value.setText(f"{value/10:.1f}")
    
    def update_iou_value(self, value):
        self.iou_value.setText(f"{value/10:.1f}")
    
    def start_tracking(self):
        self.status_label.setText("追踪中...")
        self.status_label.setStyleSheet("color: #3498db; padding: 5px;")
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.timer.start(1000)  # 每秒更新一次
    
    def stop_tracking(self):
        self.status_label.setText("已停止")
        self.status_label.setStyleSheet("color: #e74c3c; padding: 5px;")
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.timer.stop()
    
    def update_mock_data(self):
        """更新模拟数据"""
        # 创建模拟视频帧
        frame = np.zeros((480, 640, 3), dtype=np.uint8)
        frame.fill(30)  # 暗灰色背景
        
        # 绘制边框和追踪ID
        num_boxes = random.randint(3, 8)
        for i in range(num_boxes):
            # 随机位置和大小
            x = random.randint(50, 550)
            y = random.randint(50, 380)
            w = random.randint(40, 100)
            h = random.randint(40, 100)
            
            # 随机颜色
            color = (random.randint(0, 255), random.randint(0, 255), random.randint(0, 255))
            
            # 绘制边框
            cv2.rectangle(frame, (x, y), (x + w, y + h), color, 2)
            
            # 如果显示标签，则添加ID和类别
            if self.labels_check.isChecked():
                id = random.randint(1, 100)
                classes = ["汽车", "卡车", "公交车", "摩托车"]
                label = f"ID:{id} {random.choice(classes)}"
                cv2.putText(frame, label, (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
            
            # 如果显示轨迹，则添加轨迹线
            if self.trails_check.isChecked():
                points = []
                for j in range(5):
                    points.append((x + j * 5, y + random.randint(-20, 20)))
                
                for j in range(len(points) - 1):
                    cv2.line(frame, points[j], points[j + 1], color, 2)
        
        # 转换为QImage并显示
        height, width, channel = frame.shape
        bytes_per_line = 3 * width
        q_img = QImage(frame.data, width, height, bytes_per_line, QImage.Format_RGB888)
        self.video_label.setPixmap(QPixmap.fromImage(q_img))
