# -*- coding: utf-8 -*-
# @Description : 数据库连接工具
# @Date : 2025年6月20日

import os
import time
import pymysql
from contextlib import contextmanager
from typing import Dict, List, Any, Optional
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(override=True, dotenv_path='config/end-back.env')

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.config = {
            'host': os.getenv('MYSQL_HOST', '127.0.0.1'),
            'port': int(os.getenv('MYSQL_PORT', 3306)),
            'user': os.getenv('MYSQL_USER', os.getenv('MYSQL_user', 'root')),
            'password': os.getenv('MYSQL_PASSWORD', os.getenv('MYSQL_password', '123456')),
            'database': os.getenv('MYSQL_DATABASE', os.getenv('MYSQL_db', 'yolo')),
            'charset': os.getenv('MYSQL_CHARSET', os.getenv('MYSQL_charset', 'utf8mb4')),
            'autocommit': True,
            'cursorclass': pymysql.cursors.DictCursor
        }
    
    def get_connection(self):
        """获取数据库连接"""
        try:
            connection = pymysql.connect(**self.config)
            return connection
        except Exception as e:
            raise Exception(f"数据库连接失败: {str(e)}")
    
    def test_connection(self) -> bool:
        """测试数据库连接"""
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    return True
        except:
            return False

class DatabaseConnection:
    """数据库连接上下文管理器"""
    
    def __init__(self, connection):
        if connection is None:
            raise Exception("数据库连接对象不能为None")
        self.connection = connection
        self.cursor = connection.cursor()  # 立即创建cursor
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
    
    def execute(self, sql: str, params: tuple = None) -> int:
        """执行SQL语句"""
        try:
            return self.cursor.execute(sql, params)
        except Exception as e:
            self.connection.rollback()
            raise Exception(f"SQL执行失败: {str(e)}")
    
    def fetchone(self) -> Optional[Dict]:
        """获取单条记录"""
        return self.cursor.fetchone()
    
    def fetchall(self) -> List[Dict]:
        """获取所有记录"""
        return self.cursor.fetchall()
    
    def fetchmany(self, size: int) -> List[Dict]:
        """获取指定数量记录"""
        return self.cursor.fetchmany(size)
    
    def get_one(self, sql: str, params: tuple = None) -> Optional[Dict]:
        """查询单条记录"""
        self.execute(sql, params)
        return self.fetchone()
    
    def get_list(self, sql: str, params: tuple = None) -> List[Dict]:
        """查询多条记录"""
        self.execute(sql, params)
        return self.fetchall()
    
    def modify(self, sql: str, params: tuple = None) -> int:
        """执行修改操作（UPDATE/DELETE）"""
        affected_rows = self.execute(sql, params)
        self.connection.commit()
        return affected_rows
    
    def create(self, sql: str, params: tuple = None) -> int:
        """执行创建操作（INSERT）"""
        self.execute(sql, params)
        self.connection.commit()
        return self.cursor.lastrowid
    
    def multi_modify(self, sql: str, params_list: List[tuple]) -> int:
        """批量执行修改操作"""
        try:
            affected_rows = self.cursor.executemany(sql, params_list)
            self.connection.commit()
            return affected_rows
        except Exception as e:
            self.connection.rollback()
            raise Exception(f"批量操作失败: {str(e)}")
    
    def begin_transaction(self):
        """开始事务"""
        self.connection.begin()
    
    def commit(self):
        """提交事务"""
        self.connection.commit()
    
    def rollback(self):
        """回滚事务"""
        self.connection.rollback()

# 全局数据库管理器实例
db_manager = DatabaseManager()

@contextmanager
def get_db_connection():
    """获取数据库连接上下文管理器 - 修复版"""
    import pymysql

    # 使用直接数据库连接（完全绕过有问题的包装器）
    config = {
        'host': '127.0.0.1',
        'port': 3306,
        'user': 'root',
        'password': '123456',
        'database': 'yolo',
        'charset': 'utf8mb4',
        'autocommit': True,
        'cursorclass': pymysql.cursors.DictCursor
    }

    connection = None
    try:
        connection = pymysql.connect(**config)
        # 创建简化的数据库连接包装器
        db_wrapper = SimpleDatabaseConnection(connection)
        yield db_wrapper

    except Exception as e:
        raise Exception(f"数据库连接错误: {str(e)}")
    finally:
        if connection:
            try:
                connection.close()
            except:
                pass

class SimpleDatabaseConnection:
    """简化的数据库连接包装器"""

    def __init__(self, connection):
        self.connection = connection
        self.cursor = connection.cursor()

    def execute(self, sql, params=None):
        """执行SQL"""
        return self.cursor.execute(sql, params)

    def fetchone(self):
        """获取单条记录"""
        return self.cursor.fetchone()

    def fetchall(self):
        """获取所有记录"""
        return self.cursor.fetchall()

    def get_one(self, sql, params=None):
        """查询单条记录"""
        self.cursor.execute(sql, params)
        return self.cursor.fetchone()

    def get_list(self, sql, params=None):
        """查询多条记录"""
        self.cursor.execute(sql, params)
        return self.cursor.fetchall()

    def create(self, sql, params=None):
        """插入数据"""
        self.cursor.execute(sql, params)
        self.connection.commit()
        return self.cursor.lastrowid

    def modify(self, sql, params=None):
        """修改数据"""
        self.cursor.execute(sql, params)
        self.connection.commit()
        return self.cursor.rowcount

def init_database():
    """初始化数据库"""
    try:
        print("开始初始化数据库连接...")
        # 使用直接数据库连接（避免get_db_connection问题）
        import pymysql
        config = {
            'host': '127.0.0.1',
            'port': 3306,
            'user': 'root',
            'password': '123456',
            'database': 'yolo',
            'charset': 'utf8mb4',
            'autocommit': True,
            'cursorclass': pymysql.cursors.DictCursor
        }

        connection = pymysql.connect(**config)
        with connection.cursor() as cursor:
            # 检查数据库连接
            cursor.execute("SELECT 1")
            print("✅ 数据库连接成功")

            # 检查所有数据库表
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            # SHOW TABLES 返回的字段名格式为 'Tables_in_数据库名'
            table_key = 'Tables_in_yolo'
            table_names = [table[table_key] for table in tables]

            print(f"✅ 数据库表总数: {len(table_names)}")
            print(f"✅ 表列表: {', '.join(table_names)}")

            # 检查核心表是否存在
            required_tables = ['user', 'monitor', 'alarm', 'detection_task', 'tracking_target', 'accident_record']
            missing_tables = []
            for table in required_tables:
                if table in table_names:
                    print(f"✅ 核心表 {table} 存在")
                else:
                    print(f"⚠️ 警告: 核心表 {table} 不存在")
                    missing_tables.append(table)

            if missing_tables:
                print(f"⚠️ 缺少核心表: {', '.join(missing_tables)}")
            else:
                print("✅ 所有核心表都存在")

        connection.close()
        return True
    except Exception as e:
        print(f"❌ 数据库连接测试失败: {str(e)}")
        return False

def execute_sql_file(file_path: str) -> bool:
    """执行SQL文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            sql_content = file.read()
        
        # 分割SQL语句
        sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
        
        with get_db_connection() as db:
            for sql in sql_statements:
                if sql:
                    db.execute(sql)
            db.commit()
        
        print(f"SQL文件 {file_path} 执行成功")
        return True
    except Exception as e:
        print(f"执行SQL文件失败: {str(e)}")
        return False

def backup_database(backup_path: str) -> bool:
    """备份数据库"""
    try:
        import subprocess
        
        config = db_manager.config
        cmd = [
            'mysqldump',
            f'--host={config["host"]}',
            f'--port={config["port"]}',
            f'--user={config["user"]}',
            f'--password={config["password"]}',
            '--single-transaction',
            '--routines',
            '--triggers',
            config['database']
        ]
        
        with open(backup_path, 'w') as backup_file:
            subprocess.run(cmd, stdout=backup_file, check=True)
        
        print(f"数据库备份成功: {backup_path}")
        return True
    except Exception as e:
        print(f"数据库备份失败: {str(e)}")
        return False

def restore_database(backup_path: str) -> bool:
    """恢复数据库"""
    try:
        import subprocess
        
        config = db_manager.config
        cmd = [
            'mysql',
            f'--host={config["host"]}',
            f'--port={config["port"]}',
            f'--user={config["user"]}',
            f'--password={config["password"]}',
            config['database']
        ]
        
        with open(backup_path, 'r') as backup_file:
            subprocess.run(cmd, stdin=backup_file, check=True)
        
        print(f"数据库恢复成功: {backup_path}")
        return True
    except Exception as e:
        print(f"数据库恢复失败: {str(e)}")
        return False

# 数据库健康检查
def health_check() -> Dict[str, Any]:
    """数据库健康检查"""
    try:
        # 使用直接数据库连接（避免get_db_connection问题）
        import pymysql
        config = {
            'host': '127.0.0.1',
            'port': 3306,
            'user': 'root',
            'password': '123456',
            'database': 'yolo',
            'charset': 'utf8mb4',
            'autocommit': True,
            'cursorclass': pymysql.cursors.DictCursor
        }

        connection = pymysql.connect(**config)
        start_time = time.time()

        with connection.cursor() as cursor:
            # 检查连接
            cursor.execute("SELECT 1")
            response_time = (time.time() - start_time) * 1000

            # 检查表状态
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()

            # 检查连接数
            cursor.execute("SHOW STATUS LIKE 'Threads_connected'")
            connections = cursor.fetchone()

        connection.close()

        return {
            'status': 'healthy',
            'response_time_ms': round(response_time, 2),
            'tables_count': len(tables),
            'connections': int(connections['Value']) if connections else 0
        }
    except Exception as e:
        return {
            'status': 'unhealthy',
            'error': str(e)
        }


