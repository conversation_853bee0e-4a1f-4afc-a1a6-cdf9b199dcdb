# -*- coding: utf-8 -*-
# <AUTHOR> 优化版多目标智能追踪控制台 - 统一UI风格
import sys
import time
import random

from PySide6.QtCore import Qt, QSize, Signal, QTimer, QPropertyAnimation, QEasingCurve
from PySide6.QtWidgets import (QApplication, QDialog, QFrame, QHBoxLayout, QVBoxLayout, QLabel, QPushButton,
                             QLineEdit, QListWidget, QListWidgetItem, QGridLayout, QScrollArea, QGroupBox,
                             QTableWidget, QTableWidgetItem, QHeaderView, QProgressBar, QWidget)
from PySide6.QtGui import QPixmap, QColor, QIcon, QImage
import numpy as np
import cv2

class OptimizedMultiTrackingDialog(QDialog):
    """
    优化版多目标智能追踪控制台
    - 统一UI风格与主系统保持一致
    - 智能目标预览分析区域
    - 科技感视觉增强
    - 实时数据同步显示
    """

    # 信号定义
    tracking_started = Signal(list)  # 开始追踪信号，传递目标ID列表
    tracking_stopped = Signal()     # 停止追踪信号
    target_added = Signal(int)      # 添加目标信号
    target_removed = Signal(int)    # 移除目标信号

    def __init__(self, parent=None):
        super(OptimizedMultiTrackingDialog, self).__init__(parent)

        # 初始化数据
        self.tracked_targets = []
        self.target_previews = {}
        self.is_tracking = False
        self.tracking_time = 0

        # 设置窗口属性
        self.setWindowTitle("多目标智能追踪控制台")
        self.setMinimumSize(1200, 750)
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint | Qt.WindowMinimizeButtonHint)
        self.setWindowIcon(QIcon("./ui/img/tracking.png"))

        # 使用白色背景配合蓝色透明感的现代化风格
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x0:0, y0:0, x1:1, y1:1,
                    stop:0 rgba(255, 255, 255, 255), stop:1 rgba(240, 248, 255, 255));
                border: 2px solid rgba(0, 150, 255, 0.5);
                border-radius: 15px;
            }
        """)

        # 初始化定时器
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_tracking_info)

        # 定时器用于刷新可用目标列表
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_available_targets)
        self.refresh_timer.start(3000)  # 每3秒刷新一次

        # 设置UI
        self.setup_ui()

        # 连接信号
        self.connect_signals()

        # 加载可用目标
        self.load_available_targets()

    def setup_ui(self):
        """设置用户界面"""
        # 主布局
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(8, 8, 8, 8)
        self.main_layout.setSpacing(0)

        # 创建标题栏
        self.create_title_bar()

        # 创建主内容区域
        self.create_main_content()

        # 创建状态栏
        self.create_status_bar()

    def create_title_bar(self):
        """创建标题栏"""
        self.title_bar = QFrame()
        self.title_bar.setFixedHeight(70)
        self.title_bar.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(0, 80, 150, 180), stop:1 rgba(0, 150, 220, 200));
                border-top-left-radius: 15px;
                border-top-right-radius: 15px;
                border-bottom: 2px solid rgba(0, 200, 255, 0.8);
            }
        """)

        title_layout = QHBoxLayout(self.title_bar)
        title_layout.setContentsMargins(20, 0, 20, 0)

        # 标题图标和文字
        title_icon = QLabel()
        title_icon.setPixmap(QPixmap("./ui/img/tracking.png").scaled(40, 40, Qt.KeepAspectRatio, Qt.SmoothTransformation))
        title_layout.addWidget(title_icon)

        title_label = QLabel("多目标智能追踪控制台 | Multi-Target Tracking System")
        title_label.setStyleSheet("""
            color: white;
            font-size: 20px;
            font-weight: bold;
            font-family: 'Microsoft YaHei';
        """)
        title_layout.addWidget(title_label)

        title_layout.addStretch()

        # 系统状态指示器
        self.system_indicator = QFrame()
        self.system_indicator.setFixedSize(18, 18)
        self.system_indicator.setStyleSheet("""
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(0, 255, 100, 255), stop:1 rgba(0, 200, 80, 255));
            border-radius: 9px;
            border: 2px solid rgba(0, 255, 150, 0.8);
        """)
        title_layout.addWidget(self.system_indicator)

        # 系统状态文本
        self.system_status_label = QLabel("系统就绪")
        self.system_status_label.setStyleSheet("""
            color: rgba(0, 255, 150, 255);
            font-size: 16px;
            font-weight: bold;
            font-family: 'Microsoft YaHei';
        """)
        title_layout.addWidget(self.system_status_label)

        self.main_layout.addWidget(self.title_bar)

    def create_main_content(self):
        """创建主内容区域"""
        self.content_frame = QFrame()
        self.content_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 240);
                border-radius: 10px;
                border: 1px solid rgba(0, 130, 200, 0.6);
            }
        """)

        self.content_layout = QHBoxLayout(self.content_frame)
        self.content_layout.setContentsMargins(15, 15, 15, 15)
        self.content_layout.setSpacing(20)

        # 创建左侧面板 - 目标管理
        self.create_left_panel()

        # 创建中间面板 - 当前追踪目标
        self.create_center_panel()

        # 创建右侧面板 - 智能预览分析
        self.create_right_panel()

        self.main_layout.addWidget(self.content_frame)

    def create_left_panel(self):
        """创建左侧目标管理面板"""
        self.left_panel = QFrame()
        self.left_panel.setFixedWidth(320)
        self.left_panel.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 200);
                border-radius: 10px;
                border: 1px solid rgba(0, 150, 230, 0.4);
            }
        """)

        left_layout = QVBoxLayout(self.left_panel)
        left_layout.setContentsMargins(15, 15, 15, 15)
        left_layout.setSpacing(15)

        # 系统监控区域
        self.create_system_monitor(left_layout)

        # 可用目标区域
        self.create_available_targets(left_layout)

        # 目标输入区域
        self.create_target_input(left_layout)

        self.content_layout.addWidget(self.left_panel)

    def create_system_monitor(self, parent_layout):
        """创建系统监控区域"""
        monitor_group = QGroupBox("系统监控")
        monitor_group.setStyleSheet("""
            QGroupBox {
                color: rgb(0, 120, 200);
                font-size: 14px;
                font-weight: bold;
                border: 1px solid rgba(0, 130, 200, 0.6);
                border-radius: 8px;
                margin-top: 16px;
                padding-top: 10px;
                font-family: 'Microsoft YaHei';
                background: rgba(240, 248, 255, 150);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 8px;
                background: rgba(255, 255, 255, 200);
            }
        """)

        monitor_layout = QVBoxLayout(monitor_group)
        monitor_layout.setSpacing(10)

        # CPU使用率
        cpu_layout = QHBoxLayout()
        cpu_label = QLabel("CPU使用率:")
        cpu_label.setStyleSheet("color: rgb(60, 120, 180); font-size: 12px; font-weight: bold;")
        self.cpu_progress = QProgressBar()
        self.cpu_progress.setValue(24)
        self.cpu_progress.setStyleSheet("""
            QProgressBar {
                border: 1px solid rgba(0, 150, 255, 0.5);
                border-radius: 4px;
                text-align: center;
                color: rgb(60, 120, 180);
                font-size: 10px;
                background: rgba(240, 248, 255, 100);
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(0, 150, 255, 200), stop:1 rgba(0, 200, 255, 255));
                border-radius: 3px;
            }
        """)
        cpu_layout.addWidget(cpu_label)
        cpu_layout.addWidget(self.cpu_progress)
        monitor_layout.addLayout(cpu_layout)

        # 内存使用率
        memory_layout = QHBoxLayout()
        memory_label = QLabel("内存使用率:")
        memory_label.setStyleSheet("color: rgb(60, 120, 180); font-size: 12px; font-weight: bold;")
        self.memory_progress = QProgressBar()
        self.memory_progress.setValue(51)
        self.memory_progress.setStyleSheet("""
            QProgressBar {
                border: 1px solid rgba(0, 200, 100, 0.5);
                border-radius: 4px;
                text-align: center;
                color: rgb(60, 120, 180);
                font-size: 10px;
                background: rgba(240, 248, 255, 100);
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(0, 200, 100, 200), stop:1 rgba(0, 255, 150, 255));
                border-radius: 3px;
            }
        """)
        memory_layout.addWidget(memory_label)
        memory_layout.addWidget(self.memory_progress)
        monitor_layout.addLayout(memory_layout)

        # 运行时间
        time_layout = QHBoxLayout()
        time_label = QLabel("运行时间:")
        time_label.setStyleSheet("color: rgb(60, 120, 180); font-size: 12px; font-weight: bold;")
        self.time_value = QLabel("00:00:00")
        self.time_value.setStyleSheet("color: rgb(0, 150, 200); font-weight: bold; font-size: 12px;")
        time_layout.addWidget(time_label)
        time_layout.addWidget(self.time_value)
        monitor_layout.addLayout(time_layout)

        parent_layout.addWidget(monitor_group)

    def create_available_targets(self, parent_layout):
        """创建可用目标区域"""
        targets_group = QGroupBox("可用目标")
        targets_group.setStyleSheet("""
            QGroupBox {
                color: rgb(0, 120, 200);
                font-size: 14px;
                font-weight: bold;
                border: 1px solid rgba(0, 130, 200, 0.6);
                border-radius: 8px;
                margin-top: 16px;
                padding-top: 10px;
                font-family: 'Microsoft YaHei';
                background: rgba(240, 248, 255, 150);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 8px;
                background: rgba(255, 255, 255, 200);
            }
        """)

        targets_layout = QVBoxLayout(targets_group)
        targets_layout.setSpacing(10)

        # 搜索框
        search_layout = QHBoxLayout()
        self.target_search = QLineEdit()
        self.target_search.setPlaceholderText("搜索目标ID...")
        self.target_search.setStyleSheet("""
            QLineEdit {
                background: rgba(255, 255, 255, 200);
                color: rgb(60, 120, 180);
                border: 1px solid rgba(0, 150, 255, 0.5);
                border-radius: 6px;
                padding: 8px;
                font-size: 13px;
                font-family: 'Microsoft YaHei';
            }
            QLineEdit:focus {
                border: 2px solid rgba(0, 200, 255, 0.8);
                background: rgba(240, 248, 255, 255);
            }
        """)
        search_layout.addWidget(self.target_search)

        search_button = QPushButton("搜索")
        search_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(0, 80, 150, 180), stop:1 rgba(0, 150, 220, 200));
                color: white;
                border-radius: 8px;
                padding: 8px 15px;
                font-weight: bold;
                font-family: 'Microsoft YaHei';
                border: 1px solid rgba(0, 200, 255, 0.8);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(0, 100, 180, 220), stop:1 rgba(0, 180, 255, 240));
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(0, 60, 120, 200), stop:1 rgba(0, 120, 180, 220));
            }
        """)
        search_layout.addWidget(search_button)
        targets_layout.addLayout(search_layout)

        # 目标列表
        self.available_targets_list = QListWidget()
        self.available_targets_list.setStyleSheet("""
            QListWidget {
                background: rgba(255, 255, 255, 200);
                border: 1px solid rgba(0, 150, 255, 0.3);
                border-radius: 6px;
                color: rgb(60, 120, 180);
                font-family: 'Microsoft YaHei';
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid rgba(0, 150, 255, 0.2);
            }
            QListWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(0, 150, 255, 100), stop:1 rgba(0, 200, 255, 150));
                color: white;
            }
            QListWidget::item:hover {
                background: rgba(0, 150, 255, 50);
            }
        """)
        targets_layout.addWidget(self.available_targets_list)

        parent_layout.addWidget(targets_group)

    def create_target_input(self, parent_layout):
        """创建目标输入区域"""
        input_group = QGroupBox("目标管理")
        input_group.setStyleSheet("""
            QGroupBox {
                color: rgb(0, 220, 255);
                font-size: 14px;
                font-weight: bold;
                border: 1px solid rgba(0, 130, 200, 0.6);
                border-radius: 8px;
                margin-top: 16px;
                padding-top: 10px;
                font-family: 'Microsoft YaHei';
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 8px;
                background: rgba(0, 30, 70, 120);
            }
        """)

        input_layout = QVBoxLayout(input_group)
        input_layout.setSpacing(10)

        # 目标ID输入
        id_layout = QHBoxLayout()
        self.target_id_input = QLineEdit()
        self.target_id_input.setPlaceholderText("输入车辆ID或从列表选择")
        self.target_id_input.setStyleSheet("""
            QLineEdit {
                background: rgba(255, 255, 255, 200);
                color: rgb(60, 120, 180);
                border: 1px solid rgba(0, 150, 255, 0.5);
                border-radius: 6px;
                padding: 8px;
                font-size: 13px;
                font-family: 'Microsoft YaHei';
            }
            QLineEdit:focus {
                border: 2px solid rgba(0, 200, 255, 0.8);
                background: rgba(240, 248, 255, 255);
            }
        """)
        id_layout.addWidget(self.target_id_input)
        input_layout.addLayout(id_layout)

        # 按钮区域
        buttons_layout = QHBoxLayout()

        self.add_target_button = QPushButton("添加目标")
        self.add_target_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(0, 150, 80, 200), stop:1 rgba(0, 200, 120, 220));
                color: white;
                border-radius: 8px;
                padding: 8px 12px;
                font-weight: bold;
                font-family: 'Microsoft YaHei';
                border: 1px solid rgba(0, 255, 150, 0.8);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(0, 180, 100, 240), stop:1 rgba(0, 220, 140, 255));
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(0, 120, 60, 200), stop:1 rgba(0, 160, 90, 220));
            }
        """)
        buttons_layout.addWidget(self.add_target_button)

        self.clear_targets_button = QPushButton("清除所有")
        self.clear_targets_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 100, 100, 180), stop:1 rgba(255, 150, 150, 200));
                color: white;
                border-radius: 8px;
                padding: 8px 12px;
                font-weight: bold;
                font-family: 'Microsoft YaHei';
                border: 1px solid rgba(255, 200, 200, 0.8);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 120, 120, 220), stop:1 rgba(255, 170, 170, 240));
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(200, 80, 80, 200), stop:1 rgba(230, 120, 120, 220));
            }
        """)
        buttons_layout.addWidget(self.clear_targets_button)
        input_layout.addLayout(buttons_layout)

        parent_layout.addWidget(input_group)

    def create_center_panel(self):
        """创建中间当前追踪目标面板"""
        self.center_panel = QFrame()
        self.center_panel.setMinimumWidth(350)
        self.center_panel.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 200);
                border-radius: 10px;
                border: 1px solid rgba(0, 150, 230, 0.4);
            }
        """)

        center_layout = QVBoxLayout(self.center_panel)
        center_layout.setContentsMargins(15, 15, 15, 15)
        center_layout.setSpacing(15)

        # 标题区域
        header_layout = QHBoxLayout()
        title_label = QLabel("当前追踪目标")
        title_label.setStyleSheet("""
            color: rgb(0, 120, 200);
            font-size: 18px;
            font-weight: bold;
            font-family: 'Microsoft YaHei';
        """)
        header_layout.addWidget(title_label)

        # 目标计数
        self.target_count_label = QLabel("0/4")
        self.target_count_label.setStyleSheet("""
            color: rgb(0, 150, 100);
            font-size: 16px;
            font-weight: bold;
            background: rgba(0, 150, 100, 0.1);
            border-radius: 12px;
            padding: 4px 12px;
            font-family: 'Microsoft YaHei';
        """)
        header_layout.addWidget(self.target_count_label)
        header_layout.addStretch()

        # 停止追踪按钮
        self.stop_tracking_button = QPushButton("停止追踪")
        self.stop_tracking_button.setEnabled(False)
        self.stop_tracking_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 100, 100, 180), stop:1 rgba(255, 150, 150, 200));
                color: white;
                border-radius: 8px;
                padding: 6px 12px;
                font-weight: bold;
                font-family: 'Microsoft YaHei';
                border: 1px solid rgba(255, 200, 200, 0.8);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 120, 120, 220), stop:1 rgba(255, 170, 170, 240));
            }
            QPushButton:disabled {
                background: rgba(100, 100, 100, 100);
                color: rgba(150, 150, 150, 150);
                border: 1px solid rgba(150, 150, 150, 0.3);
            }
        """)
        header_layout.addWidget(self.stop_tracking_button)
        center_layout.addLayout(header_layout)

        # 追踪目标表格
        self.tracked_targets_table = QTableWidget()
        self.tracked_targets_table.setColumnCount(4)
        self.tracked_targets_table.setHorizontalHeaderLabels(["ID", "类型", "状态", "关联度"])

        # 设置表格样式
        self.tracked_targets_table.setStyleSheet("""
            QTableWidget {
                background: rgba(255, 255, 255, 200);
                border: 1px solid rgba(0, 150, 255, 0.3);
                border-radius: 6px;
                color: rgb(60, 120, 180);
                font-family: 'Microsoft YaHei';
                gridline-color: rgba(0, 150, 255, 0.2);
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid rgba(0, 150, 255, 0.2);
            }
            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(0, 150, 255, 100), stop:1 rgba(0, 200, 255, 150));
                color: white;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(0, 150, 255, 180), stop:1 rgba(0, 200, 255, 200));
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
                font-family: 'Microsoft YaHei';
            }
        """)

        # 设置表格属性
        header = self.tracked_targets_table.horizontalHeader()
        header.setStretchLastSection(True)
        self.tracked_targets_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.tracked_targets_table.setAlternatingRowColors(True)

        center_layout.addWidget(self.tracked_targets_table)

        # 追踪统计信息
        stats_group = QGroupBox("追踪统计")
        stats_group.setStyleSheet("""
            QGroupBox {
                color: rgb(0, 120, 200);
                font-size: 14px;
                font-weight: bold;
                border: 1px solid rgba(0, 130, 200, 0.6);
                border-radius: 8px;
                margin-top: 16px;
                padding-top: 10px;
                font-family: 'Microsoft YaHei';
                background: rgba(240, 248, 255, 150);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 8px;
                background: rgba(255, 255, 255, 200);
            }
        """)

        stats_layout = QGridLayout(stats_group)
        stats_layout.setSpacing(8)

        # 添加统计信息
        self.add_stat_item("追踪状态:", "待机中", 0, 0, stats_layout)
        self.add_stat_item("运行时间:", "00:00:00", 0, 2, stats_layout)
        self.add_stat_item("平均精度:", "96.7%", 1, 0, stats_layout)
        self.add_stat_item("平均速度:", "45.2 km/h", 1, 2, stats_layout)

        center_layout.addWidget(stats_group)
        self.content_layout.addWidget(self.center_panel)

    def add_stat_item(self, label_text, value_text, row, col, layout):
        """添加统计信息项"""
        label = QLabel(label_text)
        label.setStyleSheet("color: rgb(60, 120, 180); font-size: 12px; font-family: 'Microsoft YaHei'; font-weight: bold;")
        layout.addWidget(label, row, col)

        value = QLabel(value_text)
        value.setStyleSheet("color: rgb(0, 150, 200); font-weight: bold; font-size: 12px; font-family: 'Microsoft YaHei';")
        layout.addWidget(value, row, col + 1)

        # 保存引用以便更新
        field_name = f"{label_text.replace(':', '').strip().lower().replace(' ', '_')}_value"
        setattr(self, field_name, value)

    def create_right_panel(self):
        """创建右侧智能预览分析面板"""
        self.right_panel = QFrame()
        self.right_panel.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 200);
                border-radius: 10px;
                border: 1px solid rgba(0, 150, 230, 0.4);
            }
        """)

        right_layout = QVBoxLayout(self.right_panel)
        right_layout.setContentsMargins(15, 15, 15, 15)
        right_layout.setSpacing(15)

        # 标题区域
        header_layout = QHBoxLayout()
        title_label = QLabel("智能目标预览分析")
        title_label.setStyleSheet("""
            color: rgb(0, 120, 200);
            font-size: 18px;
            font-weight: bold;
            font-family: 'Microsoft YaHei';
        """)
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # 追踪状态指示器
        self.tracking_status_indicator = QLabel("● 待机中")
        self.tracking_status_indicator.setStyleSheet("""
            color: rgb(255, 165, 0);
            font-size: 14px;
            font-weight: bold;
            font-family: 'Microsoft YaHei';
        """)
        header_layout.addWidget(self.tracking_status_indicator)
        right_layout.addLayout(header_layout)

        # 预览滚动区域
        self.preview_scroll = QScrollArea()
        self.preview_scroll.setWidgetResizable(True)
        self.preview_scroll.setStyleSheet("""
            QScrollArea {
                border: none;
                background: transparent;
            }
        """)

        self.preview_container = QWidget()
        self.preview_container.setStyleSheet("background: transparent;")
        self.preview_grid = QGridLayout(self.preview_container)
        self.preview_grid.setContentsMargins(10, 10, 10, 10)
        self.preview_grid.setSpacing(15)
        self.preview_grid.setAlignment(Qt.AlignTop)

        self.preview_scroll.setWidget(self.preview_container)

        # 初始状态：显示智能提示
        self.create_empty_state_widget()

        right_layout.addWidget(self.preview_scroll)
        self.content_layout.addWidget(self.right_panel)

    def create_empty_state_widget(self):
        """创建空状态智能提示组件"""
        self.empty_state_widget = QFrame()
        self.empty_state_widget.setStyleSheet("""
            QFrame {
                background: rgba(240, 248, 255, 150);
                border: 2px dashed rgba(0, 150, 255, 0.3);
                border-radius: 12px;
            }
        """)

        empty_layout = QVBoxLayout(self.empty_state_widget)
        empty_layout.setContentsMargins(30, 40, 30, 40)
        empty_layout.setSpacing(15)

        # 图标
        icon_label = QLabel("🎯")
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("""
            font-size: 48px;
            color: rgba(0, 150, 255, 0.6);
        """)
        empty_layout.addWidget(icon_label)

        # 主要提示文字
        main_text = QLabel("智能目标预览分析")
        main_text.setAlignment(Qt.AlignCenter)
        main_text.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: rgb(0, 180, 255);
            font-family: 'Microsoft YaHei';
        """)
        empty_layout.addWidget(main_text)

        # 说明文字
        desc_text = QLabel("选择并开始追踪目标后\n此区域将显示实时车辆信息\n包括车辆图像、速度、轨迹等")
        desc_text.setAlignment(Qt.AlignCenter)
        desc_text.setStyleSheet("""
            font-size: 14px;
            color: rgba(60, 120, 180, 0.8);
            font-family: 'Microsoft YaHei';
            line-height: 1.5;
        """)
        empty_layout.addWidget(desc_text)

        # 功能提示
        features_text = QLabel("✨ 实时图像预览\n📊 速度与轨迹分析\n🎯 精确度监控\n📈 性能统计")
        features_text.setAlignment(Qt.AlignCenter)
        features_text.setStyleSheet("""
            font-size: 12px;
            color: rgba(0, 255, 150, 0.7);
            font-family: 'Microsoft YaHei';
            line-height: 1.8;
            background: rgba(0, 255, 150, 0.05);
            border-radius: 8px;
            padding: 10px;
        """)
        empty_layout.addWidget(features_text)

        # 添加到预览网格
        self.preview_grid.addWidget(self.empty_state_widget, 0, 0)

    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QFrame()
        self.status_bar.setFixedHeight(60)
        self.status_bar.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(0, 80, 150, 180), stop:1 rgba(0, 150, 220, 200));
                border-bottom-left-radius: 15px;
                border-bottom-right-radius: 15px;
                border-top: 2px solid rgba(0, 200, 255, 0.8);
            }
        """)

        status_layout = QHBoxLayout(self.status_bar)
        status_layout.setContentsMargins(20, 0, 20, 0)

        # 状态信息
        self.status_label = QLabel("● 系统就绪 - 等待目标选择")
        self.status_label.setStyleSheet("""
            color: rgba(0, 255, 150, 255);
            font-size: 16px;
            font-weight: bold;
            font-family: 'Microsoft YaHei';
        """)
        status_layout.addWidget(self.status_label)

        status_layout.addStretch()

        # 按钮区域
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)

        self.start_button = QPushButton("启动多目标追踪")
        self.start_button.setMinimumSize(180, 45)
        self.start_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(0, 150, 80, 200), stop:1 rgba(0, 200, 120, 220));
                color: white;
                border-radius: 10px;
                font-weight: bold;
                font-size: 16px;
                font-family: 'Microsoft YaHei';
                border: 2px solid rgba(0, 255, 150, 0.8);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(0, 180, 100, 240), stop:1 rgba(0, 220, 140, 255));
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(0, 120, 60, 200), stop:1 rgba(0, 160, 90, 220));
            }
        """)

        self.close_button = QPushButton("关闭")
        self.close_button.setMinimumSize(120, 45)
        self.close_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(120, 120, 120, 180), stop:1 rgba(150, 150, 150, 200));
                color: white;
                border-radius: 10px;
                font-weight: bold;
                font-size: 16px;
                font-family: 'Microsoft YaHei';
                border: 2px solid rgba(180, 180, 180, 0.8);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(140, 140, 140, 220), stop:1 rgba(170, 170, 170, 240));
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(100, 100, 100, 200), stop:1 rgba(130, 130, 130, 220));
            }
        """)

        buttons_layout.addWidget(self.start_button)
        buttons_layout.addWidget(self.close_button)
        status_layout.addLayout(buttons_layout)

        self.main_layout.addWidget(self.status_bar)

    def connect_signals(self):
        """连接信号和槽"""
        self.add_target_button.clicked.connect(self.add_target)
        self.clear_targets_button.clicked.connect(self.clear_targets)
        self.start_button.clicked.connect(self.toggle_tracking)
        self.stop_tracking_button.clicked.connect(self.stop_tracking)
        self.close_button.clicked.connect(self.close)

        # 双击列表项添加目标
        self.available_targets_list.itemDoubleClicked.connect(self.on_target_double_clicked)

        # 表格选择变化
        self.tracked_targets_table.itemSelectionChanged.connect(self.on_table_selection_changed)

    def load_available_targets(self):
        """加载可用目标列表"""
        # 尝试从父窗口获取真实的检测目标
        parent_window = self.parent()
        if parent_window and hasattr(parent_window, 'get_available_targets'):
            # 使用主窗口的方法获取可用目标
            detected_targets = parent_window.get_available_targets()

            # 如果有真实检测目标，使用真实数据
            if detected_targets:
                self.available_targets = detected_targets
                print(f"加载真实检测目标: {len(detected_targets)} 个")
            else:
                # 如果没有检测到目标，使用少量模拟数据
                self.available_targets = []
                for i in range(1, 6):  # 只生成5个模拟目标
                    self.available_targets.append({
                        'id': i,
                        'type': '车辆',
                        'confidence': random.randint(85, 99)
                    })
                print("使用模拟目标数据（无真实检测）")
        else:
            # 如果没有父窗口，使用少量模拟数据
            self.available_targets = []
            for i in range(1, 6):  # 只生成5个模拟目标
                self.available_targets.append({
                    'id': i,
                    'type': '车辆',
                    'confidence': random.randint(85, 99)
                })
            print("使用模拟目标数据（无父窗口）")

        self.update_available_targets_list()

    def update_available_targets_list(self):
        """更新可用目标列表显示"""
        self.available_targets_list.clear()

        for target in self.available_targets:
            # 兼容不同的数据结构
            target_type = target.get('type', target.get('class', '车辆'))
            target_confidence = target.get('confidence', 90)

            item_text = f"ID: {target['id']} | {target_type} | 置信度: {target_confidence}%"
            item = QListWidgetItem(item_text)
            item.setData(Qt.UserRole, target['id'])
            self.available_targets_list.addItem(item)

    def update_available_targets(self, targets_data):
        """更新可用目标列表 - 从主窗口接收真实的检测数据

        Args:
            targets_data: 包含ID和类型的目标列表
        """
        if not targets_data:
            # 如果没有检测到目标，使用模拟数据
            self.load_available_targets()
            return

        self.available_targets = []
        for target in targets_data:
            target_id = target.get('id')
            target_class = target.get('class', '车辆')

            self.available_targets.append({
                'id': target_id,
                'type': target_class,
                'confidence': random.randint(85, 99)  # 模拟置信度
            })

        self.update_available_targets_list()
        print(f"更新可用目标列表: {len(self.available_targets)} 个目标")

    def refresh_available_targets(self):
        """定时刷新可用目标列表"""
        if not self.is_tracking:  # 只在非追踪状态下刷新
            old_count = len(self.available_targets)
            old_ids = set(target['id'] for target in self.available_targets)

            self.load_available_targets()

            new_count = len(self.available_targets)
            new_ids = set(target['id'] for target in self.available_targets)

            if old_count != new_count or old_ids != new_ids:
                print(f"可用目标变化: {old_count} -> {new_count} 个目标")

    def on_target_double_clicked(self, item):
        """双击目标项时添加到追踪列表"""
        # 检查是否已达到最大追踪数量限制
        MAX_TRACKING_TARGETS = 4
        if len(self.tracked_targets) >= MAX_TRACKING_TARGETS:
            self.status_label.setText(f"● 已达到最大追踪数量限制 ({MAX_TRACKING_TARGETS}个)")
            print(f"已达到最大追踪数量限制: {MAX_TRACKING_TARGETS}个")
            return

        target_id = item.data(Qt.UserRole)
        if target_id and target_id not in self.tracked_targets:
            self.tracked_targets.append(target_id)
            self.update_tracked_targets_table()
            self.update_target_count()
            self.target_added.emit(target_id)
            self.status_label.setText(f"● 成功添加目标 {target_id}")
            print(f"双击添加目标: {target_id}")
        elif target_id in self.tracked_targets:
            self.status_label.setText(f"● 目标 {target_id} 已在追踪列表中")
            print(f"目标 {target_id} 已经在追踪列表中")

    def on_table_selection_changed(self):
        """表格选择变化时的处理"""
        selected_items = self.tracked_targets_table.selectedItems()
        if selected_items:
            row = selected_items[0].row()
            if row < len(self.tracked_targets):
                target_id = self.tracked_targets[row]
                # 这里可以添加高亮预览的逻辑
                print(f"选中目标: {target_id}")

    def add_target(self):
        """添加目标到追踪列表"""
        # 检查是否已达到最大追踪数量限制
        MAX_TRACKING_TARGETS = 4
        if len(self.tracked_targets) >= MAX_TRACKING_TARGETS:
            self.status_label.setText(f"● 已达到最大追踪数量限制 ({MAX_TRACKING_TARGETS}个)")
            print(f"已达到最大追踪数量限制: {MAX_TRACKING_TARGETS}个")
            return

        target_id_text = self.target_id_input.text().strip()

        if target_id_text:
            # 从输入框添加目标
            try:
                target_id = int(target_id_text)
                # 检查目标ID是否在可用目标列表中
                available_ids = [target['id'] for target in self.available_targets]
                if target_id in available_ids and target_id not in self.tracked_targets:
                    self.tracked_targets.append(target_id)
                    self.update_tracked_targets_table()
                    self.update_target_count()
                    self.target_id_input.clear()
                    self.target_added.emit(target_id)
                    self.status_label.setText(f"● 成功添加目标 {target_id}")
                    print(f"手动添加目标: {target_id}")
                elif target_id in self.tracked_targets:
                    self.status_label.setText(f"● 目标 {target_id} 已在追踪列表中")
                    print(f"目标 {target_id} 已经在追踪列表中")
                else:
                    self.status_label.setText(f"● 目标 {target_id} 不在可用目标列表中")
                    print(f"目标 {target_id} 不在可用目标列表中")
            except ValueError:
                self.status_label.setText("● 请输入有效的数字ID")
                print("请输入有效的数字ID")
        else:
            # 从列表中获取选中的目标
            selected_items = self.available_targets_list.selectedItems()
            if selected_items:
                target_id = selected_items[0].data(Qt.UserRole)
                if target_id and target_id not in self.tracked_targets:
                    self.tracked_targets.append(target_id)
                    self.update_tracked_targets_table()
                    self.update_target_count()
                    self.target_added.emit(target_id)
                    self.status_label.setText(f"● 成功添加目标 {target_id}")
                    print(f"从列表添加目标: {target_id}")
                elif target_id in self.tracked_targets:
                    self.status_label.setText(f"● 目标 {target_id} 已在追踪列表中")
                    print(f"目标 {target_id} 已经在追踪列表中")
            else:
                self.status_label.setText("● 请选择一个目标或输入目标ID")
                print("请选择一个目标或输入目标ID")

    def clear_targets(self):
        """清除所有追踪目标"""
        self.tracked_targets.clear()
        self.update_tracked_targets_table()
        self.update_target_count()
        self.clear_preview_area()
        print("清除所有目标")

    def update_tracked_targets_table(self):
        """更新追踪目标表格"""
        self.tracked_targets_table.setRowCount(len(self.tracked_targets))

        for idx, target_id in enumerate(self.tracked_targets):
            # ID
            id_item = QTableWidgetItem(str(target_id))
            id_item.setTextAlignment(Qt.AlignCenter)
            self.tracked_targets_table.setItem(idx, 0, id_item)

            # 类型
            type_item = QTableWidgetItem("车辆")
            type_item.setTextAlignment(Qt.AlignCenter)
            self.tracked_targets_table.setItem(idx, 1, type_item)

            # 状态
            status_text = "追踪中" if self.is_tracking else "就绪"
            status_item = QTableWidgetItem(status_text)
            status_item.setTextAlignment(Qt.AlignCenter)
            if self.is_tracking:
                status_item.setForeground(QColor("#00C875"))
            else:
                status_item.setForeground(QColor("#86909C"))
            self.tracked_targets_table.setItem(idx, 2, status_item)

            # 关联度
            confidence = random.randint(85, 99)
            confidence_item = QTableWidgetItem(f"{confidence}%")
            confidence_item.setTextAlignment(Qt.AlignCenter)
            if confidence >= 95:
                confidence_item.setForeground(QColor("#00C875"))
            elif confidence >= 90:
                confidence_item.setForeground(QColor("#4080FF"))
            else:
                confidence_item.setForeground(QColor("#FF7D00"))
            self.tracked_targets_table.setItem(idx, 3, confidence_item)

    def update_target_count(self):
        """更新目标计数显示"""
        count = len(self.tracked_targets)
        MAX_TRACKING_TARGETS = 4
        self.target_count_label.setText(f"{count}/{MAX_TRACKING_TARGETS}")

        # 更新统计信息
        if hasattr(self, 'target_count_value'):
            self.target_count_value.setText(str(count))

        # 如果达到最大数量，更新按钮状态
        if count >= MAX_TRACKING_TARGETS:
            self.add_target_button.setEnabled(False)
            self.add_target_button.setText("已达上限")
        else:
            self.add_target_button.setEnabled(True)
            self.add_target_button.setText("添加目标")

    def toggle_tracking(self):
        """切换追踪状态"""
        if not self.tracked_targets:
            self.status_label.setText("● 请先添加要追踪的目标")
            return

        self.is_tracking = not self.is_tracking

        if self.is_tracking:
            self.start_tracking()
        else:
            self.stop_tracking()

    def start_tracking(self):
        """开始追踪"""
        self.tracking_time = 0

        # 更新UI状态
        self.start_button.setText("停止追踪")
        self.start_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 100, 100, 200), stop:1 rgba(255, 150, 150, 220));
                color: white;
                border-radius: 10px;
                font-weight: bold;
                font-size: 16px;
                font-family: 'Microsoft YaHei';
                border: 2px solid rgba(255, 200, 200, 0.8);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 120, 120, 240), stop:1 rgba(255, 170, 170, 255));
            }
        """)

        # 禁用相关控件
        self.add_target_button.setEnabled(False)
        self.clear_targets_button.setEnabled(False)
        self.target_id_input.setEnabled(False)
        self.available_targets_list.setEnabled(False)
        self.stop_tracking_button.setEnabled(True)

        # 更新状态显示
        self.status_label.setText("● 多目标追踪进行中...")
        self.system_status_label.setText("追踪中")
        self.system_status_label.setStyleSheet("""
            color: rgba(255, 100, 100, 255);
            font-size: 16px;
            font-weight: bold;
            font-family: 'Microsoft YaHei';
        """)
        self.tracking_status_indicator.setText("● 追踪中")
        self.tracking_status_indicator.setStyleSheet("""
            color: rgb(0, 255, 100);
            font-size: 14px;
            font-weight: bold;
            font-family: 'Microsoft YaHei';
        """)

        # 更新统计信息
        if hasattr(self, '追踪状态_value'):
            self.追踪状态_value.setText("追踪中")
            self.追踪状态_value.setStyleSheet("color: rgb(0, 255, 100); font-weight: bold; font-size: 12px; font-family: 'Microsoft YaHei';")

        # 更新表格状态
        self.update_tracked_targets_table()

        # 清除空状态，显示预览
        self.clear_empty_state()
        self.create_target_previews()

        # 启动定时器
        self.update_timer.start(1000)

        # 发送信号
        self.tracking_started.emit(self.tracked_targets)
        print(f"开始追踪 {len(self.tracked_targets)} 个目标: {self.tracked_targets}")

    def stop_tracking(self):
        """停止追踪"""
        self.is_tracking = False

        # 更新UI状态
        self.start_button.setText("启动多目标追踪")
        self.start_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(0, 150, 80, 200), stop:1 rgba(0, 200, 120, 220));
                color: white;
                border-radius: 10px;
                font-weight: bold;
                font-size: 16px;
                font-family: 'Microsoft YaHei';
                border: 2px solid rgba(0, 255, 150, 0.8);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(0, 180, 100, 240), stop:1 rgba(0, 220, 140, 255));
            }
        """)

        # 重新启用控件
        self.add_target_button.setEnabled(True)
        self.clear_targets_button.setEnabled(True)
        self.target_id_input.setEnabled(True)
        self.available_targets_list.setEnabled(True)
        self.stop_tracking_button.setEnabled(False)

        # 更新状态显示
        self.status_label.setText("● 系统就绪 - 等待目标选择")
        self.system_status_label.setText("系统就绪")
        self.system_status_label.setStyleSheet("""
            color: rgba(0, 255, 150, 255);
            font-size: 16px;
            font-weight: bold;
            font-family: 'Microsoft YaHei';
        """)
        self.tracking_status_indicator.setText("● 待机中")
        self.tracking_status_indicator.setStyleSheet("""
            color: rgb(255, 165, 0);
            font-size: 14px;
            font-weight: bold;
            font-family: 'Microsoft YaHei';
        """)

        # 更新统计信息
        if hasattr(self, '追踪状态_value'):
            self.追踪状态_value.setText("已停止")
            self.追踪状态_value.setStyleSheet("color: rgb(255, 165, 0); font-weight: bold; font-size: 12px; font-family: 'Microsoft YaHei';")

        # 更新表格状态
        self.update_tracked_targets_table()

        # 停止定时器
        self.update_timer.stop()

        # 发送信号
        self.tracking_stopped.emit()
        print("停止追踪")

    def clear_empty_state(self):
        """清除空状态组件"""
        if hasattr(self, 'empty_state_widget') and self.empty_state_widget:
            self.empty_state_widget.setParent(None)
            self.empty_state_widget = None

    def clear_preview_area(self):
        """清除预览区域"""
        # 清除所有预览组件
        for i in reversed(range(self.preview_grid.count())):
            item = self.preview_grid.itemAt(i)
            if item and item.widget():
                item.widget().setParent(None)

        self.target_previews.clear()

        # 重新显示空状态
        self.create_empty_state_widget()

    def create_target_previews(self):
        """为追踪目标创建预览组件"""
        for idx, target_id in enumerate(self.tracked_targets):
            self.create_target_preview_card(target_id, idx)

    def create_target_preview_card(self, target_id, index, target_image=None):
        """创建单个目标预览卡片"""
        # 计算网格位置 (2列布局)
        row = index // 2
        col = index % 2

        # 创建预览卡片
        preview_card = QFrame()
        preview_card.setFixedSize(220, 200)
        preview_card.setStyleSheet("""
            QFrame {
                background: rgba(240, 248, 255, 200);
                border: 2px solid rgba(0, 150, 255, 0.5);
                border-radius: 12px;
            }
        """)

        card_layout = QVBoxLayout(preview_card)
        card_layout.setContentsMargins(10, 10, 10, 10)
        card_layout.setSpacing(8)

        # 标题栏
        header_layout = QHBoxLayout()

        id_label = QLabel(f"ID: {target_id}")
        id_label.setStyleSheet("""
            color: rgb(0, 120, 200);
            font-weight: bold;
            font-size: 14px;
            font-family: 'Microsoft YaHei';
        """)
        header_layout.addWidget(id_label)

        type_badge = QLabel("车辆")
        type_badge.setStyleSheet("""
            color: white;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(0, 150, 255, 200), stop:1 rgba(0, 200, 255, 255));
            border-radius: 8px;
            padding: 2px 8px;
            font-size: 10px;
            font-weight: bold;
            font-family: 'Microsoft YaHei';
        """)
        header_layout.addWidget(type_badge)
        header_layout.addStretch()

        # 状态指示器
        status_indicator = QLabel("●")
        status_indicator.setStyleSheet("""
            color: rgb(0, 255, 100);
            font-size: 16px;
        """)
        header_layout.addWidget(status_indicator)

        card_layout.addLayout(header_layout)

        # 图像预览区域
        image_frame = QFrame()
        image_frame.setFixedHeight(100)
        image_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 200);
                border: 1px solid rgba(0, 150, 255, 0.3);
                border-radius: 6px;
            }
        """)

        image_layout = QVBoxLayout(image_frame)
        image_layout.setContentsMargins(2, 2, 2, 2)

        image_label = QLabel()
        image_label.setAlignment(Qt.AlignCenter)
        image_label.setScaledContents(True)

        # 如果有目标图像，显示真实图像；否则显示占位符
        if target_image is not None:
            # 将numpy数组转换为QPixmap
            pixmap = self.numpy_to_pixmap(target_image)
            if pixmap and not pixmap.isNull():
                # 缩放图像以适应标签大小
                scaled_pixmap = pixmap.scaled(196, 96, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                image_label.setPixmap(scaled_pixmap)
            else:
                # 如果转换失败，显示占位符
                image_label.setText("🚗")
                image_label.setStyleSheet("""
                    font-size: 32px;
                    color: rgba(0, 150, 255, 0.8);
                """)
        else:
            # 显示占位符
            image_label.setText("🚗")
            image_label.setStyleSheet("""
                font-size: 32px;
                color: rgba(0, 150, 255, 0.8);
            """)

        image_layout.addWidget(image_label)

        card_layout.addWidget(image_frame)

        # 信息区域
        info_layout = QGridLayout()
        info_layout.setSpacing(5)

        # 置信度
        conf_label = QLabel("置信度:")
        conf_label.setStyleSheet("color: rgb(60, 120, 180); font-size: 10px; font-family: 'Microsoft YaHei'; font-weight: bold;")
        conf_value = QLabel(f"{random.randint(90, 99)}%")
        conf_value.setStyleSheet("color: rgb(0, 150, 100); font-weight: bold; font-size: 10px; font-family: 'Microsoft YaHei';")
        info_layout.addWidget(conf_label, 0, 0)
        info_layout.addWidget(conf_value, 0, 1)

        # 速度
        speed_label = QLabel("速度:")
        speed_label.setStyleSheet("color: rgb(60, 120, 180); font-size: 10px; font-family: 'Microsoft YaHei'; font-weight: bold;")
        speed_value = QLabel(f"{random.randint(30, 80)} km/h")
        speed_value.setStyleSheet("color: rgb(255, 140, 0); font-weight: bold; font-size: 10px; font-family: 'Microsoft YaHei';")
        info_layout.addWidget(speed_label, 1, 0)
        info_layout.addWidget(speed_value, 1, 1)

        card_layout.addLayout(info_layout)

        # 保存预览组件引用
        self.target_previews[target_id] = {
            'card': preview_card,
            'image': image_label,
            'status': status_indicator,
            'confidence': conf_value,
            'speed': speed_value
        }

        # 添加到网格
        self.preview_grid.addWidget(preview_card, row, col)

    def numpy_to_pixmap(self, image_array):
        """将numpy数组转换为QPixmap"""
        try:
            if image_array is None or image_array.size == 0:
                return None

            # 确保图像是3通道的
            if len(image_array.shape) == 2:
                # 灰度图转RGB
                image_array = cv2.cvtColor(image_array, cv2.COLOR_GRAY2RGB)
            elif len(image_array.shape) == 3 and image_array.shape[2] == 3:
                # BGR转RGB
                image_array = cv2.cvtColor(image_array, cv2.COLOR_BGR2RGB)
            elif len(image_array.shape) == 3 and image_array.shape[2] == 4:
                # BGRA转RGB
                image_array = cv2.cvtColor(image_array, cv2.COLOR_BGRA2RGB)

            height, width, channel = image_array.shape
            bytes_per_line = 3 * width

            # 创建QImage
            q_image = QImage(image_array.data, width, height, bytes_per_line, QImage.Format_RGB888)

            # 转换为QPixmap
            return QPixmap.fromImage(q_image)

        except Exception as e:
            print(f"图像转换错误: {e}")
            return None

    def update_target_image(self, target_id, image_array):
        """更新目标图像"""
        if target_id in self.target_previews:
            pixmap = self.numpy_to_pixmap(image_array)
            if pixmap and not pixmap.isNull():
                # 缩放图像以适应标签大小
                scaled_pixmap = pixmap.scaled(196, 96, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                self.target_previews[target_id]['image'].setPixmap(scaled_pixmap)
                # 清除文字样式
                self.target_previews[target_id]['image'].setStyleSheet("")
            else:
                # 如果图像无效，显示占位符
                self.target_previews[target_id]['image'].clear()
                self.target_previews[target_id]['image'].setText("🚗")
                self.target_previews[target_id]['image'].setStyleSheet("""
                    font-size: 32px;
                    color: rgba(0, 150, 255, 0.8);
                """)

    def update_tracking_info(self):
        """更新追踪信息"""
        if self.is_tracking:
            self.tracking_time += 1

            # 更新运行时间
            hours = self.tracking_time // 3600
            minutes = (self.tracking_time % 3600) // 60
            seconds = self.tracking_time % 60
            time_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"

            self.time_value.setText(time_str)
            if hasattr(self, '运行时间_value'):
                self.运行时间_value.setText(time_str)

            # 更新预览数据
            self.update_preview_data()

    def update_preview_data(self):
        """更新预览数据"""
        for target_id, preview in self.target_previews.items():
            # 随机更新置信度和速度
            new_confidence = random.randint(88, 99)
            new_speed = random.randint(25, 85)

            preview['confidence'].setText(f"{new_confidence}%")
            preview['speed'].setText(f"{new_speed} km/h")

# 测试代码
if __name__ == "__main__":
    app = QApplication(sys.argv)
    dialog = OptimizedMultiTrackingDialog()
    dialog.show()
    sys.exit(app.exec())
