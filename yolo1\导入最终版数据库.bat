@echo off
chcp 65001 >nul
echo ================================================================================
echo 🚀 高速公路YOLOv8智能监控系统 - 最终版数据库导入工具
echo ================================================================================
echo.

:: 设置变量
set MYSQL_USER=root
set MYSQL_PASSWORD=123456
set DATABASE_NAME=yolo
set SQL_FILE=MySQL8-最终版数据库.sql

echo 📋 导入配置:
echo    MySQL用户: %MYSQL_USER%
echo    数据库名: %DATABASE_NAME%
echo    SQL文件: %SQL_FILE%
echo    数据库类型: MySQL 8.0 (统一数据表设计)
echo.

:: 检查MySQL是否可用
echo 🔍 检查MySQL服务...
mysql --version >nul 2>&1
if errorlevel 1 (
    echo ❌ MySQL命令不可用，请检查MySQL是否已安装并添加到PATH
    echo.
    echo 💡 解决方案:
    echo    1. 确保MySQL 8.0已正确安装
    echo    2. 将MySQL的bin目录添加到系统PATH
    echo    3. 重新打开命令提示符
    pause
    exit /b 1
)
echo ✅ MySQL命令可用

:: 检查SQL文件是否存在
echo.
echo 🔍 检查SQL文件...
if not exist "%SQL_FILE%" (
    echo ❌ SQL文件不存在: %SQL_FILE%
    echo.
    echo 💡 请确保以下文件存在:
    echo    - MySQL8-最终版数据库.sql
    pause
    exit /b 1
)
echo ✅ SQL文件存在

:: 测试MySQL连接
echo.
echo 🔍 测试MySQL连接...
mysql -u %MYSQL_USER% -p%MYSQL_PASSWORD% -e "SELECT 1;" >nul 2>&1
if errorlevel 1 (
    echo ❌ MySQL连接失败，请检查用户名和密码
    echo.
    echo 💡 解决方案:
    echo    1. 确保MySQL服务已启动
    echo    2. 检查用户名和密码是否正确
    echo    3. 修改本脚本中的MYSQL_USER和MYSQL_PASSWORD变量
    pause
    exit /b 1
)
echo ✅ MySQL连接成功

:: 确认导入
echo.
echo ⚠️  警告: 此操作将删除现有的 '%DATABASE_NAME%' 数据库并重新创建
echo.
echo 📊 新数据库特点:
echo    ✅ 统一数据表设计 (5个核心表)
echo    ✅ 支持admin和operator两个角色
echo    ✅ 完整的测试数据 (6个监控点)
echo    ✅ 与前端开发计划完全对应
echo    ✅ 支持YOLOv8检测、ByteTrack追踪、事故检测
echo.
set /p confirm=是否继续? (y/N): 
if /i not "%confirm%"=="y" (
    echo 操作已取消
    pause
    exit /b 0
)

:: 导入数据库
echo.
echo 🚀 开始导入最终版数据库...
echo.

mysql -u %MYSQL_USER% -p%MYSQL_PASSWORD% < "%SQL_FILE%"
if errorlevel 1 (
    echo ❌ 数据库导入失败
    echo.
    echo 💡 可能的原因:
    echo    1. SQL文件格式错误
    echo    2. MySQL权限不足
    echo    3. 数据库连接中断
    pause
    exit /b 1
)

echo ✅ 数据库导入成功

:: 验证导入结果
echo.
echo 🧪 验证导入结果...
mysql -u %MYSQL_USER% -p%MYSQL_PASSWORD% -e "USE %DATABASE_NAME%; SELECT 'Tables:' as info, COUNT(*) as count FROM information_schema.tables WHERE table_schema='%DATABASE_NAME%' UNION ALL SELECT 'Users:', COUNT(*) FROM user UNION ALL SELECT 'Monitors:', COUNT(*) FROM monitor UNION ALL SELECT 'Records:', COUNT(*) FROM unified_record UNION ALL SELECT 'Configs:', COUNT(*) FROM system_config;"

echo.
echo ================================================================================
echo 🎉 最终版数据库导入完成！
echo ================================================================================

echo.
echo 📊 数据库结构 (统一设计):
echo    📋 核心表: 5个
echo      1. user - 用户管理
echo      2. monitor - 监控点管理  
echo      3. unified_record - 统一数据记录
echo      4. system_config - 系统配置
echo      5. system_log - 系统日志
echo    👁️ 视图: 3个 (监控状态、系统概览、警报统计)

echo.
echo 📊 测试数据:
echo    👥 用户: 2个 (admin, operator)
echo    📹 监控点: 6个 (杭州至千岛湖全程)
echo    📝 统一记录: 10条 (警报、检测、追踪、事故、交通)
echo    ⚙️ 系统配置: 8项

echo.
echo 🔑 默认登录账号:
echo    管理员: admin / 123456
echo    监控员: operator / 123456

echo.
echo 🎯 数据库优势:
echo    ✅ 统一数据表设计，避免数据冗余
echo    ✅ JSON字段存储复杂数据，灵活扩展
echo    ✅ 完整的索引和视图，查询性能优化
echo    ✅ 严格的外键约束，数据一致性保证
echo    ✅ 与前端开发计划完全对应

echo.
echo 🎯 下一步操作:
echo    1. 启动后端服务: python backend/app_enhanced.py
echo    2. 访问系统: http://localhost:5500
echo    3. 开始前端开发: 按照API接口文档-前端对接版.md
echo    4. 逐步实现前端功能模块

echo.
echo 📞 如有问题，请检查:
echo    1. MySQL 8.0服务状态
echo    2. 用户权限设置
echo    3. 数据库连接配置
echo    4. API接口文档对接

echo.
pause
