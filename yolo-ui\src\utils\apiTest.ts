// API测试工具
import axios from 'axios'

export interface ApiTestResult {
  success: boolean
  status?: number
  data?: any
  error?: string
  duration?: number
}

export class ApiTester {
  private baseURL: string

  constructor(baseURL: string = '/api/v1') {
    this.baseURL = baseURL
  }

  async testEndpoint(endpoint: string, method: 'GET' | 'POST' = 'GET', data?: any): Promise<ApiTestResult> {
    const startTime = Date.now()
    
    try {
      const config = {
        method,
        url: `${this.baseURL}${endpoint}`,
        timeout: 5000,
        headers: {
          'Content-Type': 'application/json'
        }
      }

      if (data && method === 'POST') {
        config.data = data
      }

      const response = await axios(config)
      const duration = Date.now() - startTime

      return {
        success: true,
        status: response.status,
        data: response.data,
        duration
      }
    } catch (error: any) {
      const duration = Date.now() - startTime
      
      return {
        success: false,
        status: error.response?.status,
        error: error.message,
        data: error.response?.data,
        duration
      }
    }
  }

  async testConnection(): Promise<boolean> {
    try {
      // 尝试连接后端健康检查端点
      const response = await axios.get('http://127.0.0.1:5501/api/v1/health', {
        timeout: 3000
      })
      return response.status === 200
    } catch (error) {
      console.warn('后端健康检查失败:', error)
      return false
    }
  }

  async testAllEndpoints(): Promise<Record<string, ApiTestResult>> {
    const endpoints = [
      '/auth/profile',
      '/analysis/statistics/overview',
      '/monitor/list',
      '/accident/alerts/realtime',
      '/accident/records'
    ]

    const results: Record<string, ApiTestResult> = {}

    for (const endpoint of endpoints) {
      results[endpoint] = await this.testEndpoint(endpoint)
      // 添加小延迟避免请求过于频繁
      await new Promise(resolve => setTimeout(resolve, 100))
    }

    return results
  }
}

export const apiTester = new ApiTester()
