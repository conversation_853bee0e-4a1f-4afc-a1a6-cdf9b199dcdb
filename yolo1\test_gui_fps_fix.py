#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试yolo1项目GUI FPS显示修复
验证FPS更新频率是否与yolo项目一致
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import torch
import numpy as np
import time
from PySide6.QtCore import QObject, Signal
from classes.yolo import YoloPredictor

class FPSTestReceiver(QObject):
    """接收FPS信号的测试类"""
    def __init__(self):
        super().__init__()
        self.fps_updates = []
        self.last_update_time = time.time()
    
    def on_fps_update(self, fps_text):
        current_time = time.time()
        interval = current_time - self.last_update_time
        self.fps_updates.append({
            'fps': fps_text,
            'time': current_time,
            'interval': interval
        })
        self.last_update_time = current_time
        print(f"FPS更新: {fps_text}, 间隔: {interval:.3f}秒")

def test_gui_fps_updates():
    print("=== yolo1项目GUI FPS更新测试 ===")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"GPU设备: {torch.cuda.get_device_name(0)}")
    
    # 创建YOLO预测器
    predictor = YoloPredictor()
    
    # 创建FPS接收器
    fps_receiver = FPSTestReceiver()
    
    # 连接FPS信号
    predictor.yolo2main_fps.connect(fps_receiver.on_fps_update)
    
    # 设置测试参数
    predictor.new_model_name = 'yolov8n.pt'
    predictor.source = '0'  # 使用摄像头
    predictor.conf_thres = 0.25
    predictor.iou_thres = 0.45
    predictor.speed_thres = 10  # 10ms延迟
    
    print("\n开始FPS更新测试（10秒）...")
    
    try:
        # 模拟检测过程
        test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
        
        # 初始化计数器
        predictor.count = 0
        predictor.start_time = time.time()
        predictor.prev_time = time.time()
        
        start_test_time = time.time()
        frame_count = 0
        
        while time.time() - start_test_time < 10:  # 测试10秒
            # 模拟emit_res中的FPS计算
            predictor.count += 1
            if predictor.count % 3 == 0 and predictor.count >= 3:
                fps_value = int(3 / (time.time() - predictor.start_time))
                predictor.yolo2main_fps.emit(f"{fps_value} FPS")
                predictor.start_time = time.time()
            
            # 模拟主循环中的实时FPS计算
            curr_time = time.time()
            if hasattr(predictor, 'prev_time'):
                fps = 1. / (curr_time - predictor.prev_time)
                predictor.yolo2main_fps.emit('%.1f FPS' % fps)
            predictor.prev_time = curr_time
            
            frame_count += 1
            time.sleep(0.033)  # 模拟30FPS的处理速度
        
        # 分析结果
        print(f"\n=== 测试结果分析 ===")
        print(f"总测试时间: 10秒")
        print(f"模拟处理帧数: {frame_count}")
        print(f"FPS更新次数: {len(fps_receiver.fps_updates)}")
        
        if len(fps_receiver.fps_updates) > 1:
            intervals = [update['interval'] for update in fps_receiver.fps_updates[1:]]  # 跳过第一个
            avg_interval = np.mean(intervals)
            min_interval = np.min(intervals)
            max_interval = np.max(intervals)
            
            print(f"平均更新间隔: {avg_interval:.3f}秒")
            print(f"最小更新间隔: {min_interval:.3f}秒")
            print(f"最大更新间隔: {max_interval:.3f}秒")
            
            # 评估更新频率
            expected_interval = 0.033  # 期望每帧都更新
            if avg_interval <= 0.1:  # 100ms内
                print("✅ FPS更新频率正常，用户应该感觉流畅")
            elif avg_interval <= 0.3:  # 300ms内
                print("⚠️ FPS更新频率一般，可能有轻微卡顿感")
            else:
                print("❌ FPS更新频率过低，用户会感觉明显卡顿")
        
        # 显示最近几次FPS更新
        print(f"\n最近5次FPS更新:")
        for update in fps_receiver.fps_updates[-5:]:
            print(f"  {update['fps']} (间隔: {update['interval']:.3f}s)")
        
        return True
        
    except Exception as e:
        print(f"测试过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_gui_fps_updates()
    if success:
        print("\n✅ GUI FPS更新测试完成")
    else:
        print("\n❌ GUI FPS更新测试失败")