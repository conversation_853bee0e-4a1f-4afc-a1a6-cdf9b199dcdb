# -*- coding: utf-8 -*-
# @Description : 测试supervision库兼容性修复
# @Date : 2025年6月21日

import sys
import os
import cv2
import numpy as np
from ultralytics import YOLO
import supervision as sv

def test_supervision_compatibility():
    """测试supervision库兼容性"""
    print("🔍 测试Supervision库兼容性修复")
    print("="*60)
    
    # 检查supervision版本
    try:
        version = sv.__version__
        print(f"📦 Supervision版本: {version}")
    except AttributeError:
        print("📦 Supervision版本: 未知")
    
    # 检查可用的API方法
    has_from_ultralytics = hasattr(sv.Detections, 'from_ultralytics')
    has_from_yolov8 = hasattr(sv.Detections, 'from_yolov8')
    
    print(f"🔧 支持from_ultralytics: {has_from_ultralytics}")
    print(f"🔧 支持from_yolov8: {has_from_yolov8}")
    
    return has_from_ultralytics, has_from_yolov8

def create_detections_from_result(result):
    """兼容性函数测试"""
    try:
        return sv.Detections.from_ultralytics(result)
    except AttributeError:
        try:
            return sv.Detections.from_yolov8(result)
        except AttributeError:
            if result.boxes is not None:
                boxes = result.boxes.xyxy.cpu().numpy()
                confidences = result.boxes.conf.cpu().numpy()
                class_ids = result.boxes.cls.cpu().numpy().astype(int)
                track_ids = result.boxes.id.cpu().numpy().astype(int) if result.boxes.id is not None else None
                
                return sv.Detections(
                    xyxy=boxes,
                    confidence=confidences,
                    class_id=class_ids,
                    tracker_id=track_ids
                )
            else:
                return sv.Detections.empty()

def test_yolo_detection():
    """测试YOLO检测和supervision兼容性"""
    print("\n🎯 测试YOLO检测和Supervision兼容性")
    print("="*60)
    
    try:
        # 加载YOLO模型
        print("📥 加载YOLO模型...")
        model = YOLO('yolov8n.pt')  # 会自动下载
        print("✅ YOLO模型加载成功")
        
        # 创建测试图像
        print("🖼️ 创建测试图像...")
        test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
        
        # 进行检测
        print("🔍 进行目标检测...")
        results = model(test_image, verbose=False)
        result = results[0]
        
        print(f"📊 检测结果: {len(result.boxes) if result.boxes is not None else 0} 个目标")
        
        # 测试兼容性函数
        print("🔧 测试兼容性函数...")
        detections = create_detections_from_result(result)
        
        print(f"✅ 兼容性函数成功创建检测结果")
        print(f"   检测对象数量: {len(detections)}")
        print(f"   检测对象类型: {type(detections)}")
        
        # 测试检测结果的属性
        if len(detections) > 0:
            print(f"   边界框形状: {detections.xyxy.shape}")
            print(f"   置信度形状: {detections.confidence.shape}")
            print(f"   类别ID形状: {detections.class_id.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_file_imports():
    """测试文件导入是否正常"""
    print("\n📁 测试文件导入")
    print("="*60)
    
    test_files = [
        'classes/yolo.py',
        'app.py',
        'main.py'
    ]
    
    success_count = 0
    
    for file_path in test_files:
        if os.path.exists(file_path):
            try:
                # 检查文件中是否包含兼容性代码
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                has_compat_function = 'create_detections_from_result' in content
                has_old_api = 'from_yolov8' in content
                has_new_api = 'from_ultralytics' in content
                
                print(f"📄 {file_path}:")
                print(f"   兼容性函数: {'✅' if has_compat_function else '❌'}")
                print(f"   包含新API: {'✅' if has_new_api else '❌'}")
                print(f"   包含旧API: {'✅' if has_old_api else '❌'}")
                
                if has_compat_function:
                    success_count += 1
                    
            except Exception as e:
                print(f"❌ 读取文件失败: {e}")
        else:
            print(f"❌ 文件不存在: {file_path}")
    
    print(f"\n📊 文件修复状态: {success_count}/{len(test_files)} 个文件已修复")
    return success_count == len(test_files)

def test_collision_detection_import():
    """测试碰撞检测对话框导入"""
    print("\n🚗 测试碰撞检测功能导入")
    print("="*60)
    
    try:
        from ui.dialog.collision_detection_dialog import CollisionDetectionDialog
        print("✅ 碰撞检测对话框导入成功")
        
        # 测试算法类
        from ui.dialog.collision_detection_dialog import CollisionDetectionAlgorithm
        algorithm = CollisionDetectionAlgorithm()
        print("✅ 碰撞检测算法类创建成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 创建失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 Supervision库兼容性修复测试")
    print("="*80)
    
    # 测试supervision兼容性
    has_ultralytics, has_yolov8 = test_supervision_compatibility()
    
    # 测试YOLO检测
    yolo_success = test_yolo_detection()
    
    # 测试文件修复
    files_success = test_file_imports()
    
    # 测试碰撞检测导入
    collision_success = test_collision_detection_import()
    
    # 总结
    print(f"\n🎉 测试完成总结")
    print("="*80)
    print(f"📦 Supervision兼容性: {'✅ 正常' if (has_ultralytics or has_yolov8) else '❌ 异常'}")
    print(f"🎯 YOLO检测功能: {'✅ 正常' if yolo_success else '❌ 异常'}")
    print(f"📁 文件修复状态: {'✅ 完成' if files_success else '❌ 未完成'}")
    print(f"🚗 碰撞检测功能: {'✅ 正常' if collision_success else '❌ 异常'}")
    
    all_success = yolo_success and files_success and collision_success
    
    if all_success:
        print("\n🎊 所有测试通过！系统已修复并可以正常使用！")
        print("\n🚀 现在可以:")
        print("   1. 启动主程序: python main.py")
        print("   2. 测试碰撞检测: python test_collision_detection.py")
        print("   3. 使用GUI界面的碰撞检测功能")
    else:
        print("\n⚠️ 部分测试失败，请检查上述错误信息")
    
    return all_success

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        sys.exit(1)
