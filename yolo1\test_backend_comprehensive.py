# -*- coding: utf-8 -*-
"""
基于Yolov8与ByteTrack的高速公路智慧监控平台 - 后端功能综合测试
测试所有API端点的功能是否正常
"""

import requests
import json
import time
import os
from datetime import datetime, timedelta

class BackendTester:
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.api_base = f"{base_url}/api/v1"
        self.token = None
        self.headers = {'Content-Type': 'application/json'}
        self.test_results = []
        
    def log_test(self, test_name, success, message="", response_data=None):
        """记录测试结果"""
        result = {
            'test_name': test_name,
            'success': success,
            'message': message,
            'timestamp': datetime.now().isoformat(),
            'response_data': response_data
        }
        self.test_results.append(result)
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{status} - {test_name}: {message}")
        
    def test_server_connection(self):
        """测试服务器连接"""
        try:
            response = requests.get(f"{self.base_url}/", timeout=5)
            if response.status_code == 200:
                self.log_test("服务器连接", True, "服务器响应正常")
                return True
            else:
                self.log_test("服务器连接", False, f"服务器响应异常: {response.status_code}")
                return False
        except Exception as e:
            self.log_test("服务器连接", False, f"连接失败: {str(e)}")
            return False
    
    def test_auth_login(self):
        """测试用户登录"""
        try:
            login_data = {
                "username": "admin",
                "password": "admin123"
            }
            
            response = requests.post(
                f"{self.api_base}/auth/login",
                json=login_data,
                headers=self.headers,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success') and 'token' in data.get('data', {}):
                    self.token = data['data']['token']
                    self.headers['Authorization'] = f"Bearer {self.token}"
                    self.log_test("用户登录", True, "登录成功，获取到令牌")
                    return True
                else:
                    self.log_test("用户登录", False, "登录响应格式异常")
                    return False
            else:
                self.log_test("用户登录", False, f"登录失败: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("用户登录", False, f"登录异常: {str(e)}")
            return False
    
    def test_system_info(self):
        """测试系统信息获取"""
        try:
            response = requests.get(
                f"{self.api_base}/system/info",
                headers=self.headers,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.log_test("系统信息", True, "系统信息获取成功")
                    return True
                else:
                    self.log_test("系统信息", False, "系统信息响应异常")
                    return False
            else:
                self.log_test("系统信息", False, f"获取失败: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("系统信息", False, f"获取异常: {str(e)}")
            return False
    
    def test_monitor_list(self):
        """测试监控点列表"""
        try:
            response = requests.get(
                f"{self.api_base}/monitor/list?page=1&page_size=10",
                headers=self.headers,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    monitors = data.get('data', {}).get('monitors', [])
                    self.log_test("监控点列表", True, f"获取到 {len(monitors)} 个监控点")
                    return True
                else:
                    self.log_test("监控点列表", False, "监控点列表响应异常")
                    return False
            else:
                self.log_test("监控点列表", False, f"获取失败: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("监控点列表", False, f"获取异常: {str(e)}")
            return False
    
    def test_realtime_status(self):
        """测试实时状态"""
        try:
            response = requests.get(
                f"{self.api_base}/realtime/status",
                headers=self.headers,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.log_test("实时状态", True, "实时状态获取成功")
                    return True
                else:
                    self.log_test("实时状态", False, "实时状态响应异常")
                    return False
            else:
                self.log_test("实时状态", False, f"获取失败: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("实时状态", False, f"获取异常: {str(e)}")
            return False
    
    def test_analysis_alarms(self):
        """测试警报分析"""
        try:
            response = requests.get(
                f"{self.api_base}/analysis/alarms?page=1&page_size=10",
                headers=self.headers,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    alarms = data.get('data', {}).get('alarms', [])
                    self.log_test("警报分析", True, f"获取到 {len(alarms)} 条警报记录")
                    return True
                else:
                    self.log_test("警报分析", False, "警报分析响应异常")
                    return False
            else:
                self.log_test("警报分析", False, f"获取失败: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("警报分析", False, f"获取异常: {str(e)}")
            return False
    
    def test_tracking_algorithms(self):
        """测试追踪算法"""
        try:
            response = requests.get(
                f"{self.api_base}/tracking/algorithms",
                headers=self.headers,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    algorithms = data.get('data', [])
                    self.log_test("追踪算法", True, f"获取到 {len(algorithms)} 个追踪算法")
                    return True
                else:
                    self.log_test("追踪算法", False, "追踪算法响应异常")
                    return False
            else:
                self.log_test("追踪算法", False, f"获取失败: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("追踪算法", False, f"获取异常: {str(e)}")
            return False
    
    def test_config_algorithm(self):
        """测试算法配置"""
        try:
            response = requests.get(
                f"{self.api_base}/config/algorithm",
                headers=self.headers,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    configs = data.get('data', [])
                    self.log_test("算法配置", True, f"获取到 {len(configs)} 个配置项")
                    return True
                else:
                    self.log_test("算法配置", False, "算法配置响应异常")
                    return False
            else:
                self.log_test("算法配置", False, f"获取失败: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("算法配置", False, f"获取异常: {str(e)}")
            return False
    
    def test_database_connection(self):
        """测试数据库连接"""
        try:
            response = requests.get(
                f"{self.api_base}/system/health",
                headers=self.headers,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    health_data = data.get('data', {})
                    db_status = health_data.get('database', {}).get('status')
                    if db_status == 'healthy':
                        self.log_test("数据库连接", True, "数据库连接正常")
                        return True
                    else:
                        self.log_test("数据库连接", False, f"数据库状态异常: {db_status}")
                        return False
                else:
                    self.log_test("数据库连接", False, "健康检查响应异常")
                    return False
            else:
                self.log_test("数据库连接", False, f"健康检查失败: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("数据库连接", False, f"检查异常: {str(e)}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("\n" + "="*60)
        print("🚀 开始后端功能综合测试")
        print("="*60)
        
        # 基础连接测试
        if not self.test_server_connection():
            print("\n❌ 服务器连接失败，无法继续测试")
            return False
        
        # 认证测试
        if not self.test_auth_login():
            print("\n❌ 用户认证失败，无法继续测试")
            return False
        
        # 功能模块测试
        test_functions = [
            self.test_system_info,
            self.test_database_connection,
            self.test_monitor_list,
            self.test_realtime_status,
            self.test_analysis_alarms,
            self.test_tracking_algorithms,
            self.test_config_algorithm
        ]
        
        for test_func in test_functions:
            test_func()
            time.sleep(0.5)  # 避免请求过于频繁
        
        # 生成测试报告
        self.generate_report()
        
        return True
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "="*60)
        print("📊 测试报告")
        print("="*60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests} ✅")
        print(f"失败: {failed_tests} ❌")
        print(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ 失败的测试:")
            for result in self.test_results:
                if not result['success']:
                    print(f"  - {result['test_name']}: {result['message']}")
        
        # 保存详细报告到文件
        report_file = f"backend_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump({
                'summary': {
                    'total_tests': total_tests,
                    'passed_tests': passed_tests,
                    'failed_tests': failed_tests,
                    'success_rate': (passed_tests/total_tests)*100
                },
                'test_results': self.test_results
            }, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 详细报告已保存到: {report_file}")

def main():
    """主函数"""
    # 可以修改为实际的后端服务地址
    backend_url = "http://localhost:5000"
    
    print("基于Yolov8与ByteTrack的高速公路智慧监控平台 - 后端功能测试")
    print(f"测试目标: {backend_url}")
    
    tester = BackendTester(backend_url)
    tester.run_all_tests()

if __name__ == "__main__":
    main()