#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复车辆碰撞检测对话框的黑色阴影问题
将黑色阴影改为浅蓝色阴影
"""

import os
import re

def fix_collision_dialog_shadow():
    """修复车辆碰撞检测对话框阴影问题"""
    print("正在修复车辆碰撞检测对话框阴影问题...")
    
    file_path = "ui/dialog/collision_detection_dialog.py"
    if not os.path.exists(file_path):
        print(f"错误：找不到文件 {file_path}")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找添加阴影效果的方法
    shadow_method_pattern = r'def add_shadow_effect\(self\):[^}]*?self\.setGraphicsEffect\(shadow\)'
    
    # 新的阴影效果方法
    new_shadow_method = '''def add_shadow_effect(self):
        """添加阴影效果 - 浅蓝色阴影"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(25)
        shadow.setColor(QColor(100, 180, 255, 35))  # 浅蓝色阴影
        shadow.setOffset(0, 8)
        self.setGraphicsEffect(shadow)'''
    
    # 替换阴影效果方法
    if re.search(shadow_method_pattern, content, re.DOTALL):
        new_content = re.sub(shadow_method_pattern, new_shadow_method, content, flags=re.DOTALL)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print("✅ 车辆碰撞检测对话框阴影已修改为浅蓝色")
        return True
    else:
        # 如果找不到现有的方法，尝试在文件末尾添加
        print("⚠️ 无法找到现有的阴影效果方法，尝试添加新方法...")
        
        # 检查文件是否已经包含add_shadow_effect方法的调用
        if "self.add_shadow_effect()" in content:
            # 在文件末尾添加方法
            with open(file_path, 'a', encoding='utf-8') as f:
                f.write("\n\n")
                f.write(new_shadow_method)
            
            print("✅ 已在文件末尾添加新的阴影效果方法")
            return True
        else:
            print("❌ 文件中没有调用add_shadow_effect方法，无法修复")
            return False

if __name__ == "__main__":
    fix_collision_dialog_shadow()