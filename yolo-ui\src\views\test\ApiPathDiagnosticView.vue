<template>
  <div class="api-path-diagnostic">
    <a-card title="API路径诊断工具">
      <a-alert type="error" show-icon style="margin-bottom: 16px;">
        <template #title>检测到404错误</template>
        <div>
          <p>所有API请求返回404 "资源不存在"，这表明API路径配置有问题</p>
          <p>需要确认后端的实际API路径配置</p>
        </div>
      </a-alert>
      
      <a-tabs v-model:active-key="activeTab" type="card">
        <!-- 路径测试 -->
        <a-tab-pane key="path-test" title="路径测试">
          <div class="test-section">
            <h3>测试不同的API基础路径</h3>
            
            <a-form layout="vertical">
              <a-form-item label="后端服务地址">
                <a-input v-model="baseUrl" placeholder="http://127.0.0.1:5501" />
              </a-form-item>
              
              <a-form-item label="API基础路径">
                <a-select v-model="apiBasePath" placeholder="选择或输入API基础路径">
                  <a-option value="/api/v1">标准路径: /api/v1</a-option>
                  <a-option value="/api">简化路径: /api</a-option>
                  <a-option value="">根路径: /</a-option>
                  <a-option value="/v1">版本路径: /v1</a-option>
                  <a-option value="/highway">自定义: /highway</a-option>
                </a-select>
              </a-form-item>
              
              <a-form-item>
                <a-space>
                  <a-button @click="testAllPaths" :loading="testing" type="primary">
                    测试所有可能路径
                  </a-button>
                  <a-button @click="testCurrentPath" :loading="singleTesting">
                    测试当前路径
                  </a-button>
                  <a-button @click="scanPorts">
                    扫描端口
                  </a-button>
                </a-space>
              </a-form-item>
            </a-form>
            
            <!-- 路径测试结果 -->
            <div class="test-results" v-if="pathResults.length > 0">
              <h4>路径测试结果：</h4>
              <div class="result-list">
                <div 
                  v-for="result in pathResults" 
                  :key="result.path"
                  class="result-item"
                  :class="{ 'success': result.success, 'failed': !result.success }"
                >
                  <div class="result-header">
                    <span class="path">{{ result.fullUrl }}</span>
                    <a-tag :color="result.success ? 'green' : 'red'">
                      {{ result.success ? '成功' : '失败' }}
                    </a-tag>
                    <span class="status">{{ result.status }}</span>
                  </div>
                  <div class="result-details" v-if="result.data">
                    <pre>{{ JSON.stringify(result.data, null, 2) }}</pre>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </a-tab-pane>

        <!-- 端点发现 -->
        <a-tab-pane key="endpoint-discovery" title="端点发现">
          <div class="test-section">
            <h3>自动发现API端点</h3>
            <p>尝试发现后端实际可用的API端点</p>
            
            <a-space direction="vertical" fill>
              <a-button @click="discoverEndpoints" :loading="discovering">
                开始端点发现
              </a-button>
              
              <a-button @click="testCommonEndpoints" :loading="commonTesting">
                测试常见端点
              </a-button>
              
              <a-button @click="testHealthEndpoints" :loading="healthTesting">
                测试健康检查端点
              </a-button>
            </a-space>
            
            <!-- 发现的端点 -->
            <div class="discovered-endpoints" v-if="discoveredEndpoints.length > 0">
              <h4>发现的可用端点：</h4>
              <a-list :data="discoveredEndpoints" size="small">
                <template #item="{ item }">
                  <a-list-item>
                    <div class="endpoint-item">
                      <span class="method">{{ item.method }}</span>
                      <span class="url">{{ item.url }}</span>
                      <a-tag :color="item.status < 400 ? 'green' : 'red'">
                        {{ item.status }}
                      </a-tag>
                      <span class="response">{{ item.response }}</span>
                    </div>
                  </a-list-item>
                </template>
              </a-list>
            </div>
          </div>
        </a-tab-pane>

        <!-- 后端配置检查 -->
        <a-tab-pane key="backend-check" title="后端配置检查">
          <div class="test-section">
            <h3>后端配置检查清单</h3>
            
            <a-alert type="info" show-icon style="margin-bottom: 16px;">
              <template #title>请将以下清单发给后端开发者</template>
            </a-alert>
            
            <div class="checklist">
              <h4>🔴 紧急问题确认</h4>
              <a-checkbox-group v-model="checkedItems" direction="vertical">
                <a-checkbox value="api-path">
                  API基础路径是否为 <code>/api/v1/</code>？
                </a-checkbox>
                <a-checkbox value="server-running">
                  服务器是否在 <code>http://127.0.0.1:5500</code> 运行？
                </a-checkbox>
                <a-checkbox value="cors-config">
                  是否配置了CORS允许 <code>http://localhost:3000</code>？
                </a-checkbox>
                <a-checkbox value="routes-registered">
                  所有API路由是否正确注册？
                </a-checkbox>
              </a-checkbox-group>
              
              <a-divider />
              
              <h4>🟡 配置检查命令</h4>
              <div class="command-list">
                <div class="command-item">
                  <h5>1. 检查服务状态</h5>
                  <pre class="command">curl http://127.0.0.1:5500/health</pre>
                </div>
                
                <div class="command-item">
                  <h5>2. 检查API文档</h5>
                  <pre class="command">curl http://127.0.0.1:5500/docs</pre>
                </div>
                
                <div class="command-item">
                  <h5>3. 测试登录API</h5>
                  <pre class="command">curl -X POST http://127.0.0.1:5501/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"123456"}'</pre>
                </div>
                
                <div class="command-item">
                  <h5>4. 检查端口占用</h5>
                  <pre class="command">netstat -an | findstr 5501</pre>
                </div>
              </div>
              
              <a-divider />
              
              <h4>📋 需要后端提供的信息</h4>
              <ul class="info-list">
                <li>完整的API端点列表</li>
                <li>实际的API基础路径</li>
                <li>服务器启动日志</li>
                <li>CORS配置代码</li>
                <li>路由注册代码</li>
                <li>确认服务运行在 http://127.0.0.1:5501</li>
              </ul>
              
              <a-button @click="generateBackendReport" type="primary">
                生成后端检查报告
              </a-button>
            </div>
          </div>
        </a-tab-pane>

        <!-- 修复建议 -->
        <a-tab-pane key="fix-suggestions" title="修复建议">
          <div class="test-section">
            <h3>常见问题修复建议</h3>
            
            <a-collapse>
              <a-collapse-item header="404错误 - API路径不匹配" key="404">
                <div class="fix-content">
                  <p><strong>问题</strong>：前端请求的路径与后端实际路径不匹配</p>
                  <p><strong>解决方案</strong>：</p>
                  <ol>
                    <li>确认后端API基础路径</li>
                    <li>检查路由注册是否正确</li>
                    <li>验证URL拼接逻辑</li>
                  </ol>
                  <pre class="code-block">
# FastAPI路由注册示例
from fastapi import FastAPI

app = FastAPI()

# 确保路由正确注册
@app.post("/api/v1/auth/login")
async def login(request: LoginRequest):
    # 登录逻辑
    pass

# 或使用路由器
from fastapi import APIRouter
router = APIRouter(prefix="/api/v1")
app.include_router(router)
                  </pre>
                </div>
              </a-collapse-item>
              
              <a-collapse-item header="CORS跨域问题" key="cors">
                <div class="fix-content">
                  <p><strong>问题</strong>：跨域请求被阻止</p>
                  <p><strong>解决方案</strong>：配置CORS中间件</p>
                  <pre class="code-block">
# FastAPI CORS配置
from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
                  </pre>
                </div>
              </a-collapse-item>
              
              <a-collapse-item header="服务器启动问题" key="server">
                <div class="fix-content">
                  <p><strong>问题</strong>：服务器未在正确的地址和端口启动</p>
                  <p><strong>解决方案</strong>：检查启动配置</p>
                  <pre class="code-block">
# 确保服务器在正确的地址启动
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=5501)
                  </pre>
                </div>
              </a-collapse-item>
            </a-collapse>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { Message } from '@arco-design/web-vue'
import axios from 'axios'

const activeTab = ref('path-test')

// 测试状态
const testing = ref(false)
const singleTesting = ref(false)
const discovering = ref(false)
const commonTesting = ref(false)
const healthTesting = ref(false)

// 配置
const baseUrl = ref('http://127.0.0.1:5501')
const apiBasePath = ref('/api/v1')

// 结果
const pathResults = ref<any[]>([])
const discoveredEndpoints = ref<any[]>([])
const checkedItems = ref<string[]>([])

// 可能的API路径
const possiblePaths = [
  '/api/v1',
  '/api',
  '',
  '/v1',
  '/highway',
  '/highway/api',
  '/highway/api/v1'
]

// 常见端点
const commonEndpoints = [
  { method: 'GET', path: '/health' },
  { method: 'GET', path: '/docs' },
  { method: 'GET', path: '/openapi.json' },
  { method: 'POST', path: '/auth/login' },
  { method: 'GET', path: '/auth/profile' },
  { method: 'GET', path: '/monitor/list' }
]

// 测试单个路径
const testPath = async (basePath: string, endpoint: string = '/auth/login') => {
  const fullUrl = `${baseUrl.value}${basePath}${endpoint}`

  try {
    const response = await axios.post(fullUrl, {
      username: 'admin',
      password: '123456'
    }, {
      timeout: 3000,
      headers: {
        'Content-Type': 'application/json'
      }
    })

    return {
      path: basePath,
      fullUrl,
      success: true,
      status: response.status,
      data: response.data
    }
  } catch (error: any) {
    return {
      path: basePath,
      fullUrl,
      success: false,
      status: error.response?.status || 'Network Error',
      data: error.response?.data || { error: error.message }
    }
  }
}

// 测试所有可能的路径
const testAllPaths = async () => {
  testing.value = true
  pathResults.value = []

  Message.info('开始测试所有可能的API路径...')

  for (const path of possiblePaths) {
    const result = await testPath(path)
    pathResults.value.push(result)

    // 添加延迟避免请求过于频繁
    await new Promise(resolve => setTimeout(resolve, 200))
  }

  testing.value = false

  const successCount = pathResults.value.filter(r => r.success).length
  if (successCount > 0) {
    Message.success(`发现 ${successCount} 个可用路径`)
  } else {
    Message.error('所有路径测试失败，请检查后端服务')
  }
}

// 测试当前路径
const testCurrentPath = async () => {
  singleTesting.value = true

  const result = await testPath(apiBasePath.value)
  pathResults.value = [result]

  singleTesting.value = false

  if (result.success) {
    Message.success('当前路径测试成功')
  } else {
    Message.error(`当前路径测试失败: ${result.status}`)
  }
}

// 扫描端口
const scanPorts = async () => {
  const ports = [5501, 5500, 8000, 8080, 3000, 5000, 9000]
  const results = []

  Message.info('开始扫描端口...')

  for (const port of ports) {
    try {
      const response = await axios.get(`http://127.0.0.1:${port}/health`, {
        timeout: 2000
      })
      results.push({
        port,
        status: 'active',
        response: response.data
      })
    } catch (error) {
      results.push({
        port,
        status: 'inactive',
        response: null
      })
    }
  }

  console.log('端口扫描结果:', results)
  Message.info(`扫描完成，发现 ${results.filter(r => r.status === 'active').length} 个活跃端口`)
}

// 发现端点
const discoverEndpoints = async () => {
  discovering.value = true
  discoveredEndpoints.value = []

  Message.info('开始发现API端点...')

  // 测试不同基础路径下的常见端点
  for (const basePath of possiblePaths) {
    for (const endpoint of commonEndpoints) {
      const fullUrl = `${baseUrl.value}${basePath}${endpoint.path}`

      try {
        const config = {
          method: endpoint.method.toLowerCase(),
          url: fullUrl,
          timeout: 2000,
          headers: {
            'Content-Type': 'application/json'
          }
        }

        if (endpoint.method === 'POST') {
          config.data = { username: 'test', password: 'test' }
        }

        const response = await axios(config)

        discoveredEndpoints.value.push({
          method: endpoint.method,
          url: fullUrl,
          status: response.status,
          response: JSON.stringify(response.data).substring(0, 100) + '...'
        })
      } catch (error: any) {
        if (error.response && error.response.status !== 404) {
          discoveredEndpoints.value.push({
            method: endpoint.method,
            url: fullUrl,
            status: error.response.status,
            response: error.response.data?.message || error.message
          })
        }
      }

      // 添加延迟
      await new Promise(resolve => setTimeout(resolve, 100))
    }
  }

  discovering.value = false
  Message.success(`发现 ${discoveredEndpoints.value.length} 个端点`)
}

// 测试常见端点
const testCommonEndpoints = async () => {
  commonTesting.value = true

  const testEndpoints = [
    `${baseUrl.value}/health`,
    `${baseUrl.value}/docs`,
    `${baseUrl.value}/api/docs`,
    `${baseUrl.value}/api/v1/docs`,
    `${baseUrl.value}/openapi.json`
  ]

  for (const url of testEndpoints) {
    try {
      const response = await axios.get(url, { timeout: 2000 })
      console.log(`${url}: 成功`, response.data)
    } catch (error: any) {
      console.log(`${url}: 失败`, error.response?.status)
    }
  }

  commonTesting.value = false
  Message.info('常见端点测试完成，请查看控制台')
}

// 测试健康检查端点
const testHealthEndpoints = async () => {
  healthTesting.value = true

  const healthUrls = [
    `${baseUrl.value}/health`,
    `${baseUrl.value}/api/health`,
    `${baseUrl.value}/api/v1/health`,
    `${baseUrl.value}/ping`,
    `${baseUrl.value}/status`
  ]

  for (const url of healthUrls) {
    try {
      const response = await axios.get(url, { timeout: 2000 })
      Message.success(`健康检查成功: ${url}`)
      console.log(url, response.data)
      break
    } catch (error: any) {
      console.log(`${url}: ${error.response?.status || error.message}`)
    }
  }

  healthTesting.value = false
}

// 生成后端检查报告
const generateBackendReport = () => {
  const report = {
    timestamp: new Date().toISOString(),
    frontend_config: {
      base_url: baseUrl.value,
      api_base_path: apiBasePath.value,
      proxy_target: 'http://127.0.0.1:5500'
    },
    test_results: pathResults.value,
    discovered_endpoints: discoveredEndpoints.value,
    checklist: checkedItems.value,
    recommendations: [
      '确认后端服务在 http://127.0.0.1:5500 运行',
      '确认API基础路径为 /api/v1/',
      '配置CORS允许 http://localhost:3000',
      '检查所有路由是否正确注册',
      '添加健康检查端点 /health'
    ]
  }

  const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `backend-diagnostic-report-${Date.now()}.json`
  link.click()
  URL.revokeObjectURL(url)

  Message.success('后端检查报告已生成')
}
</script>

<style scoped>
.api-path-diagnostic {
  padding: 20px;
}

.test-section {
  margin-bottom: 24px;
}

.test-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.test-results {
  margin-top: 16px;
}

.result-list {
  margin-top: 12px;
}

.result-item {
  margin-bottom: 12px;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #e5e6eb;
}

.result-item.success {
  background: #f6ffed;
  border-color: #b7eb8f;
}

.result-item.failed {
  background: #fff2f0;
  border-color: #ffccc7;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.path {
  font-family: 'Courier New', monospace;
  font-weight: 600;
}

.status {
  font-size: 12px;
  color: #86909c;
}

.result-details pre {
  margin: 0;
  font-size: 12px;
  max-height: 150px;
  overflow-y: auto;
  background: #f7f8fa;
  padding: 8px;
  border-radius: 4px;
}

.discovered-endpoints {
  margin-top: 16px;
}

.endpoint-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.method {
  font-weight: 600;
  min-width: 60px;
}

.url {
  font-family: 'Courier New', monospace;
  flex: 1;
}

.response {
  font-size: 12px;
  color: #86909c;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.checklist {
  padding: 16px;
  background: #f7f8fa;
  border-radius: 8px;
}

.command-list {
  margin: 16px 0;
}

.command-item {
  margin-bottom: 16px;
}

.command-item h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
}

.command {
  background: #f7f8fa;
  padding: 8px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  margin: 0;
  border: 1px solid #e5e6eb;
}

.info-list {
  padding-left: 20px;
}

.info-list li {
  margin: 8px 0;
}

.fix-content {
  padding: 16px;
}

.fix-content ol {
  padding-left: 20px;
}

.fix-content li {
  margin: 8px 0;
}

.code-block {
  background: #f7f8fa;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #e5e6eb;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  margin: 12px 0;
  overflow-x: auto;
}
</style>
