#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的后端API测试脚本
用于快速验证后端服务状态
"""

import requests
import json

def test_backend():
    """测试后端服务"""
    base_url = "http://127.0.0.1:5500"
    
    print("🚀 高速公路智能监控系统 - 后端API测试")
    print("=" * 60)
    
    # 1. 测试基础连接
    print("\n1️⃣ 测试基础连接...")
    try:
        response = requests.get(base_url, timeout=5)
        if response.status_code == 200:
            print("✅ 基础连接成功")
            data = response.json()
            print(f"   系统状态: {data.get('status', 'unknown')}")
            print(f"   版本: {data.get('version', 'unknown')}")
        else:
            print(f"❌ 基础连接失败: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败: 后端服务未启动")
        print("\n🔧 请启动后端服务:")
        print("   cd C:\\Users\\<USER>\\Desktop\\qweru\\yolo")
        print("   python backend/app_enhanced.py")
        return False
    except Exception as e:
        print(f"❌ 连接异常: {e}")
        return False
    
    # 2. 测试API文档
    print("\n2️⃣ 测试API文档...")
    try:
        response = requests.get(f"{base_url}/api/v1/docs", timeout=5)
        if response.status_code == 200:
            print("✅ API文档接口正常")
            data = response.json()
            endpoints = data.get('endpoints', {})
            print(f"   可用模块: {list(endpoints.keys())}")
        else:
            print(f"❌ API文档接口失败: {response.status_code}")
    except Exception as e:
        print(f"❌ API文档接口异常: {e}")
    
    # 3. 测试登录接口
    print("\n3️⃣ 测试登录接口...")
    try:
        login_data = {"username": "admin", "password": "123456"}
        response = requests.post(f"{base_url}/api/v1/auth/login", json=login_data, timeout=5)
        if response.status_code == 200:
            print("✅ 登录接口正常")
            data = response.json()
            token = data.get('data', {}).get('token')
            if token:
                print(f"   获取Token: {token[:30]}...")
                return test_protected_apis(base_url, token)
            else:
                print("⚠️ 登录成功但未获取到Token")
        else:
            print(f"❌ 登录失败: {response.status_code}")
            print(f"   响应: {response.text}")
    except Exception as e:
        print(f"❌ 登录接口异常: {e}")
    
    return False

def test_protected_apis(base_url, token):
    """测试需要认证的API"""
    headers = {"Authorization": f"Bearer {token}"}
    
    # 4. 测试用户信息
    print("\n4️⃣ 测试用户信息接口...")
    try:
        response = requests.get(f"{base_url}/api/v1/auth/profile", headers=headers, timeout=5)
        if response.status_code == 200:
            print("✅ 用户信息接口正常")
            data = response.json()
            user = data.get('data', {})
            print(f"   用户: {user.get('username', 'N/A')}")
            print(f"   角色: {user.get('grade', 'N/A')}")
        else:
            print(f"❌ 用户信息接口失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 用户信息接口异常: {e}")
    
    # 5. 测试监控点列表
    print("\n5️⃣ 测试监控点列表接口...")
    try:
        response = requests.get(f"{base_url}/api/v1/monitor/list", headers=headers, timeout=5)
        if response.status_code == 200:
            print("✅ 监控点列表接口正常")
            data = response.json()
            monitors = data.get('data', {}).get('monitors', [])
            print(f"   监控点数量: {len(monitors)}")
            if monitors:
                print(f"   第一个监控点: {monitors[0].get('name', 'N/A')}")
        else:
            print(f"❌ 监控点列表接口失败: {response.status_code}")
            print(f"   响应: {response.text}")
    except Exception as e:
        print(f"❌ 监控点列表接口异常: {e}")
    
    # 6. 测试概览统计
    print("\n6️⃣ 测试概览统计接口...")
    try:
        response = requests.get(f"{base_url}/api/v1/analysis/statistics/overview", headers=headers, timeout=5)
        if response.status_code == 200:
            print("✅ 概览统计接口正常")
            data = response.json()
            stats = data.get('data', {})
            print(f"   监控点数量: {stats.get('monitor_count', 'N/A')}")
            print(f"   活跃监控点: {stats.get('active_monitor_count', 'N/A')}")
            print(f"   总警报数: {stats.get('total_alarms', 'N/A')}")
        else:
            print(f"❌ 概览统计接口失败: {response.status_code}")
            print(f"   响应: {response.text}")
    except Exception as e:
        print(f"❌ 概览统计接口异常: {e}")
    
    return True

def main():
    """主函数"""
    success = test_backend()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 后端API测试完成 - 服务运行正常")
        print("✅ 可以开始前端开发和测试")
    else:
        print("⚠️ 后端API测试发现问题")
        print("🔧 请检查:")
        print("   1. 后端服务是否启动")
        print("   2. 数据库是否正确配置")
        print("   3. 端口5500是否被占用")
    print("=" * 60)

if __name__ == "__main__":
    main()
