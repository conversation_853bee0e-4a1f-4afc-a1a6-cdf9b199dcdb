# -*- coding: utf-8 -*-
"""
违规检测器类
"""

import cv2
import numpy as np
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple

class ViolationDetector:
    """违规检测器类"""
    
    def __init__(self):
        self.speed_limit = 60  # 默认速度限制 km/h
        self.enable_speeding = True
        self.enable_wrong_way = True
        self.enable_lane_change = True
        self.enable_illegal_parking = True
        
        # 阈值设置
        self.speeding_threshold = 1.2  # 超速阈值倍数
        self.wrong_way_threshold = 0.8  # 逆行检测阈值
        self.lane_change_threshold = 0.5  # 变道检测阈值
        self.parking_time_threshold = 30  # 违停时间阈值(秒)
        
    def configure(self, config: Dict[str, Any]):
        """配置违规检测参数"""
        self.speed_limit = config.get('speed_limit', self.speed_limit)
        self.enable_speeding = config.get('enable_speeding', self.enable_speeding)
        self.enable_wrong_way = config.get('enable_wrong_way', self.enable_wrong_way)
        self.enable_lane_change = config.get('enable_lane_change', self.enable_lane_change)
        self.enable_illegal_parking = config.get('enable_illegal_parking', self.enable_illegal_parking)
        
        self.speeding_threshold = config.get('speeding_threshold', self.speeding_threshold)
        self.wrong_way_threshold = config.get('wrong_way_threshold', self.wrong_way_threshold)
        self.lane_change_threshold = config.get('lane_change_threshold', self.lane_change_threshold)
        self.parking_time_threshold = config.get('parking_time_threshold', self.parking_time_threshold)
    
    def detect_speeding(self, vehicle_speed: float) -> bool:
        """检测超速违规"""
        if not self.enable_speeding:
            return False
        return vehicle_speed > self.speed_limit * self.speeding_threshold
    
    def detect_wrong_way(self, vehicle_direction: Tuple[float, float], 
                        road_direction: Tuple[float, float]) -> bool:
        """检测逆行违规"""
        if not self.enable_wrong_way:
            return False
        
        # 计算方向向量的点积
        dot_product = (vehicle_direction[0] * road_direction[0] + 
                      vehicle_direction[1] * road_direction[1])
        
        # 归一化
        vehicle_norm = np.sqrt(vehicle_direction[0]**2 + vehicle_direction[1]**2)
        road_norm = np.sqrt(road_direction[0]**2 + road_direction[1]**2)
        
        if vehicle_norm == 0 or road_norm == 0:
            return False
        
        cos_angle = dot_product / (vehicle_norm * road_norm)
        
        # 如果夹角大于阈值，认为是逆行
        return cos_angle < -self.wrong_way_threshold
    
    def detect_lane_change(self, vehicle_trajectory: List[Tuple[float, float]], 
                          lane_boundaries: List[Tuple[float, float]]) -> bool:
        """检测违规变道"""
        if not self.enable_lane_change or len(vehicle_trajectory) < 2:
            return False
        
        # 简化的变道检测逻辑
        # 检查车辆轨迹是否跨越车道边界
        for i in range(1, len(vehicle_trajectory)):
            prev_pos = vehicle_trajectory[i-1]
            curr_pos = vehicle_trajectory[i]
            
            # 检查是否跨越车道线
            for boundary in lane_boundaries:
                if self._line_intersect(prev_pos, curr_pos, boundary):
                    return True
        
        return False
    
    def detect_illegal_parking(self, vehicle_stationary_time: float) -> bool:
        """检测违规停车"""
        if not self.enable_illegal_parking:
            return False
        return vehicle_stationary_time > self.parking_time_threshold
    
    def _line_intersect(self, p1: Tuple[float, float], p2: Tuple[float, float], 
                       boundary: Tuple[float, float]) -> bool:
        """检查线段是否与边界相交"""
        # 简化的线段相交检测
        # 这里可以实现更复杂的几何算法
        return False
    
    def detect_violations(self, vehicle_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """综合检测所有违规行为"""
        violations = []
        
        # 超速检测
        if 'speed' in vehicle_data:
            if self.detect_speeding(vehicle_data['speed']):
                violations.append({
                    'type': 'speeding',
                    'description': f"超速行驶，当前速度: {vehicle_data['speed']:.1f} km/h，限速: {self.speed_limit} km/h",
                    'severity': 'high',
                    'timestamp': datetime.now().isoformat()
                })
        
        # 逆行检测
        if 'direction' in vehicle_data and 'road_direction' in vehicle_data:
            if self.detect_wrong_way(vehicle_data['direction'], vehicle_data['road_direction']):
                violations.append({
                    'type': 'wrong_way',
                    'description': '逆向行驶',
                    'severity': 'critical',
                    'timestamp': datetime.now().isoformat()
                })
        
        # 违规变道检测
        if 'trajectory' in vehicle_data and 'lane_boundaries' in vehicle_data:
            if self.detect_lane_change(vehicle_data['trajectory'], vehicle_data['lane_boundaries']):
                violations.append({
                    'type': 'illegal_lane_change',
                    'description': '违规变道',
                    'severity': 'medium',
                    'timestamp': datetime.now().isoformat()
                })
        
        # 违规停车检测
        if 'stationary_time' in vehicle_data:
            if self.detect_illegal_parking(vehicle_data['stationary_time']):
                violations.append({
                    'type': 'illegal_parking',
                    'description': f"违规停车，停车时间: {vehicle_data['stationary_time']:.1f} 秒",
                    'severity': 'medium',
                    'timestamp': datetime.now().isoformat()
                })
        
        return violations
    
    def get_config(self) -> Dict[str, Any]:
        """获取当前配置"""
        return {
            'speed_limit': self.speed_limit,
            'enable_speeding': self.enable_speeding,
            'enable_wrong_way': self.enable_wrong_way,
            'enable_lane_change': self.enable_lane_change,
            'enable_illegal_parking': self.enable_illegal_parking,
            'speeding_threshold': self.speeding_threshold,
            'wrong_way_threshold': self.wrong_way_threshold,
            'lane_change_threshold': self.lane_change_threshold,
            'parking_time_threshold': self.parking_time_threshold
        }