<template>
  <div class="accident-disabled">
    <a-result
      status="warning"
      title="事故检测功能暂时禁用"
      sub-title="为了系统稳定性，事故检测功能已暂时禁用，正在进行技术修复"
    >
      <template #extra>
        <a-space direction="vertical" size="large">
          <a-alert type="warning" show-icon>
            <template #title>功能状态说明</template>
            <div>
              <p>• 事故检测功能因技术问题暂时禁用</p>
              <p>• 其他系统功能正常运行</p>
              <p>• 预计修复时间：技术团队正在处理中</p>
              <p>• 如有紧急需求，请联系系统管理员</p>
            </div>
          </a-alert>
          
          <a-space>
            <a-button type="primary" @click="goToDashboard">
              返回仪表板
            </a-button>
            <a-button @click="goToMonitor">
              监控管理
            </a-button>
            <a-button @click="goToDetection">
              检测中心
            </a-button>
          </a-space>
          
          <!-- 开发环境显示技术信息 -->
          <div v-if="isDev" class="dev-info">
            <a-divider />
            <h3>开发者信息</h3>
            <a-descriptions :column="1" size="small">
              <a-descriptions-item label="问题原因">
                事故检测页面的API调用导致页面卡死，影响整个应用的导航
              </a-descriptions-item>
              <a-descriptions-item label="临时方案">
                使用此禁用页面替代原事故检测页面，确保系统稳定运行
              </a-descriptions-item>
              <a-descriptions-item label="修复进度">
                已创建数据适配器和诊断工具，需要后端API配合调试
              </a-descriptions-item>
            </a-descriptions>
            
            <a-space style="margin-top: 16px;">
              <a-button @click="goToApiTest" type="outline">
                API诊断工具
              </a-button>
              <a-button @click="goToAdapterTest" type="outline">
                数据适配测试
              </a-button>
              <a-button @click="enableOriginalPage" status="danger">
                启用原页面（风险）
              </a-button>
            </a-space>
          </div>
        </a-space>
      </template>
    </a-result>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { Modal, Message } from '@arco-design/web-vue'

const router = useRouter()
const isDev = import.meta.env.DEV

// 导航到其他页面
const goToDashboard = () => {
  router.push('/dashboard')
}

const goToMonitor = () => {
  router.push('/monitor')
}

const goToDetection = () => {
  router.push('/detection')
}

const goToApiTest = () => {
  router.push('/accident-api-test')
}

const goToAdapterTest = () => {
  router.push('/data-adapter-test')
}

// 启用原页面（开发环境）
const enableOriginalPage = () => {
  Modal.confirm({
    title: '风险警告',
    content: '启用原事故检测页面可能导致系统卡死，确定要继续吗？',
    okText: '确定启用',
    cancelText: '取消',
    onOk: () => {
      // 这里可以通过修改路由或其他方式启用原页面
      Message.warning('请手动修改路由配置以启用原页面')
      // 或者直接跳转到原页面（风险操作）
      // window.location.href = '/accident-original'
    }
  })
}
</script>

<style scoped>
.accident-disabled {
  padding: 40px 20px;
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dev-info {
  max-width: 600px;
  text-align: left;
  padding: 20px;
  background: #f7f8fa;
  border-radius: 8px;
  border: 1px solid #e5e6eb;
}

.dev-info h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1d2129;
}
</style>
