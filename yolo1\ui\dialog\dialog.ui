<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Form</class>
 <widget class="QWidget" name="Form">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>744</width>
    <height>41</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>0</width>
    <height>40</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>16777215</width>
    <height>41</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="windowIcon">
   <iconset>
    <normaloff>:/img/icon/实时视频流解析.png</normaloff>:/img/icon/实时视频流解析.png</iconset>
  </property>
  <property name="styleSheet">
   <string notr="true">#Form{
background:qlineargradient(x0:0, y0:1, x1:1, y1:1,stop:0.4  rgb(48, 167, 217), stop:1 rgb(5,150,229))
}</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout">
   <property name="topMargin">
    <number>5</number>
   </property>
   <property name="bottomMargin">
    <number>5</number>
   </property>
   <item>
    <widget class="QLabel" name="label">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>30</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel{font-family: &quot;Microsoft YaHei&quot;;
font-size: 18px;
font-weight: bold;
color:white;}</string>
     </property>
     <property name="text">
      <string>rtsp address:</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QLineEdit" name="rtspEdit">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>31</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: rgb(207, 207, 207);
	background-color: #f7f7f7;
	border: none;
	outline: none;
	border-radius: 5px; /* 边框圆角 */
	padding-left: 12px; /* 文本距离左边界有5px */
font-family: &quot;Microsoft YaHei&quot;;


</string>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QPushButton" name="rtspButton">
     <property name="styleSheet">
      <string notr="true">QPushButton{font-family: &quot;Microsoft YaHei&quot;;
width:70px;
font-size: 18px;
font-weight: bold;
color:white;
text-align: center center;
padding: 5px;
padding-bottom: 4px;
border-style: solid;
border-width: 0px;
border-color: rgba(144, 0, 255, 0.8);
border-radius: 3px;
background-color: rgba(255,255,255,50);
}

QPushButton:focus{outline: none;}

QPushButton::pressed{font-family: &quot;Microsoft YaHei&quot;;
                     font-size: 16px;
                     font-weight: bold;
                     color:rgb(200,200,200);
                     text-align: center center;
                     padding-left: 5px;
                     padding-right: 5px;
                     padding-top: 4px;
                     padding-bottom: 4px;
                     border-style: solid;
                     border-width: 0px;
                     border-color: rgba(255, 255, 255, 255);
                     border-radius: 3px;
                     background-color:  rgba(255,255,255,150);}

QPushButton::hover {
border-style: solid;
border-width: 0px;
border-radius: 3px;
background-color: rgba(255,255,255,70);
}</string>
     </property>
     <property name="text">
      <string>确认</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QPushButton" name="pushButton">
     <property name="styleSheet">
      <string notr="true">QPushButton{font-family: &quot;Microsoft YaHei&quot;;
width:70px;
font-size: 18px;
font-weight: bold;
color:white;
text-align: center center;
padding: 5px;
padding-bottom: 4px;
border-style: solid;
border-width: 0px;
border-color: rgba(144, 0, 255, 0.8);
border-radius: 3px;
background-color: rgba(255,255,255,50);
}

QPushButton:focus{outline: none;}

QPushButton::pressed{font-family: &quot;Microsoft YaHei&quot;;
                     font-size: 16px;
                     font-weight: bold;
                     color:rgb(200,200,200);
                     text-align: center center;
                     padding-left: 5px;
                     padding-right: 5px;
                     padding-top: 4px;
                     padding-bottom: 4px;
                     border-style: solid;
                     border-width: 0px;
                     border-color: rgba(255, 255, 255, 255);
                     border-radius: 3px;
                     background-color:  rgba(255,255,255,150);}

QPushButton::hover {
border-style: solid;
border-width: 0px;
border-radius: 3px;
background-color: rgba(255,255,255,70);
}</string>
     </property>
     <property name="text">
      <string>取消</string>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
