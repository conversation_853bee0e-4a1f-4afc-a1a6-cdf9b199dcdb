#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库连接测试脚本
用于验证数据库配置是否正确
"""

import os
import sys
import pymysql
from dotenv import load_dotenv

def test_database_connection():
    """测试数据库连接"""
    print("🔍 测试数据库连接...")
    
    # 加载环境变量
    load_dotenv(override=True, dotenv_path='config/end-back.env')
    
    # 获取数据库配置
    config = {
        'host': os.getenv('MYSQL_HOST', '127.0.0.1'),
        'port': int(os.getenv('MYSQL_PORT', 3306)),
        'user': os.getenv('MYSQL_USER', os.getenv('MYSQL_user', 'root')),
        'password': os.getenv('MYSQL_PASSWORD', os.getenv('MYSQL_password', '123456')),
        'database': os.getenv('MYSQL_DATABASE', os.getenv('MYSQL_db', 'yolo')),
        'charset': os.getenv('MYSQL_CHARSET', os.getenv('MYSQL_charset', 'utf8mb4')),
        'autocommit': True,
        'cursorclass': pymysql.cursors.DictCursor
    }
    
    print(f"📋 数据库配置:")
    print(f"   主机: {config['host']}")
    print(f"   端口: {config['port']}")
    print(f"   用户: {config['user']}")
    print(f"   密码: {'*' * len(config['password'])}")
    print(f"   数据库: {config['database']}")
    print(f"   字符集: {config['charset']}")
    
    try:
        # 尝试连接数据库
        print("\n🔗 尝试连接数据库...")
        connection = pymysql.connect(**config)
        print("✅ 数据库连接成功!")
        
        # 测试查询
        with connection.cursor() as cursor:
            # 检查数据库是否存在
            cursor.execute("SELECT DATABASE() as current_db")
            result = cursor.fetchone()
            print(f"✅ 当前数据库: {result['current_db']}")
            
            # 检查表数量
            cursor.execute("""
                SELECT COUNT(*) as table_count 
                FROM information_schema.tables 
                WHERE table_schema = %s
            """, (config['database'],))
            result = cursor.fetchone()
            table_count = result['table_count']
            print(f"✅ 数据库表数量: {table_count}")
            
            # 检查用户表
            cursor.execute("SELECT COUNT(*) as user_count FROM user")
            result = cursor.fetchone()
            user_count = result['user_count']
            print(f"✅ 用户数量: {user_count}")
            
            # 检查监控点表
            cursor.execute("SELECT COUNT(*) as monitor_count FROM monitor")
            result = cursor.fetchone()
            monitor_count = result['monitor_count']
            print(f"✅ 监控点数量: {monitor_count}")
            
            # 列出所有表
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            table_names = [list(table.values())[0] for table in tables]
            print(f"✅ 数据库表列表: {', '.join(table_names)}")
        
        connection.close()
        print("\n🎉 数据库连接测试成功!")
        return True
        
    except pymysql.Error as e:
        print(f"\n❌ 数据库连接失败: {e}")
        print("\n🔧 可能的解决方案:")
        print("   1. 检查MySQL服务是否启动: net start mysql80")
        print("   2. 检查用户名和密码是否正确")
        print("   3. 检查数据库是否存在: mysql -u root -p123456 -e 'SHOW DATABASES;'")
        print("   4. 检查端口是否正确")
        return False
        
    except Exception as e:
        print(f"\n❌ 连接异常: {e}")
        return False

def test_backend_database_import():
    """测试后端数据库模块导入"""
    print("\n🔍 测试后端数据库模块...")
    
    try:
        # 添加backend目录到Python路径
        backend_path = os.path.join(os.getcwd(), 'backend')
        if backend_path not in sys.path:
            sys.path.insert(0, backend_path)
        
        # 导入数据库模块
        from utils.database import DatabaseManager, get_db_connection
        print("✅ 数据库模块导入成功")
        
        # 测试数据库管理器
        db_manager = DatabaseManager()
        print("✅ 数据库管理器创建成功")
        
        # 测试连接
        connection = db_manager.get_connection()
        print("✅ 数据库连接获取成功")
        
        # 测试上下文管理器
        with get_db_connection() as db:
            db.execute("SELECT 1 as test")
            result = db.fetchone()
            print(f"✅ 数据库查询测试成功: {result}")
        
        print("✅ 后端数据库模块测试成功!")
        return True
        
    except Exception as e:
        print(f"❌ 后端数据库模块测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 高速公路智能监控系统 - 数据库连接测试")
    print("=" * 60)
    
    # 检查配置文件是否存在
    config_file = 'config/end-back.env'
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        print("💡 请确保配置文件存在并包含正确的数据库配置")
        return
    
    print(f"✅ 配置文件存在: {config_file}")
    
    # 测试基础数据库连接
    db_success = test_database_connection()
    
    # 测试后端数据库模块
    backend_success = test_backend_database_import()
    
    print("\n" + "=" * 60)
    if db_success and backend_success:
        print("🎉 所有数据库测试通过!")
        print("✅ 现在可以重新启动后端服务:")
        print("   python backend/app_enhanced.py")
    else:
        print("⚠️ 数据库测试发现问题，请根据上述提示解决")
    print("=" * 60)

if __name__ == "__main__":
    main()
