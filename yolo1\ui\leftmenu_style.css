/* 左侧菜单栏主框架 - 应用规则阴影 */
#LeftMenuBg {
    background-color: transparent;
    border: none;
}

/* 左侧菜单栏中的所有按钮 - 添加规则阴影 */
#LeftMenuBg QPushButton {
    background-repeat: no-repeat;
    background-position: left center;
    border: none;
    border-left: 20px solid rgba(0, 120, 215, 0.8); /* 使用蓝色作为左侧边框 */
    text-align: left;
    padding-left: 40px;
    color: white;
    font: 500 11pt "Microsoft YaHei";
    border-radius: 10px;
    margin: 5px 10px; /* 增加上下边距以便阴影效果更明显 */
    background-color: rgba(0, 150, 255, 0.3); /* 半透明蓝色背景 */
    /* Qt不支持box-shadow，使用border和background实现阴影效果 */
}

/* 鼠标悬停效果 */
#LeftMenuBg QPushButton:hover {
    background-color: rgba(255, 255, 255, 0.25);
    /* Qt不支持box-shadow，使用border和background实现阴影效果 */
}

/* 按下效果 */
#LeftMenuBg QPushButton:pressed {
    background-color: rgba(255, 255, 255, 0.3);
    /* Qt不支持box-shadow，使用border和background实现阴影效果 */
}

/* 特定类型的按钮可以有不同的左侧边框颜色 */
#LeftMenuBg #src_file_button {
    border-left: 20px solid rgba(41, 128, 185, 0.8); /* 本地文件 */
}

#LeftMenuBg #src_cam_button {
    border-left: 20px solid rgba(46, 204, 113, 0.8); /* 调用摄像头 */
}

#LeftMenuBg #src_rtsp_button {
    border-left: 20px solid rgba(52, 152, 219, 0.8); /* RTSP监控 */
}

#LeftMenuBg #src_graph_button {
    border-left: 20px solid rgba(60, 160, 230, 0.8); /* 流量图 */
}

#LeftMenuBg #src_lock_button {
    border-left: 20px solid rgba(155, 89, 182, 0.8); /* 单目标跟踪 */
}

#LeftMenuBg #src_web_button {
    border-left: 20px solid rgba(243, 156, 18, 0.8); /* 热力图 */
}

#LeftMenuBg #src_violation_button {
    border-left: 20px solid rgba(231, 76, 60, 0.8); /* 违规检测 */
}

#LeftMenuBg #src_pedestrian_button {
    border-left: 20px solid rgba(41, 128, 255, 0.8); /* 行人检测 */
}

#LeftMenuBg #src_multi_tracking_button {
    border-left: 20px solid rgba(52, 152, 219, 0.8); /* 多目标追踪 */
}

/* 所有可能影响导航栏的子控件 */
#LeftMenuBg QFrame,
#LeftMenuBg QWidget,
#LeftMenuBg QLabel,
#TopLogoInfo,
#ToggleBox,
#MenuBox,
#VersionInfo {
    background-color: transparent;
}