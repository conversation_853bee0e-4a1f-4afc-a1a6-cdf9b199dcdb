#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试UI修复效果
"""

import sys
from PySide6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QHBoxLayout

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("UI修复测试")
        self.setGeometry(100, 100, 600, 300)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QHBoxLayout(central_widget)
        
        # 创建行人检测按钮
        pedestrian_layout = QVBoxLayout()
        pedestrian_label = QWidget()
        pedestrian_label.setStyleSheet("background-color: #3498db; color: white; padding: 10px; font-weight: bold;")
        pedestrian_layout_inner = QVBoxLayout(pedestrian_label)
        pedestrian_layout_inner.addWidget(QPushButton("行人检测系统"))
        pedestrian_layout.addWidget(pedestrian_label)
        
        self.pedestrian_button = QPushButton("测试行人检测对话框")
        self.pedestrian_button.setStyleSheet("padding: 10px; font-size: 14px;")
        self.pedestrian_button.clicked.connect(self.test_pedestrian_dialog)
        pedestrian_layout.addWidget(self.pedestrian_button)
        
        # 创建碰撞检测按钮
        collision_layout = QVBoxLayout()
        collision_label = QWidget()
        collision_label.setStyleSheet("background-color: #e74c3c; color: white; padding: 10px; font-weight: bold;")
        collision_layout_inner = QVBoxLayout(collision_label)
        collision_layout_inner.addWidget(QPushButton("车辆碰撞检测系统"))
        collision_layout.addWidget(collision_label)
        
        self.collision_button = QPushButton("测试碰撞检测对话框")
        self.collision_button.setStyleSheet("padding: 10px; font-size: 14px;")
        self.collision_button.clicked.connect(self.test_collision_dialog)
        collision_layout.addWidget(self.collision_button)
        
        layout.addLayout(pedestrian_layout)
        layout.addLayout(collision_layout)
    
    def test_pedestrian_dialog(self):
        """测试行人检测对话框"""
        try:
            from ui.dialog.simple_pedestrian_dialog import SimplePedestrianDialog
            dialog = SimplePedestrianDialog(self)
            dialog.show()
            print("✅ 行人检测对话框显示成功")
        except Exception as e:
            print(f"❌ 行人检测对话框显示失败: {e}")
            import traceback
            traceback.print_exc()
    
    def test_collision_dialog(self):
        """测试碰撞检测对话框"""
        try:
            from ui.dialog.collision_detection_dialog import CollisionDetectionDialog
            dialog = CollisionDetectionDialog(self)
            dialog.show()
            print("✅ 碰撞检测对话框显示成功")
        except Exception as e:
            print(f"❌ 碰撞检测对话框显示失败: {e}")
            import traceback
            traceback.print_exc()

def main():
    app = QApplication(sys.argv)
    window = TestWindow()
    window.show()
    print("🚀 测试应用启动成功")
    print("请点击按钮测试修复后的对话框...")
    sys.exit(app.exec())

if __name__ == "__main__":
    main()