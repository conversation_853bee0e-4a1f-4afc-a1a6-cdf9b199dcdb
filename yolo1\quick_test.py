# -*- coding: utf-8 -*-
# @Description : 快速测试修复结果
# @Date : 2025年6月21日

import sys
import os

def test_imports():
    """测试关键导入"""
    print("🔍 测试关键模块导入...")
    
    try:
        import supervision as sv
        print("✅ supervision导入成功")
        
        # 检查API
        has_ultralytics = hasattr(sv.Detections, 'from_ultralytics')
        has_yolov8 = hasattr(sv.Detections, 'from_yolov8')
        
        print(f"   from_ultralytics: {has_ultralytics}")
        print(f"   from_yolov8: {has_yolov8}")
        
    except Exception as e:
        print(f"❌ supervision导入失败: {e}")
        return False
    
    try:
        from ultralytics import YOLO
        print("✅ ultralytics导入成功")
    except Exception as e:
        print(f"❌ ultralytics导入失败: {e}")
        return False
    
    try:
        import numpy as np
        import cv2
        print("✅ numpy和cv2导入成功")
    except Exception as e:
        print(f"❌ numpy/cv2导入失败: {e}")
        return False
    
    return True

def test_compatibility_function():
    """测试兼容性函数"""
    print("\n🔧 测试兼容性函数...")
    
    try:
        import supervision as sv
        from ultralytics import YOLO
        import numpy as np
        
        # 兼容性函数
        def create_detections_from_result(result):
            try:
                return sv.Detections.from_ultralytics(result)
            except AttributeError:
                try:
                    return sv.Detections.from_yolov8(result)
                except AttributeError:
                    if result.boxes is not None:
                        boxes = result.boxes.xyxy.cpu().numpy()
                        confidences = result.boxes.conf.cpu().numpy()
                        class_ids = result.boxes.cls.cpu().numpy().astype(int)
                        track_ids = result.boxes.id.cpu().numpy().astype(int) if result.boxes.id is not None else None
                        
                        return sv.Detections(
                            xyxy=boxes,
                            confidence=confidences,
                            class_id=class_ids,
                            tracker_id=track_ids
                        )
                    else:
                        return sv.Detections.empty()
        
        # 测试
        model = YOLO('yolov8n.pt')
        test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
        results = model(test_image, verbose=False)
        result = results[0]
        
        detections = create_detections_from_result(result)
        print(f"✅ 兼容性函数工作正常，检测到 {len(detections)} 个目标")
        
        return True
        
    except Exception as e:
        print(f"❌ 兼容性函数测试失败: {e}")
        return False

def test_collision_detection():
    """测试碰撞检测导入"""
    print("\n🚗 测试碰撞检测功能...")
    
    try:
        # 检查文件是否存在
        if not os.path.exists('ui/dialog/collision_detection_dialog.py'):
            print("❌ 碰撞检测对话框文件不存在")
            return False
        
        from ui.dialog.collision_detection_dialog import CollisionDetectionDialog, CollisionDetectionAlgorithm
        
        # 创建算法实例
        algorithm = CollisionDetectionAlgorithm()
        print("✅ 碰撞检测算法创建成功")
        
        # 测试算法
        test_objects = [
            {'track_id': 1, 'bbox': [100, 100, 150, 150], 'confidence': 0.9},
            {'track_id': 2, 'bbox': [130, 130, 180, 180], 'confidence': 0.85}
        ]
        
        from datetime import datetime
        collisions = algorithm.detect_collision(test_objects, datetime.now())
        print(f"✅ 碰撞检测测试完成，检测到 {len(collisions)} 个碰撞")
        
        return True
        
    except Exception as e:
        print(f"❌ 碰撞检测测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 快速修复验证测试")
    print("="*50)
    
    # 测试导入
    imports_ok = test_imports()
    
    # 测试兼容性
    compat_ok = test_compatibility_function()
    
    # 测试碰撞检测
    collision_ok = test_collision_detection()
    
    # 总结
    print(f"\n📊 测试结果:")
    print(f"   模块导入: {'✅' if imports_ok else '❌'}")
    print(f"   兼容性修复: {'✅' if compat_ok else '❌'}")
    print(f"   碰撞检测: {'✅' if collision_ok else '❌'}")
    
    all_ok = imports_ok and compat_ok and collision_ok
    
    if all_ok:
        print("\n🎊 所有测试通过！修复成功！")
        print("\n现在可以正常使用:")
        print("   1. 启动GUI: python main.py")
        print("   2. 使用碰撞检测功能")
    else:
        print("\n⚠️ 部分测试失败，需要进一步检查")
    
    return all_ok

if __name__ == "__main__":
    main()
