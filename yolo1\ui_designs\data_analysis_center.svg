<svg width="1200" height="900" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e3c72;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2a5298;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.95" />
      <stop offset="100%" style="stop-color:#f8fafc;stop-opacity:0.95" />
    </linearGradient>
    <linearGradient id="chartGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:0.3" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="100%" height="100%" fill="url(#bgGradient)"/>
  
  <!-- Header -->
  <rect x="20" y="20" width="1160" height="80" rx="12" fill="url(#cardGradient)" filter="url(#shadow)"/>
  <text x="50" y="50" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#1f2937">📊 数据分析中心</text>
  <text x="50" y="75" font-family="Arial, sans-serif" font-size="14" fill="#6b7280">智能交通数据分析与可视化平台</text>
  
  <!-- Filter Section -->
  <rect x="20" y="120" width="1160" height="120" rx="12" fill="url(#cardGradient)" filter="url(#shadow)"/>
  <text x="50" y="150" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#1f2937">🔍 筛选条件</text>
  
  <!-- Date Range -->
  <text x="50" y="175" font-family="Arial, sans-serif" font-size="14" fill="#374151">时间范围:</text>
  <rect x="120" y="160" width="120" height="30" rx="6" fill="#f3f4f6" stroke="#d1d5db"/>
  <text x="130" y="178" font-family="Arial, sans-serif" font-size="12" fill="#374151">2023-12-01</text>
  <text x="250" y="178" font-family="Arial, sans-serif" font-size="14" fill="#374151">至</text>
  <rect x="270" y="160" width="120" height="30" rx="6" fill="#f3f4f6" stroke="#d1d5db"/>
  <text x="280" y="178" font-family="Arial, sans-serif" font-size="12" fill="#374151">2023-12-31</text>
  
  <!-- Monitor Point -->
  <text x="420" y="175" font-family="Arial, sans-serif" font-size="14" fill="#374151">监控点:</text>
  <rect x="480" y="160" width="100" height="30" rx="6" fill="#f3f4f6" stroke="#d1d5db"/>
  <text x="490" y="178" font-family="Arial, sans-serif" font-size="12" fill="#374151">全部 ▼</text>
  
  <!-- Data Type -->
  <text x="50" y="210" font-family="Arial, sans-serif" font-size="14" fill="#374151">数据类型:</text>
  <rect x="120" y="195" width="120" height="30" rx="6" fill="#f3f4f6" stroke="#d1d5db"/>
  <text x="130" y="213" font-family="Arial, sans-serif" font-size="12" fill="#374151">交通流量 ▼</text>
  
  <!-- Action Buttons -->
  <rect x="270" y="195" width="60" height="30" rx="6" fill="#3b82f6" stroke="#2563eb"/>
  <text x="285" y="213" font-family="Arial, sans-serif" font-size="12" fill="white">🔍 查询</text>
  <rect x="340" y="195" width="60" height="30" rx="6" fill="#10b981" stroke="#059669"/>
  <text x="355" y="213" font-family="Arial, sans-serif" font-size="12" fill="white">📤 导出</text>
  
  <!-- Traffic Flow Analysis -->
  <rect x="20" y="260" width="570" height="300" rx="12" fill="url(#cardGradient)" filter="url(#shadow)"/>
  <text x="50" y="290" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#1f2937">📈 交通流量分析</text>
  <text x="50" y="315" font-family="Arial, sans-serif" font-size="14" fill="#6b7280">日均交通流量趋势 (最近30天)</text>
  
  <!-- Chart Area -->
  <rect x="50" y="330" width="510" height="200" rx="8" fill="#f9fafb" stroke="#e5e7eb"/>
  
  <!-- Y-axis labels -->
  <text x="45" y="340" font-family="Arial, sans-serif" font-size="10" fill="#6b7280" text-anchor="end">2000</text>
  <text x="45" y="370" font-family="Arial, sans-serif" font-size="10" fill="#6b7280" text-anchor="end">1800</text>
  <text x="45" y="400" font-family="Arial, sans-serif" font-size="10" fill="#6b7280" text-anchor="end">1600</text>
  <text x="45" y="430" font-family="Arial, sans-serif" font-size="10" fill="#6b7280" text-anchor="end">1400</text>
  <text x="45" y="460" font-family="Arial, sans-serif" font-size="10" fill="#6b7280" text-anchor="end">1200</text>
  <text x="45" y="490" font-family="Arial, sans-serif" font-size="10" fill="#6b7280" text-anchor="end">1000</text>
  <text x="45" y="520" font-family="Arial, sans-serif" font-size="10" fill="#6b7280" text-anchor="end">800</text>
  
  <!-- X-axis labels -->
  <text x="70" y="545" font-family="Arial, sans-serif" font-size="10" fill="#6b7280" text-anchor="middle">1</text>
  <text x="150" y="545" font-family="Arial, sans-serif" font-size="10" fill="#6b7280" text-anchor="middle">5</text>
  <text x="230" y="545" font-family="Arial, sans-serif" font-size="10" fill="#6b7280" text-anchor="middle">10</text>
  <text x="310" y="545" font-family="Arial, sans-serif" font-size="10" fill="#6b7280" text-anchor="middle">15</text>
  <text x="390" y="545" font-family="Arial, sans-serif" font-size="10" fill="#6b7280" text-anchor="middle">20</text>
  <text x="470" y="545" font-family="Arial, sans-serif" font-size="10" fill="#6b7280" text-anchor="middle">25</text>
  <text x="550" y="545" font-family="Arial, sans-serif" font-size="10" fill="#6b7280" text-anchor="middle">30</text>
  
  <!-- Chart Line -->
  <polyline points="70,500 90,480 110,460 130,440 150,420 170,400 190,380 210,360 230,350 250,340 270,350 290,360 310,370 330,380 350,390 370,400 390,410 410,420 430,430 450,440 470,450 490,460 510,470 530,480 550,490" 
            fill="none" stroke="#3b82f6" stroke-width="3" stroke-linecap="round"/>
  
  <!-- Data Points -->
  <circle cx="70" cy="500" r="4" fill="#3b82f6"/>
  <circle cx="150" cy="420" r="4" fill="#3b82f6"/>
  <circle cx="230" cy="350" r="4" fill="#3b82f6"/>
  <circle cx="310" cy="370" r="4" fill="#3b82f6"/>
  <circle cx="390" cy="410" r="4" fill="#3b82f6"/>
  <circle cx="470" cy="450" r="4" fill="#3b82f6"/>
  <circle cx="550" cy="490" r="4" fill="#3b82f6"/>
  
  <!-- Hourly Distribution Table -->
  <rect x="610" y="260" width="570" height="300" rx="12" fill="url(#cardGradient)" filter="url(#shadow)"/>
  <text x="640" y="290" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#1f2937">🕐 小时分布统计</text>
  
  <!-- Table Header -->
  <rect x="640" y="310" width="510" height="35" rx="6" fill="#f3f4f6" stroke="#e5e7eb"/>
  <text x="660" y="330" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#374151">时段</text>
  <text x="760" y="330" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#374151">平均车流量</text>
  <text x="860" y="330" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#374151">警报次数</text>
  <text x="940" y="330" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#374151">事故次数</text>
  <text x="1020" y="330" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#374151">拥堵指数</text>
  
  <!-- Table Rows -->
  <rect x="640" y="345" width="510" height="30" fill="white" stroke="#e5e7eb"/>
  <text x="660" y="363" font-family="Arial, sans-serif" font-size="11" fill="#374151">06:00-08:00</text>
  <text x="780" y="363" font-family="Arial, sans-serif" font-size="11" fill="#374151">1850</text>
  <text x="880" y="363" font-family="Arial, sans-serif" font-size="11" fill="#374151">25</text>
  <text x="960" y="363" font-family="Arial, sans-serif" font-size="11" fill="#374151">3</text>
  <text x="1040" y="363" font-family="Arial, sans-serif" font-size="11" fill="#374151">0.75</text>
  
  <rect x="640" y="375" width="510" height="30" fill="#f9fafb" stroke="#e5e7eb"/>
  <text x="660" y="393" font-family="Arial, sans-serif" font-size="11" fill="#374151">08:00-10:00</text>
  <text x="780" y="393" font-family="Arial, sans-serif" font-size="11" fill="#374151">2100</text>
  <text x="880" y="393" font-family="Arial, sans-serif" font-size="11" fill="#374151">35</text>
  <text x="960" y="393" font-family="Arial, sans-serif" font-size="11" fill="#374151">5</text>
  <text x="1040" y="393" font-family="Arial, sans-serif" font-size="11" fill="#374151">0.85</text>
  
  <rect x="640" y="405" width="510" height="30" fill="white" stroke="#e5e7eb"/>
  <text x="660" y="423" font-family="Arial, sans-serif" font-size="11" fill="#374151">10:00-12:00</text>
  <text x="780" y="423" font-family="Arial, sans-serif" font-size="11" fill="#374151">1650</text>
  <text x="880" y="423" font-family="Arial, sans-serif" font-size="11" fill="#374151">18</text>
  <text x="960" y="423" font-family="Arial, sans-serif" font-size="11" fill="#374151">2</text>
  <text x="1040" y="423" font-family="Arial, sans-serif" font-size="11" fill="#374151">0.45</text>
  
  <rect x="640" y="435" width="510" height="30" fill="#f9fafb" stroke="#e5e7eb"/>
  <text x="660" y="453" font-family="Arial, sans-serif" font-size="11" fill="#374151">12:00-14:00</text>
  <text x="780" y="453" font-family="Arial, sans-serif" font-size="11" fill="#374151">1800</text>
  <text x="880" y="453" font-family="Arial, sans-serif" font-size="11" fill="#374151">22</text>
  <text x="960" y="453" font-family="Arial, sans-serif" font-size="11" fill="#374151">3</text>
  <text x="1040" y="453" font-family="Arial, sans-serif" font-size="11" fill="#374151">0.55</text>
  
  <rect x="640" y="465" width="510" height="30" fill="white" stroke="#e5e7eb"/>
  <text x="660" y="483" font-family="Arial, sans-serif" font-size="11" fill="#374151">14:00-16:00</text>
  <text x="780" y="483" font-family="Arial, sans-serif" font-size="11" fill="#374151">1950</text>
  <text x="880" y="483" font-family="Arial, sans-serif" font-size="11" fill="#374151">28</text>
  <text x="960" y="483" font-family="Arial, sans-serif" font-size="11" fill="#374151">4</text>
  <text x="1040" y="483" font-family="Arial, sans-serif" font-size="11" fill="#374151">0.68</text>
  
  <rect x="640" y="495" width="510" height="30" fill="#f9fafb" stroke="#e5e7eb"/>
  <text x="660" y="513" font-family="Arial, sans-serif" font-size="11" fill="#374151">16:00-18:00</text>
  <text x="780" y="513" font-family="Arial, sans-serif" font-size="11" fill="#374151">2200</text>
  <text x="880" y="513" font-family="Arial, sans-serif" font-size="11" fill="#374151">40</text>
  <text x="960" y="513" font-family="Arial, sans-serif" font-size="11" fill="#374151">6</text>
  <text x="1040" y="513" font-family="Arial, sans-serif" font-size="11" fill="#374151">0.90</text>
  
  <!-- Heatmap Section -->
  <rect x="20" y="580" width="1160" height="280" rx="12" fill="url(#cardGradient)" filter="url(#shadow)"/>
  <text x="50" y="610" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#1f2937">🗺️ 热力图分析</text>
  <text x="50" y="635" font-family="Arial, sans-serif" font-size="14" fill="#6b7280">路段热力分布图</text>
  
  <!-- Heatmap Chart -->
  <rect x="50" y="650" width="1100" height="180" rx="8" fill="#f9fafb" stroke="#e5e7eb"/>
  
  <!-- Road Segments -->
  <text x="70" y="675" font-family="Arial, sans-serif" font-size="12" fill="#374151">K100+000</text>
  <rect x="150" y="665" width="400" height="15" fill="#dc2626" rx="2"/>
  
  <text x="70" y="700" font-family="Arial, sans-serif" font-size="12" fill="#374151">K100+200</text>
  <rect x="150" y="690" width="320" height="15" fill="#ea580c" rx="2"/>
  
  <text x="70" y="725" font-family="Arial, sans-serif" font-size="12" fill="#374151">K100+400</text>
  <rect x="150" y="715" width="280" height="15" fill="#f59e0b" rx="2"/>
  
  <text x="70" y="750" font-family="Arial, sans-serif" font-size="12" fill="#374151">K100+600</text>
  <rect x="150" y="740" width="360" height="15" fill="#eab308" rx="2"/>
  
  <text x="70" y="775" font-family="Arial, sans-serif" font-size="12" fill="#374151">K100+800</text>
  <rect x="150" y="765" width="400" height="15" fill="#dc2626" rx="2"/>
  
  <text x="70" y="800" font-family="Arial, sans-serif" font-size="12" fill="#374151">K101+000</text>
  <rect x="150" y="790" width="240" height="15" fill="#65a30d" rx="2"/>
  
  <!-- Legend -->
  <text x="150" y="820" font-family="Arial, sans-serif" font-size="12" fill="#6b7280">低</text>
  <rect x="180" y="810" width="200" height="10" fill="url(#chartGradient)" rx="5"/>
  <text x="390" y="820" font-family="Arial, sans-serif" font-size="12" fill="#6b7280">高</text>
  
</svg>