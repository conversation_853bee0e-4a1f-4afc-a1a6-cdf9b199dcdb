<template>
  <a-layout class="main-layout">
    <!-- 侧边导航栏 -->
    <a-layout-sider
      :width="240"
      :collapsed="collapsed"
      :collapsible="true"
      @collapse="onCollapse"
      class="layout-sider"
    >
      <div class="logo">
        <h2 v-if="!collapsed">智能监控系统</h2>
        <h2 v-else>监控</h2>
      </div>
      
      <a-menu
        :default-selected-keys="[currentRoute]"
        :default-open-keys="openKeys"
        mode="vertical"
        theme="light"
        @menu-item-click="handleMenuClick"
        class="custom-menu"
      >
        <a-menu-item key="dashboard">
          <template #icon><icon-dashboard /></template>
          仪表板
        </a-menu-item>
        
        <a-menu-item key="monitor">
          <template #icon><icon-camera /></template>
          监控管理
        </a-menu-item>
        
        <a-menu-item key="detection">
          <template #icon><icon-eye /></template>
          检测中心
        </a-menu-item>
        
        <a-menu-item key="tracking">
          <template #icon><icon-location /></template>
          多目标追踪
        </a-menu-item>
        
        <a-menu-item key="accident">
          <template #icon><icon-exclamation-circle /></template>
          事故检测
        </a-menu-item>
        
        <a-menu-item key="analysis">
          <template #icon><icon-bar-chart /></template>
          数据分析
        </a-menu-item>
        
        <a-menu-item key="system" v-if="userStore.user?.grade === 'admin'">
          <template #icon><icon-settings /></template>
          系统管理
        </a-menu-item>
        
        <a-menu-item key="profile">
          <template #icon><icon-user /></template>
          个人中心
        </a-menu-item>

        <!-- 开发工具 -->
        <a-menu-item key="api-test" v-if="isDev">
          <template #icon><icon-bug /></template>
          API测试
        </a-menu-item>

        <a-menu-item key="business-test" v-if="isDev">
          <template #icon><icon-experiment /></template>
          业务测试
        </a-menu-item>

        <a-menu-item key="login-diagnostic" v-if="isDev">
          <template #icon><icon-exclamation-circle /></template>
          登录诊断
        </a-menu-item>

        <a-menu-item key="api-path-diagnostic" v-if="isDev">
          <template #icon><icon-link /></template>
          路径诊断
        </a-menu-item>

        <a-menu-item key="api-integration-test" v-if="isDev">
          <template #icon><icon-check-circle /></template>
          对接验证
        </a-menu-item>

        <a-menu-item key="api-status-check" v-if="isDev">
          <template #icon><icon-dashboard /></template>
          API状态
        </a-menu-item>

        <a-menu-item key="data-format-check" v-if="isDev">
          <template #icon><icon-code /></template>
          数据格式
        </a-menu-item>

        <a-menu-item key="accident-api-test" v-if="isDev">
          <template #icon><icon-exclamation-circle /></template>
          事故API诊断
        </a-menu-item>

        <a-menu-item key="data-adapter-test" v-if="isDev">
          <template #icon><icon-swap /></template>
          数据适配器
        </a-menu-item>

      </a-menu>
    </a-layout-sider>
    
    <!-- 主内容区域 -->
    <a-layout>
      <!-- 顶部导航栏 -->
      <a-layout-header class="layout-header">
        <div class="header-left">
          <a-breadcrumb>
            <a-breadcrumb-item v-for="item in breadcrumbs" :key="item.path">
              {{ item.name }}
            </a-breadcrumb-item>
          </a-breadcrumb>
        </div>
        
        <div class="header-right">
          <a-space>
            <!-- 用户信息 -->
            <a-dropdown>
              <a-button type="text" class="user-button">
                <template #icon><icon-user class="user-icon" /></template>
                {{ userStore.user?.username }}
                <template #suffix><icon-down class="down-icon" /></template>
              </a-button>
              <template #content>
                <a-doption @click="goToProfile">
                  <template #icon><icon-user /></template>
                  个人中心
                </a-doption>
                <a-doption @click="handleLogout">
                  <template #icon><icon-export /></template>
                  退出登录
                </a-doption>
              </template>
            </a-dropdown>
          </a-space>
        </div>
      </a-layout-header>
      
      <!-- 内容区域 -->
      <a-layout-content class="layout-content">
        <router-view />
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Message } from '@arco-design/web-vue'
import {
  IconDashboard,
  IconCamera,
  IconEye,
  IconLocation,
  IconExclamationCircle,
  IconBarChart,
  IconSettings,
  IconUser,
  IconDown,
  IconExport,
  IconBug,
  IconExperiment,
  IconLink,
  IconCheckCircle,
  IconCode,
  IconSwap
} from '@arco-design/web-vue/es/icon'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const route = useRoute()
const userStore = useAuthStore()

const collapsed = ref(false)
const openKeys = ref([''])
const isDev = false // import.meta.env.DEV

const currentRoute = computed(() => route.name as string)

// 面包屑导航
const breadcrumbs = computed(() => {
  const routeMap: Record<string, string> = {
    dashboard: '仪表板',
    monitor: '监控管理',
    detection: '检测中心',
    tracking: '多目标追踪',
    accident: '事故检测',
    analysis: '数据分析',
    system: '系统管理',
    profile: '个人中心'
  }
  
  return [
    { name: '首页', path: '/' },
    { name: routeMap[currentRoute.value] || '未知页面', path: route.path }
  ]
})

const onCollapse = (val: boolean) => {
  collapsed.value = val
}

const handleMenuClick = (key: string) => {
  router.push(`/${key}`)
}

const goToProfile = () => {
  router.push('/profile')
}

const handleLogout = async () => {
  await userStore.logout()
  Message.success('已退出登录')
  router.push('/login')
}

// 监听路由变化，更新选中的菜单项
watch(
  () => route.name,
  (newRoute) => {
    if (newRoute) {
      // 这里可以根据需要设置展开的菜单项
    }
  },
  { immediate: true }
)
</script>

<style scoped>
.main-layout {
  height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
}

.main-layout::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23475569" opacity="0.03"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.layout-sider {
  background: rgba(255, 255, 255, 0.8) !important;
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(148, 163, 184, 0.2);
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.05);
  z-index: 10;
}

.logo {
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  font-weight: 600;
  font-size: 18px;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.logo::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transform: rotate(45deg);
  animation: shine 4s infinite;
}

@keyframes shine {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
  100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
}

.logo h2 {
  margin: 0;
}

.layout-header {
  background: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(20px);
  padding: 0 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(148, 163, 184, 0.2);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  z-index: 5;
}

.layout-content {
  margin: 32px;
  padding: 32px;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.custom-menu .arco-menu-item {
  border-radius: 4px;
  margin: 4px 8px;
}

.custom-menu .arco-menu-item.arco-menu-selected {
  background-color: #e8f3ff;
  color: #165dff;
  font-weight: 500;
}

.custom-menu .arco-menu-item.arco-menu-selected .arco-icon {
  color: #165dff;
}

.user-button {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border: none;
  color: white;
  padding: 8px 16px;
  border-radius: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
}

.user-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  background: linear-gradient(135deg, #2563eb, #1e40af);
}

.user-icon {
  margin-right: 8px;
}

.down-icon {
  margin-left: 8px;
  transition: transform 0.3s ease;
}

.user-button:hover .down-icon {
  transform: rotate(180deg);
}

.arco-menu-item {
  position: relative;
  overflow: hidden;
  color: #475569 !important;
}

.arco-menu-item::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  transition: width 0.3s ease;
}

.arco-menu-item:hover::after {
  width: 100%;
}

.arco-menu-item:hover {
  background-color: rgba(59, 130, 246, 0.1) !important;
  color: #1e40af !important;
}

.arco-menu-item.arco-menu-selected {
  background-color: rgba(59, 130, 246, 0.15) !important;
  color: #1d4ed8 !important;
}

</style>
