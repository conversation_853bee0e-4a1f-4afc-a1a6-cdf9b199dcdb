<template>
  <div class="accident-api-test">
    <a-card title="事故检测API测试">
      <a-alert type="warning" show-icon style="margin-bottom: 16px;">
        <template #title>事故检测页面卡死问题诊断</template>
        <div>
          <p>事故检测页面一点击就卡住，需要检查相关API是否正常响应</p>
          <p>此工具将逐个测试事故检测页面用到的所有API</p>
        </div>
      </a-alert>
      
      <div class="test-section">
        <h3>API测试结果</h3>
        
        <div class="api-tests">
          <div v-for="test in apiTests" :key="test.name" class="test-item">
            <div class="test-header">
              <h4>{{ test.title }}</h4>
              <a-space>
                <a-tag :color="getStatusColor(test.status)">
                  {{ getStatusText(test.status) }}
                </a-tag>
                <a-button 
                  size="small" 
                  @click="testApi(test)"
                  :loading="test.testing"
                >
                  测试
                </a-button>
              </a-space>
            </div>
            
            <div class="test-info">
              <p><strong>API端点:</strong> <code>{{ test.endpoint }}</code></p>
              <p><strong>用途:</strong> {{ test.description }}</p>
            </div>
            
            <div class="test-result" v-if="test.result">
              <div class="result-summary">
                <p><strong>响应时间:</strong> {{ test.result.duration }}ms</p>
                <p><strong>状态码:</strong> {{ test.result.status }}</p>
                <p v-if="test.result.error"><strong>错误:</strong> {{ test.result.error }}</p>
              </div>
              
              <a-collapse size="small" v-if="test.result.data">
                <a-collapse-item header="响应数据" key="data">
                  <pre class="response-data">{{ JSON.stringify(test.result.data, null, 2) }}</pre>
                </a-collapse-item>
              </a-collapse>
            </div>
          </div>
        </div>
        
        <a-divider />
        
        <div class="actions">
          <a-space>
            <a-button @click="testAllApis" :loading="allTesting" type="primary">
              测试所有API
            </a-button>
            <a-button @click="testPageLoad" :loading="pageLoadTesting" type="outline">
              模拟页面加载
            </a-button>
            <a-button @click="generateReport">
              生成诊断报告
            </a-button>
          </a-space>
        </div>
        
        <!-- 诊断结果 -->
        <div class="diagnosis" v-if="diagnosis">
          <h3>诊断结果</h3>
          <a-alert :type="diagnosis.type" show-icon>
            <template #title>{{ diagnosis.title }}</template>
            <div>
              <p>{{ diagnosis.message }}</p>
              <div v-if="diagnosis.suggestions.length > 0">
                <p><strong>建议解决方案:</strong></p>
                <ul>
                  <li v-for="suggestion in diagnosis.suggestions" :key="suggestion">
                    {{ suggestion }}
                  </li>
                </ul>
              </div>
            </div>
          </a-alert>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { Message } from '@arco-design/web-vue'
import axios from 'axios'

const allTesting = ref(false)
const pageLoadTesting = ref(false)
const diagnosis = ref(null)

// API测试配置
const apiTests = reactive([
  {
    name: 'monitors',
    title: '获取监控点列表',
    endpoint: '/api/v1/monitor/list',
    description: '事故检测页面首先需要加载监控点列表',
    testing: false,
    status: 'pending',
    result: null
  },
  {
    name: 'realtime_alerts',
    title: '获取实时警报',
    endpoint: '/api/v1/accident/alerts/realtime',
    description: '实时警报区域的数据源，最容易导致卡死',
    testing: false,
    status: 'pending',
    result: null
  },
  {
    name: 'accident_records',
    title: '获取事故记录',
    endpoint: '/api/v1/accident/records',
    description: '事故记录表格的数据源',
    testing: false,
    status: 'pending',
    result: null
  },
  {
    name: 'accident_config',
    title: '获取事故检测配置',
    endpoint: '/api/v1/accident/config',
    description: '检测配置相关API',
    testing: false,
    status: 'pending',
    result: null
  }
])

// 获取状态颜色
const getStatusColor = (status: string) => {
  switch (status) {
    case 'success': return 'green'
    case 'failed': return 'red'
    case 'timeout': return 'orange'
    case 'testing': return 'blue'
    default: return 'gray'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'success': return '成功'
    case 'failed': return '失败'
    case 'timeout': return '超时'
    case 'testing': return '测试中'
    default: return '未测试'
  }
}

// 数据适配函数（与事故检测页面保持一致）
const adaptAlertData = (backendData: any) => {
  if (backendData?.alerts) {
    return backendData.alerts.map((alert: any) => ({
      alert_id: alert.id || alert.alert_id,
      alert_type: alert.alarm_type || alert.alert_type || 'unknown',
      monitor_id: alert.monitor_id,
      monitor_name: alert.location || alert.monitor_name || `监控点${alert.monitor_id}`,
      severity: alert.severity || 'medium',
      confidence: alert.confidence_level || alert.confidence || 0,
      description: alert.description || '暂无描述',
      status: alert.status || 'pending',
      create_time: alert.create_time || alert.timestamp || new Date().toISOString()
    }))
  } else if (Array.isArray(backendData)) {
    return backendData
  }
  return []
}

const adaptRecordData = (backendData: any) => {
  if (backendData?.records) {
    return backendData.records.map((record: any) => ({
      record_id: record.id || record.record_id,
      monitor_id: record.monitor_id,
      accident_type: record.accident_type || record.type || 'unknown',
      severity: record.severity || 'medium',
      description: record.description || '暂无描述',
      status: record.status || 'pending',
      create_time: record.create_time || record.timestamp || new Date().toISOString()
    }))
  } else if (Array.isArray(backendData)) {
    return backendData
  }
  return []
}

// 测试单个API
const testApi = async (test: any) => {
  test.testing = true
  test.status = 'testing'
  const startTime = Date.now()

  try {
    // 5秒超时，比事故检测页面的超时更短
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('测试超时')), 5000)
    })

    const config = {
      method: 'get',
      url: test.endpoint,
      timeout: 5000,
      headers: {
        'Authorization': 'Bearer test-token',
        'Content-Type': 'application/json'
      },
      params: test.name === 'monitors' ? { page: 1, size: 10 } :
             test.name === 'realtime_alerts' ? { limit: 5 } :
             test.name === 'accident_records' ? { page: 1, page_size: 5 } : {}
    }

    const apiPromise = axios(config)
    const response = await Promise.race([apiPromise, timeoutPromise])
    const duration = Date.now() - startTime

    // 对特定API应用数据适配
    let adaptedData = response.data
    if (test.name === 'realtime_alerts' && response.data) {
      adaptedData = {
        ...response.data,
        adapted_alerts: adaptAlertData(response.data.data || response.data)
      }
    } else if (test.name === 'accident_records' && response.data) {
      adaptedData = {
        ...response.data,
        adapted_records: adaptRecordData(response.data.data || response.data)
      }
    }

    test.result = {
      status: response.status,
      duration,
      data: adaptedData,
      raw_data: response.data // 保留原始数据用于对比
    }

    if (duration > 3000) {
      test.status = 'timeout'
      Message.warning(`${test.title} 响应较慢 (${duration}ms)`)
    } else {
      test.status = 'success'
      Message.success(`${test.title} 测试成功`)
    }

  } catch (error: any) {
    const duration = Date.now() - startTime

    test.result = {
      status: error.response?.status || 'Network Error',
      duration,
      error: error.message,
      data: error.response?.data
    }

    if (error.message.includes('超时') || duration >= 5000) {
      test.status = 'timeout'
      Message.error(`${test.title} 请求超时`)
    } else {
      test.status = 'failed'
      Message.error(`${test.title} 测试失败: ${error.message}`)
    }
  } finally {
    test.testing = false
  }
}

// 测试所有API
const testAllApis = async () => {
  allTesting.value = true
  
  for (const test of apiTests) {
    await testApi(test)
    await new Promise(resolve => setTimeout(resolve, 500))
  }
  
  allTesting.value = false
  generateDiagnosis()
}

// 模拟页面加载
const testPageLoad = async () => {
  pageLoadTesting.value = true
  
  try {
    Message.info('模拟事故检测页面加载过程...')
    
    // 模拟页面加载的API调用顺序
    const startTime = Date.now()
    
    // 并行调用前三个API（模拟onMounted中的Promise.all）
    const promises = [
      testApi(apiTests[0]), // monitors
      testApi(apiTests[1]), // realtime_alerts  
      testApi(apiTests[2])  // accident_records
    ]
    
    await Promise.all(promises)
    
    const totalTime = Date.now() - startTime
    
    if (totalTime > 10000) {
      Message.error(`页面加载模拟完成，总耗时 ${totalTime}ms - 这会导致页面卡死！`)
    } else if (totalTime > 5000) {
      Message.warning(`页面加载模拟完成，总耗时 ${totalTime}ms - 页面响应较慢`)
    } else {
      Message.success(`页面加载模拟完成，总耗时 ${totalTime}ms - 页面响应正常`)
    }
    
  } catch (error) {
    Message.error('页面加载模拟失败')
  } finally {
    pageLoadTesting.value = false
    generateDiagnosis()
  }
}

// 生成诊断结果
const generateDiagnosis = () => {
  const failedTests = apiTests.filter(test => test.status === 'failed')
  const timeoutTests = apiTests.filter(test => test.status === 'timeout')
  const slowTests = apiTests.filter(test => test.result && test.result.duration > 3000)
  
  if (failedTests.length > 0) {
    diagnosis.value = {
      type: 'error',
      title: '发现API错误',
      message: `${failedTests.length} 个API请求失败，这会导致页面卡死`,
      suggestions: [
        '检查后端服务是否正常运行',
        '确认失败的API端点是否已实现',
        '检查API路由配置是否正确',
        '暂时注释掉失败的API调用，让页面先能正常加载'
      ]
    }
  } else if (timeoutTests.length > 0) {
    diagnosis.value = {
      type: 'warning',
      title: '发现API超时',
      message: `${timeoutTests.length} 个API响应超时，这会导致页面加载缓慢或卡死`,
      suggestions: [
        '优化后端API性能',
        '增加数据库查询索引',
        '减少单次查询的数据量',
        '在前端增加更短的超时时间'
      ]
    }
  } else if (slowTests.length > 0) {
    diagnosis.value = {
      type: 'warning',
      title: 'API响应较慢',
      message: `${slowTests.length} 个API响应时间超过3秒，影响用户体验`,
      suggestions: [
        '优化后端查询逻辑',
        '添加数据缓存',
        '使用分页减少数据量',
        '考虑异步加载非关键数据'
      ]
    }
  } else {
    diagnosis.value = {
      type: 'success',
      title: '所有API正常',
      message: '所有API都能正常响应，页面卡死可能是其他原因',
      suggestions: [
        '检查前端代码逻辑',
        '查看浏览器控制台错误',
        '检查网络连接',
        '尝试清除浏览器缓存'
      ]
    }
  }
}

// 生成诊断报告
const generateReport = () => {
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      total_apis: apiTests.length,
      success_count: apiTests.filter(t => t.status === 'success').length,
      failed_count: apiTests.filter(t => t.status === 'failed').length,
      timeout_count: apiTests.filter(t => t.status === 'timeout').length
    },
    details: apiTests.map(test => ({
      name: test.name,
      title: test.title,
      endpoint: test.endpoint,
      status: test.status,
      duration: test.result?.duration,
      error: test.result?.error
    })),
    diagnosis: diagnosis.value
  }
  
  const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `accident-api-diagnosis-${Date.now()}.json`
  link.click()
  URL.revokeObjectURL(url)
  
  Message.success('诊断报告已生成')
}
</script>

<style scoped>
.accident-api-test {
  padding: 20px;
}

.test-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.api-tests {
  margin-top: 16px;
}

.test-item {
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid #e5e6eb;
  border-radius: 8px;
}

.test-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.test-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.test-info p {
  margin: 4px 0;
  font-size: 12px;
  color: #666;
}

.test-result {
  margin-top: 16px;
  border-top: 1px solid #e5e6eb;
  padding-top: 16px;
}

.result-summary p {
  margin: 4px 0;
  font-size: 12px;
}

.response-data {
  font-family: 'Courier New', monospace;
  font-size: 11px;
  max-height: 200px;
  overflow-y: auto;
  margin: 0;
}

.actions {
  text-align: center;
  margin: 24px 0;
}

.diagnosis {
  margin-top: 24px;
}

.diagnosis h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.diagnosis ul {
  margin: 8px 0 0 20px;
}

.diagnosis li {
  margin: 4px 0;
}
</style>
