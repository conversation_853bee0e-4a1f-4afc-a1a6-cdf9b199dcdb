import axios from 'axios'
import { useAuthStore } from '@/stores/auth'
import { Message } from '@arco-design/web-vue'
import { API_CONFIG } from '@/config/constants'

// 创建axios实例
const request = axios.create({
  baseURL: API_CONFIG.API_PREFIX,
  timeout: API_CONFIG.TIMEOUT,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    const authStore = useAuthStore()
    if (authStore.token) {
      config.headers.Authorization = `Bearer ${authStore.token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    const authStore = useAuthStore()

    // 开发环境下，如果是连接错误，给出友好提示
    if (import.meta.env.DEV && error.code === 'ERR_NETWORK') {
      console.warn('后端服务器连接失败，请确保后端服务在 http://127.0.0.1:5500 运行')
      Message.warning('后端服务器连接失败，请检查后端服务是否启动')
      return Promise.reject(error)
    }

    if (error.response?.status === 401) {
      Message.error('登录已过期，请重新登录')
      authStore.logout()
      window.location.href = '/login'
    } else if (error.response?.status === 403) {
      Message.error('权限不足')
    } else if (error.response?.status >= 500) {
      Message.error('服务器错误，请稍后重试')
    } else {
      Message.error(error.response?.data?.message || '请求失败')
    }

    return Promise.reject(error)
  }
)

export default request
