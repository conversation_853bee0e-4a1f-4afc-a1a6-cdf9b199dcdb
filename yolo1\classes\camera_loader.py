# u7b80u5316u7684u6444u50cfu5934u52a0u8f7du5668
import cv2
import numpy as np

class CameraLoader:
    """u7528u4e8eu52a0u8f7du548cu64cdu4f5cu6444u50cfu5934u7684u7c7b"""
    
    def __init__(self):
        self.camera = None
        self.camera_id = 0
        self.is_rtsp = False
        self.rtsp_url = ""
    
    def open_camera(self, camera_id=0):
        """u6253u5f00u672cu5730u6444u50cfu5934"""
        try:
            self.camera = cv2.VideoCapture(camera_id)
            self.camera_id = camera_id
            self.is_rtsp = False
            return self.camera.isOpened()
        except Exception as e:
            print(f"Error opening camera: {e}")
            return False
    
    def open_rtsp(self, rtsp_url):
        """u6253u5f00 RTSP u6d41"""
        try:
            self.camera = cv2.VideoCapture(rtsp_url)
            self.rtsp_url = rtsp_url
            self.is_rtsp = True
            return self.camera.isOpened()
        except Exception as e:
            print(f"Error opening RTSP stream: {e}")
            return False
    
    def read_frame(self):
        """u8bfbu53d6u4e00u5e27u56feu50cf"""
        if self.camera is None or not self.camera.isOpened():
            return False, None
        
        ret, frame = self.camera.read()
        return ret, frame
    
    def release(self):
        """u91cau653eu6444u50cfu5934u8d44u6e90"""
        if self.camera is not None and self.camera.isOpened():
            self.camera.release()
            self.camera = None
