from PySide6.QtWidgets import QDialog
from PySide6.QtCore import Qt, Signal, Slot
from PySide6.QtGui import QColor
import cv2
import numpy as np
from .multi_tracking_dialog import MultiTrackingForm

class MultiTrackingDialog(QDialog, MultiTrackingForm):
    """多目标追踪对话框窗口类，实现多目标追踪的核心功能"""
    
    # 自定义信号
    tracking_started = Signal()   # 追踪开始信号
    tracking_stopped = Signal()   # 追踪停止信号
    tracking_updated = Signal(dict)  # 追踪更新信号，传递追踪结果数据
    
    def __init__(self, parent=None):
        super(MultiTrackingDialog, self).__init__(parent)
        print("初始化多目标跟踪对话框")  # 调试输出
        
        # 先调用setupUi
        self.setupUi(self)
        print("完成UI设置")  # 调试输出
        
        # 窗口配置 - 使用常规窗口标志，避免透明背景导致的问题
        # self.setWindowFlags(Qt.FramelessWindowHint | Qt.Dialog)
        # self.setAttribute(Qt.WA_TranslucentBackground)
        
        # 使用普通窗口样式确保可见
        self.setWindowFlags(Qt.Window)
        
        # 初始化追踪状态
        self.is_tracking = False
        self.tracking_thread = None
        self.mock_timer = None
        
        # 自定义属性
        self.confidence_threshold = 0.4  # 默认置信度阈值
        self.iou_threshold = 0.5         # 默认IOU阈值
        self.show_labels = True          # 是否显示标签
        self.show_trails = True          # 是否显示轨迹
        
        # 连接UI中的信号和槽
        try:
            # 连接滑块值变更信号
            self.confSlider.valueChanged.connect(self.onConfidenceChanged)
            self.iouSlider.valueChanged.connect(self.onIouChanged)
            
            # 连接复选框值变更信号
            self.showLabelsCheck.toggled.connect(self.onShowLabelsToggled)
            self.showTrailsCheck.toggled.connect(self.onShowTrailsToggled)
            
            # 连接按钮信号
            self.startButton.clicked.connect(self.startTracking)
            self.stopButton.clicked.connect(self.stopTracking)
            self.closeButton.clicked.connect(self.close)
            
            print("信号连接成功")  # 调试输出
        except Exception as e:
            print(f"信号连接失败: {str(e)}")  # 调试输出
        
        # 初始化界面
        self.setupInitialUI()
    
    def setupInitialUI(self):
        """初始化界面设置"""
        # 设置默认值
        self.confSlider.setValue(int(self.confidence_threshold * 10))
        self.iouSlider.setValue(int(self.iou_threshold * 10))
        self.showLabelsCheck.setChecked(self.show_labels)
        self.showTrailsCheck.setChecked(self.show_trails)
        
        # 禁用开始时的停止按钮
        self.stopButton.setEnabled(False)
        
        # 设置初始状态文本
        self.statusLabel.setText("就绪")
    
    def startTracking(self, video_source=None):
        """开始多目标追踪
        
        Args:
            video_source: 视频源 (可以是摄像头索引、视频文件路径或RTSP流URL)
        """
        # 避免重复启动
        if self.is_tracking:
            return
            
        self.is_tracking = True
        
        # 更新UI状态
        self.statusLabel.setText("正在追踪...")
        self.statusLabel.setStyleSheet("color: rgb(0, 200, 255); font-weight: bold;")
        self.startButton.setEnabled(False)
        self.stopButton.setEnabled(True)
        
        # TODO: 在实际应用中，启动真实的追踪线程
        # self.startTrackingThread(video_source)
        
        # 发送追踪开始信号
        self.tracking_started.emit()
        
        # 仅用于演示：使用模拟数据
        self.mockTrackingData()
    
    def stopTracking(self):
        """停止多目标追踪"""
        if not self.is_tracking:
            return
            
        self.is_tracking = False
        
        # 更新UI状态
        self.statusLabel.setText("已停止")
        self.statusLabel.setStyleSheet("color: rgb(255, 100, 100); font-weight: bold;")
        self.startButton.setEnabled(True)
        self.stopButton.setEnabled(False)
        
        # 停止模拟定时器
        if self.mock_timer and self.mock_timer.isActive():
            self.mock_timer.stop()
        
        # TODO: 在实际应用中，停止真实的追踪线程
        # self.stopTrackingThread()
        
        # 发送追踪停止信号
        self.tracking_stopped.emit()
    
    def closeDialog(self):
        """关闭对话框"""
        # 停止所有追踪活动
        self.stopTracking()
        
        # 关闭对话框
        self.close()
    
    def onConfidenceChanged(self, value):
        """置信度滑块值变化处理
        
        Args:
            value: int 滑块值 (1-10)
        """
        self.confidence_threshold = value / 10.0
        self.confValueLabel.setText(f"{self.confidence_threshold:.1f}")
        
        # 若正在追踪，更新阈值
        if self.is_tracking and self.tracking_thread:
            # TODO: 更新追踪线程的阈值
            pass
    
    def onIouChanged(self, value):
        """IOU滑块值变化处理
        
        Args:
            value: int 滑块值 (1-10)
        """
        self.iou_threshold = value / 10.0
        self.iouValueLabel.setText(f"{self.iou_threshold:.1f}")
        
        # 若正在追踪，更新阈值
        if self.is_tracking and self.tracking_thread:
            # TODO: 更新追踪线程的阈值
            pass
    
    def onShowLabelsToggled(self, checked):
        """标签显示复选框状态变化处理
        
        Args:
            checked: bool 是否选中
        """
        self.show_labels = checked
        
        # 若正在追踪，更新显示选项
        if self.is_tracking and self.tracking_thread:
            # TODO: 更新追踪线程的显示选项
            pass
    
    def onShowTrailsToggled(self, checked):
        """轨迹显示复选框状态变化处理
        
        Args:
            checked: bool 是否选中
        """
        self.show_trails = checked
        
        # 若正在追踪，更新显示选项
        if self.is_tracking and self.tracking_thread:
            # TODO: 更新追踪线程的显示选项
            pass
    
    def clearStatistics(self):
        """清除统计数据"""
        # 重置统计标签
        self.totalCountLabel.setText("0")
        self.classCountLabel.setText("载客车: 0  货车: 0  摩托: 0")
        self.speedValueLabel.setText("0 km/h")
        
        # 清除所有目标卡片
        while self.targetsContainerLayout.count() > 1:
            item = self.targetsContainerLayout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
        
        # 重置存储容器
        self.track_targets = {}
        self.target_cards = {}
    
    def exportData(self):
        """导出追踪数据"""
        import os
        import csv
        from datetime import datetime
        from PyQt5.QtWidgets import QFileDialog
        
        # 如果没有数据，则不导出
        if not self.track_targets:
            self.statusLabel.setText("无数据可导出")
            self.statusLabel.setStyleSheet("color: rgb(255, 100, 100); font-weight: bold;")
            return
        
        # 获取保存路径
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        default_filename = f"tracking_data_{timestamp}.csv"
        
        # 打开文件对话框
        filepath, _ = QFileDialog.getSaveFileName(
            self, "保存追踪数据", default_filename, "CSV文件 (*.csv)"
        )
        
        # 用户取消保存
        if not filepath:
            return
        
        # 写入CSV文件
        try:
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                # 创建CSV写入器
                writer = csv.writer(csvfile)
                
                # 写入表头
                writer.writerow(["目标ID", "类别", "置信度", "状态", "轨迹点数", "速度(km/h)"])
                
                # 写入数据
                for target_id, card in self.target_cards.items():
                    # 获取卡片状态信息
                    target_class = card.classLabel.text().replace("类别: ", "")
                    status = "跟踪中" if "跟踪中" in card.statusLabel.text() else "已丢失"
                    confidence = float(card.confidenceLabel.text().replace("置信度: ", ""))
                    track_length = int(card.trackLengthLabel.text().replace("轨迹点: ", ""))
                    speed_text = card.speedLabel.text().replace("速度: ", "").replace(" km/h", "")
                    
                    # 若速度为'--'，则设为0
                    speed = 0 if speed_text == "--" else float(speed_text)
                    
                    # 写入该行数据
                    writer.writerow([target_id, target_class, confidence, status, track_length, speed])
            
            # 导出成功提示
            self.statusLabel.setText(f"数据已导出到: {os.path.basename(filepath)}")
            self.statusLabel.setStyleSheet("color: rgb(0, 230, 150); font-weight: bold;")
                
        except Exception as e:
            # 导出失败提示
            self.statusLabel.setText(f"导出失败: {str(e)}")
            self.statusLabel.setStyleSheet("color: rgb(255, 100, 100); font-weight: bold;")
    
    # --- 拖拽功能实现 ---
    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton and self.titleBar.geometry().contains(event.pos()):
            self.dragging = True
            self.drag_position = event.globalPos() - self.frameGeometry().topLeft()
            event.accept()
    
    def mouseMoveEvent(self, event):
        if event.buttons() == Qt.LeftButton and self.dragging:
            self.move(event.globalPos() - self.drag_position)
            event.accept()
    
    def mouseReleaseEvent(self, event):
        self.dragging = False
