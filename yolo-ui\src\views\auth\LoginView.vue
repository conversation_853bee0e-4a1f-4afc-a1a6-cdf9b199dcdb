<template>
  <div class="login-container">
    <!-- 网格炫酷背景 -->
    <div class="grid-background">
      <div class="grid-lines"></div>
      <div class="grid-dots"></div>
      <div class="floating-particles"></div>
    </div>

    <div class="login-box">
      <div class="login-header">
        <div class="logo-section">
          <div class="logo-icon">🛣️</div>
          <h1>Highway Intelligence</h1>
          <h2>高速公路智能监控系统</h2>
        </div>
        <p class="subtitle">AI-Powered Traffic Monitoring Platform</p>
        <p class="subtitle-cn">基于YOLOv8与ByteTrack的智能监控平台</p>
      </div>

      <a-form
        :model="loginForm"
        :rules="rules"
        @submit="handleLogin"
        layout="vertical"
        class="login-form"
      >
        <a-form-item field="username" label="Username / 用户名">
          <a-input
            v-model="loginForm.username"
            placeholder="Enter your username / 请输入用户名"
            size="large"
            :prefix="IconUser"
            class="modern-input"
          />
        </a-form-item>

        <a-form-item field="password" label="Password / 密码">
          <a-input-password
            v-model="loginForm.password"
            placeholder="Enter your password / 请输入密码"
            size="large"
            :prefix="IconLock"
            class="modern-input"
          />
        </a-form-item>

        <a-form-item>
          <a-button
            type="primary"
            html-type="submit"
            size="large"
            long
            :loading="loading"
            class="login-button"
          >
            <span v-if="!loading">Sign In / 登录</span>
            <span v-else>Signing In... / 登录中...</span>
          </a-button>
        </a-form-item>
      </a-form>


    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { Message } from '@arco-design/web-vue'
import { IconUser, IconLock } from '@arco-design/web-vue/es/icon'
import { useAuthStore } from '@/stores/auth'
import type { LoginRequest } from '@/types/auth'

const router = useRouter()
const authStore = useAuthStore()

const loading = ref(false)
const loginForm = reactive<LoginRequest>({
  username: '',
  password: ''
})

const rules = {
  username: [
    { required: true, message: '请输入用户名' }
  ],
  password: [
    { required: true, message: '请输入密码' }
  ]
}

const handleLogin = async () => {
  loading.value = true
  try {
    const result = await authStore.login(loginForm)
    if (result.success) {
      Message.success('登录成功')
      router.push('/dashboard')
    } else {
      Message.error(result.message || '登录失败')
    }
  } catch (error: any) {
    Message.error(error.message || '登录失败')
  } finally {
    loading.value = false
  }
}


</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%),
    linear-gradient(135deg, #0f0f23 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
  position: relative;
  overflow: hidden;
}

/* 网格炫酷背景 */
.grid-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.grid-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 20s linear infinite;
}

.grid-dots {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(circle, rgba(255, 255, 255, 0.2) 1px, transparent 1px);
  background-size: 25px 25px;
  animation: gridMove 15s linear infinite reverse;
}

.floating-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 20% 30%, rgba(0, 212, 255, 0.3) 2px, transparent 2px),
    radial-gradient(circle at 80% 70%, rgba(255, 107, 107, 0.3) 2px, transparent 2px),
    radial-gradient(circle at 40% 80%, rgba(78, 205, 196, 0.3) 2px, transparent 2px);
  background-size: 100px 100px, 150px 150px, 200px 200px;
  animation: float 25s ease-in-out infinite;
}



.login-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 25% 25%, #00d4ff 0%, transparent 2%),
    radial-gradient(circle at 75% 75%, #ff6b6b 0%, transparent 2%),
    radial-gradient(circle at 50% 10%, #4ecdc4 0%, transparent 1.5%),
    radial-gradient(circle at 10% 90%, #ffe66d 0%, transparent 1.5%),
    radial-gradient(circle at 90% 10%, #ff8a80 0%, transparent 1.5%);
  animation: float 20s ease-in-out infinite;
  opacity: 0.6;
}

.login-container::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: 
    conic-gradient(from 0deg at 50% 50%, transparent 0deg, rgba(0, 212, 255, 0.1) 60deg, transparent 120deg),
    conic-gradient(from 120deg at 50% 50%, transparent 0deg, rgba(255, 107, 107, 0.1) 60deg, transparent 120deg),
    conic-gradient(from 240deg at 50% 50%, transparent 0deg, rgba(78, 205, 196, 0.1) 60deg, transparent 120deg);
  animation: rotate 30s linear infinite;
  pointer-events: none;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-20px) rotate(120deg); }
  66% { transform: translateY(20px) rotate(240deg); }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.login-box {
  width: 480px;
  min-height: 580px;
  padding: 40px 45px;
  background: rgba(255, 255, 255, 0.98);
  border-radius: 20px;
  box-shadow: 
    0 20px 60px rgba(0, 0, 0, 0.15),
    0 8px 25px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(25px);
  border: 1px solid rgba(255, 255, 255, 0.5);
  margin: 0 auto;
  position: relative;
  z-index: 10;
  transform: translateY(0);
  transition: all 0.3s ease;
}

.login-box:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 25px 70px rgba(0, 0, 0, 0.18),
    0 10px 30px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.login-box::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 20px;
  padding: 2px;
  background: linear-gradient(135deg, 
    rgba(0, 212, 255, 0.4) 0%, 
    rgba(255, 107, 107, 0.4) 25%, 
    rgba(78, 205, 196, 0.4) 50%,
    rgba(138, 43, 226, 0.4) 75%,
    rgba(0, 212, 255, 0.4) 100%);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  pointer-events: none;
  animation: borderGlow 4s ease-in-out infinite;
}

@keyframes borderGlow {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.logo-section {
  margin-bottom: 20px;
}

.logo-icon {
  font-size: 48px;
  margin-bottom: 16px;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.login-header h1 {
  font-size: 28px;
  font-weight: 700;
  letter-spacing: 2px;
  line-height: 1.2;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #0066ff 0%, #00d4ff 50%, #4ecdc4 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-family: 'Orbitron', 'Roboto', 'Microsoft YaHei', sans-serif;
  margin-bottom: 8px;
  text-transform: uppercase;
}

.login-header h2 {
  font-size: 16px;
  font-weight: 500;
  color: #4a5568;
  margin-bottom: 0;
  font-family: 'Microsoft YaHei', sans-serif;
  letter-spacing: 1px;
}

.subtitle {
  color: #2d3748;
  font-size: 15px;
  font-weight: 500;
  letter-spacing: 1.2px;
  line-height: 1.4;
  margin: 8px 0 4px 0;
  font-family: 'Roboto', sans-serif;
  text-transform: uppercase;
}

.subtitle-cn {
  color: #718096;
  font-size: 13px;
  font-weight: 400;
  letter-spacing: 0.5px;
  line-height: 1.4;
  margin: 0;
  font-family: 'Microsoft YaHei', sans-serif;
}

.login-form {
  margin-bottom: 25px;
}

.login-form :deep(.arco-form-item-label) {
  font-weight: 600;
  font-size: 14px;
  color: #2d3748;
  font-family: 'Roboto', 'Microsoft YaHei', sans-serif;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
}

.modern-input {
  border-radius: 12px;
  border: 2px solid rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  font-family: 'Roboto', 'Microsoft YaHei', sans-serif;
}

.modern-input:hover {
  border-color: rgba(0, 102, 255, 0.3);
  box-shadow: 0 0 0 3px rgba(0, 102, 255, 0.1);
}

.modern-input:focus {
  border-color: #0066ff;
  box-shadow: 0 0 0 3px rgba(0, 102, 255, 0.2);
}

.login-button {
  height: 50px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 16px;
  letter-spacing: 1px;
  background: linear-gradient(135deg, #0066ff 0%, #00d4ff 100%);
  border: none;
  box-shadow: 0 4px 15px rgba(0, 102, 255, 0.3);
  transition: all 0.3s ease;
  font-family: 'Roboto', 'Microsoft YaHei', sans-serif;
  text-transform: uppercase;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 102, 255, 0.4);
  background: linear-gradient(135deg, #0052cc 0%, #00b8cc 100%);
}

.login-button:active {
  transform: translateY(0);
}

/* 动画关键帧 */
@keyframes gridMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  25% { transform: translateY(-10px) rotate(1deg); }
  50% { transform: translateY(-5px) rotate(0deg); }
  75% { transform: translateY(-15px) rotate(-1deg); }
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 20px rgba(0, 212, 255, 0.3); }
  50% { box-shadow: 0 0 40px rgba(0, 212, 255, 0.6); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-box {
    width: 90%;
    max-width: 400px;
    margin: 20px;
  }

  .grid-lines {
    background-size: 30px 30px;
  }

  .grid-dots {
    background-size: 15px 15px;
  }
}
</style>
