# 高科技风格的违规行为检测对话框界面
from PySide6.QtCore import (QCoreApplication, QMetaObject, QObject, QPoint, QRect,
    QSize, Qt, QPropertyAnimation, QEasingCurve, Signal, Property)
from PySide6.QtGui import (QBrush, QColor, QConicalGradient, QCursor, QFont,
    QFontDatabase, QGradient, QIcon, QLinearGradient, QPainter, QPalette,
    QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QFrame, QLabel, QLineEdit, QPushButton,
    QSizePolicy, QVBoxLayout, QHBoxLayout, QWidget, QGraphicsDropShadowEffect,
    QCheckBox, QScrollArea, QGridLayout)

class ViolationDetectionForm(object):
    def setupUi(self, Form):
        if not Form.objectName():
            Form.setObjectName(u"ViolationDetectionForm")
        
        # 设置窗口大小和样式
        Form.resize(620, 480)
        Form.setMinimumSize(QSize(620, 480))
        Form.setMaximumSize(QSize(620, 480))
        Form.setStyleSheet(u"#ViolationDetectionForm {\n"
                           "    background-color: qlineargradient(x0:0, y0:0, x1:1, y1:1, \n"
                           "                      stop:0 rgb(20, 50, 80), stop:1 rgb(30, 90, 120));\n"
                           "    border-radius: 15px;\n"
                           "    border: 2px solid rgba(255, 120, 0, 0.5);\n"
                           "}")
        
        # 设置无边框窗口
        Form.setWindowFlags(Qt.FramelessWindowHint)
        Form.setAttribute(Qt.WA_TranslucentBackground)
        
        # 主布局
        self.verticalLayout = QVBoxLayout(Form)
        self.verticalLayout.setSpacing(10)
        self.verticalLayout.setContentsMargins(15, 15, 15, 15)
        
        # 标题栏
        self.titleBar = QFrame(Form)
        self.titleBar.setMinimumSize(QSize(0, 40))
        self.titleBar.setMaximumSize(QSize(16777215, 40))
        self.titleBar.setStyleSheet(u"QFrame {\n"
                                   "    border-radius: 8px;\n"
                                   "    background-color: rgba(0, 60, 120, 180);\n"
                                   "}")
        
        # 标题栏布局
        self.horizontalLayout_title = QHBoxLayout(self.titleBar)
        self.horizontalLayout_title.setSpacing(6)
        self.horizontalLayout_title.setContentsMargins(15, 0, 15, 0)
        
        # 标题标签
        self.titleLabel = QLabel(self.titleBar)
        self.titleLabel.setStyleSheet(u"QLabel {\n"
                                     "    color: rgb(255, 255, 255);\n"
                                     "    font: 700 14pt \"Segoe UI\";\n"
                                     "    background-color: transparent;\n"
                                     "}")
        self.titleLabel.setText("交通违规行为检测系统")
        self.horizontalLayout_title.addWidget(self.titleLabel)
        
        # 添加弹性空间
        spacerItem = QWidget()
        spacerItem.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.horizontalLayout_title.addWidget(spacerItem)
        
        # 关闭按钮
        self.closeButton = QPushButton(self.titleBar)
        self.closeButton.setMinimumSize(QSize(24, 24))
        self.closeButton.setMaximumSize(QSize(24, 24))
        self.closeButton.setCursor(QCursor(Qt.PointingHandCursor))
        self.closeButton.setStyleSheet(u"QPushButton {\n"
                                      "    border-radius: 12px;\n"
                                      "    background-color: rgb(255, 80, 80);\n"
                                      "    border: none;\n"
                                      "}\n"
                                      "\n"
                                      "QPushButton:hover {\n"
                                      "    background-color: rgb(255, 120, 120);\n"
                                      "}")
        self.closeButton.setText("")
        self.horizontalLayout_title.addWidget(self.closeButton)
        
        # 添加标题栏到主布局
        self.verticalLayout.addWidget(self.titleBar)
        
        # 内容区域
        self.contentArea = QFrame(Form)
        self.contentArea.setStyleSheet(u"QFrame {\n"
                                     "    border-radius: 10px;\n"
                                     "    background-color: rgba(0, 40, 80, 150);\n"
                                     "}")
        
        # 内容区域布局
        self.verticalLayout_content = QVBoxLayout(self.contentArea)
        self.verticalLayout_content.setSpacing(15)
        self.verticalLayout_content.setContentsMargins(20, 20, 20, 20)
        
        # 添加标题
        self.settingsTitle = QLabel(self.contentArea)
        self.settingsTitle.setStyleSheet(u"QLabel {\n"
                                       "    color: rgb(255, 180, 0);\n"
                                       "    font: 700 12pt \"Segoe UI\";\n"
                                       "    background-color: transparent;\n"
                                       "}")
        self.settingsTitle.setText("请选择要检测的违规类型:")
        self.verticalLayout_content.addWidget(self.settingsTitle)
        
        # 检测设置区域
        self.detectionFrame = QFrame(self.contentArea)
        self.detectionFrame.setMinimumSize(QSize(0, 120))
        self.detectionFrame.setStyleSheet(u"QFrame {\n"
                                        "    border-radius: 10px;\n"
                                        "    background-color: rgba(0, 60, 100, 100);\n"
                                        "    border: 1px solid rgba(0, 180, 255, 80);\n"
                                        "}")
        
        # 创建网格布局
        self.gridLayout = QGridLayout(self.detectionFrame)
        self.gridLayout.setSpacing(15)
        self.gridLayout.setContentsMargins(15, 15, 15, 15)
        
        # 超速检测选项
        self.speedViolationCheck = QCheckBox(self.detectionFrame)
        self.speedViolationCheck.setStyleSheet(u"QCheckBox {\n"
                                             "    color: rgb(255, 255, 255);\n"
                                             "    font: 10pt \"Segoe UI\";\n"
                                             "    background-color: transparent;\n"
                                             "    spacing: 8px;\n"
                                             "}\n"
                                             "\n"
                                             "QCheckBox::indicator {\n"
                                             "    width: 18px;\n"
                                             "    height: 18px;\n"
                                             "    border-radius: 4px;\n"
                                             "    border: 2px solid rgb(0, 120, 210);\n"
                                             "}\n"
                                             "\n"
                                             "QCheckBox::indicator:checked {\n"
                                             "    border: 2px solid rgb(0, 180, 255);\n"
                                             "    background-color: rgb(0, 180, 255);\n"
                                             "    image: url(:/all/img/check_yes.png);\n"
                                             "}")
        self.speedViolationCheck.setText("超速违规检测")
        self.speedViolationCheck.setChecked(True)
        self.gridLayout.addWidget(self.speedViolationCheck, 0, 0)
        
        # 速度阈值标签
        self.speedLimitLabel = QLabel(self.detectionFrame)
        self.speedLimitLabel.setStyleSheet(u"QLabel {\n"
                                          "    color: rgb(180, 200, 255);\n"
                                          "    font: 10pt \"Segoe UI\";\n"
                                          "    background-color: transparent;\n"
                                          "}")
        self.speedLimitLabel.setText("速度阈值:")
        self.gridLayout.addWidget(self.speedLimitLabel, 0, 1)
        
        # 速度阈值输入框
        self.speedLimitEdit = QLineEdit(self.detectionFrame)
        self.speedLimitEdit.setMinimumSize(QSize(0, 30))
        self.speedLimitEdit.setStyleSheet(u"QLineEdit {\n"
                                         "    border-radius: 6px;\n"
                                         "    background-color: rgba(0, 30, 60, 200);\n"
                                         "    color: rgb(255, 255, 255);\n"
                                         "    padding-left: 10px;\n"
                                         "    border: 1px solid rgba(0, 180, 255, 100);\n"
                                         "}")
        self.speedLimitEdit.setText("120")
        self.speedLimitEdit.setAlignment(Qt.AlignCenter)
        self.gridLayout.addWidget(self.speedLimitEdit, 0, 2)
        
        # 逆行检测选项
        self.wrongDirectionCheck = QCheckBox(self.detectionFrame)
        self.wrongDirectionCheck.setStyleSheet(u"QCheckBox {\n"
                                             "    color: rgb(255, 255, 255);\n"
                                             "    font: 10pt \"Segoe UI\";\n"
                                             "    background-color: transparent;\n"
                                             "    spacing: 8px;\n"
                                             "}\n"
                                             "\n"
                                             "QCheckBox::indicator {\n"
                                             "    width: 18px;\n"
                                             "    height: 18px;\n"
                                             "    border-radius: 4px;\n"
                                             "    border: 2px solid rgb(0, 120, 210);\n"
                                             "}\n"
                                             "\n"
                                             "QCheckBox::indicator:checked {\n"
                                             "    border: 2px solid rgb(0, 180, 255);\n"
                                             "    background-color: rgb(0, 180, 255);\n"
                                             "    image: url(:/all/img/check_yes.png);\n"
                                             "}")
        self.wrongDirectionCheck.setText("逆行违规检测")
        self.wrongDirectionCheck.setChecked(True)
        self.gridLayout.addWidget(self.wrongDirectionCheck, 1, 0)
        
        # 违规变道检测选项
        self.laneChangeCheck = QCheckBox(self.detectionFrame)
        self.laneChangeCheck.setStyleSheet(u"QCheckBox {\n"
                                         "    color: rgb(255, 255, 255);\n"
                                         "    font: 10pt \"Segoe UI\";\n"
                                         "    background-color: transparent;\n"
                                         "    spacing: 8px;\n"
                                         "}\n"
                                         "\n"
                                         "QCheckBox::indicator {\n"
                                         "    width: 18px;\n"
                                         "    height: 18px;\n"
                                         "    border-radius: 4px;\n"
                                         "    border: 2px solid rgb(0, 120, 210);\n"
                                         "}\n"
                                         "\n"
                                         "QCheckBox::indicator:checked {\n"
                                         "    border: 2px solid rgb(0, 180, 255);\n"
                                         "    background-color: rgb(0, 180, 255);\n"
                                         "    image: url(:/all/img/check_yes.png);\n"
                                         "}")
        self.laneChangeCheck.setText("违规变道检测")
        self.laneChangeCheck.setChecked(True)
        self.gridLayout.addWidget(self.laneChangeCheck, 2, 0)
        
        # 违规变道阈值设置
        self.laneChangeThresholdLabel = QLabel(self.detectionFrame)
        self.laneChangeThresholdLabel.setStyleSheet(u"QLabel {\n"
                                                  "    color: rgb(180, 200, 255);\n"
                                                  "    font: 10pt \"Segoe UI\";\n"
                                                  "    background-color: transparent;\n"
                                                  "}")
        self.laneChangeThresholdLabel.setText("变道阈值:")
        self.gridLayout.addWidget(self.laneChangeThresholdLabel, 2, 1)
        
        # 违规变道阈值输入框
        self.laneChangeThresholdEdit = QLineEdit(self.detectionFrame)
        self.laneChangeThresholdEdit.setMinimumSize(QSize(0, 30))
        self.laneChangeThresholdEdit.setStyleSheet(u"QLineEdit {\n"
                                                 "    border-radius: 6px;\n"
                                                 "    background-color: rgba(0, 30, 60, 200);\n"
                                                 "    color: rgb(255, 255, 255);\n"
                                                 "    padding-left: 10px;\n"
                                                 "    border: 1px solid rgba(0, 180, 255, 100);\n"
                                                 "}")
        self.laneChangeThresholdEdit.setText("3")
        self.laneChangeThresholdEdit.setAlignment(Qt.AlignCenter)
        self.gridLayout.addWidget(self.laneChangeThresholdEdit, 2, 2)
        
        # 添加设置区域到内容区域
        self.verticalLayout_content.addWidget(self.detectionFrame)
        
        # 违规统计信息区域标题
        self.statisticsTitle = QLabel(self.contentArea)
        self.statisticsTitle.setStyleSheet(u"QLabel {\n"
                                         "    color: rgb(255, 180, 0);\n"
                                         "    font: 700 12pt \"Segoe UI\";\n"
                                         "    background-color: transparent;\n"
                                         "}")
        self.statisticsTitle.setText("实时违规统计:")
        self.verticalLayout_content.addWidget(self.statisticsTitle)
        
        # 违规统计信息区域
        self.statisticsFrame = QFrame(self.contentArea)
        self.statisticsFrame.setMinimumSize(QSize(0, 140))
        self.statisticsFrame.setStyleSheet(u"QFrame {\n"
                                         "    border-radius: 10px;\n"
                                         "    background-color: rgba(0, 60, 100, 100);\n"
                                         "    border: 1px solid rgba(0, 180, 255, 80);\n"
                                         "}")
        
        # 统计区域布局
        self.statisticsLayout = QVBoxLayout(self.statisticsFrame)
        self.statisticsLayout.setSpacing(10)
        self.statisticsLayout.setContentsMargins(15, 15, 15, 15)
        
        # 超速违规统计
        self.speedViolationStats = QLabel(self.statisticsFrame)
        self.speedViolationStats.setStyleSheet(u"QLabel {\n"
                                             "    color: rgb(255, 255, 255);\n"
                                             "    font: 11pt \"Segoe UI\";\n"
                                             "    background-color: transparent;\n"
                                             "}")
        self.speedViolationStats.setText("超速违规: 0 起")
        self.statisticsLayout.addWidget(self.speedViolationStats)
        
        # 逆行违规统计
        self.wrongDirectionStats = QLabel(self.statisticsFrame)
        self.wrongDirectionStats.setStyleSheet(u"QLabel {\n"
                                             "    color: rgb(255, 255, 255);\n"
                                             "    font: 11pt \"Segoe UI\";\n"
                                             "    background-color: transparent;\n"
                                             "}")
        self.wrongDirectionStats.setText("逆行违规: 0 起")
        self.statisticsLayout.addWidget(self.wrongDirectionStats)
        
        # 违规变道统计
        self.laneChangeStats = QLabel(self.statisticsFrame)
        self.laneChangeStats.setStyleSheet(u"QLabel {\n"
                                         "    color: rgb(255, 255, 255);\n"
                                         "    font: 11pt \"Segoe UI\";\n"
                                         "    background-color: transparent;\n"
                                         "}")
        self.laneChangeStats.setText("违规变道: 0 起")
        self.statisticsLayout.addWidget(self.laneChangeStats)
        
        # 总违规统计
        self.totalViolationStats = QLabel(self.statisticsFrame)
        self.totalViolationStats.setStyleSheet(u"QLabel {\n"
                                             "    color: rgb(255, 120, 0);\n"
                                             "    font: 700 12pt \"Segoe UI\";\n"
                                             "    background-color: transparent;\n"
                                             "}")
        self.totalViolationStats.setText("总违规数: 0 起")
        self.statisticsLayout.addWidget(self.totalViolationStats)
        
        # 添加统计区域到内容区域
        self.verticalLayout_content.addWidget(self.statisticsFrame)
        
        # 添加内容区域到主布局
        self.verticalLayout.addWidget(self.contentArea)
        
        # 按钮区域
        self.buttonArea = QFrame(Form)
        self.buttonArea.setMinimumSize(QSize(0, 50))
        self.buttonArea.setMaximumSize(QSize(16777215, 50))
        self.buttonArea.setStyleSheet(u"QFrame {\n"
                                     "    background-color: transparent;\n"
                                     "}")
        
        # 按钮区域布局
        self.horizontalLayout_buttons = QHBoxLayout(self.buttonArea)
        self.horizontalLayout_buttons.setSpacing(15)
        self.horizontalLayout_buttons.setContentsMargins(0, 0, 0, 0)
        
        # 添加弹性空间
        spacerItem2 = QWidget()
        spacerItem2.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.horizontalLayout_buttons.addWidget(spacerItem2)
        
        # 取消按钮
        self.cancelButton = QPushButton(self.buttonArea)
        self.cancelButton.setMinimumSize(QSize(120, 40))
        self.cancelButton.setCursor(QCursor(Qt.PointingHandCursor))
        self.cancelButton.setStyleSheet(u"QPushButton {\n"
                                       "    border-radius: 8px;\n"
                                       "    background-color: rgb(80, 100, 120);\n"
                                       "    color: rgb(255, 255, 255);\n"
                                       "    font: 700 10pt \"Segoe UI\";\n"
                                       "}\n"
                                       "\n"
                                       "QPushButton:hover {\n"
                                       "    background-color: rgb(90, 110, 130);\n"
                                       "}")
        self.cancelButton.setText("取消")
        self.horizontalLayout_buttons.addWidget(self.cancelButton)
        
        # 确认按钮
        self.applyButton = QPushButton(self.buttonArea)
        self.applyButton.setMinimumSize(QSize(120, 40))
        self.applyButton.setCursor(QCursor(Qt.PointingHandCursor))
        self.applyButton.setStyleSheet(u"QPushButton {\n"
                                     "    border-radius: 8px;\n"
                                     "    background-color: rgb(0, 120, 210);\n"
                                     "    color: rgb(255, 255, 255);\n"
                                     "    font: 700 10pt \"Segoe UI\";\n"
                                     "}\n"
                                     "\n"
                                     "QPushButton:hover {\n"
                                     "    background-color: rgb(0, 140, 230);\n"
                                     "}")
        self.applyButton.setText("启用检测")
        self.horizontalLayout_buttons.addWidget(self.applyButton)
        
        # 添加按钮区域到主布局
        self.verticalLayout.addWidget(self.buttonArea)
        
        # 添加阴影效果
        self.addShadow(self.contentArea)
        self.addShadow(self.titleBar)
    
    def addShadow(self, widget):
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect(widget)
        shadow.setBlurRadius(15)
        shadow.setColor(QColor(0, 0, 0, 80))
        shadow.setOffset(0, 0)
        widget.setGraphicsEffect(shadow)


# 实现窗口类
class ViolationDetectionWindow(QWidget):
    # 定义信号
    configConfirmed = Signal(dict)
    statsUpdated = Signal(dict)
    
    def __init__(self):
        super(ViolationDetectionWindow, self).__init__()
        self.ui = ViolationDetectionForm()
        self.ui.setupUi(self)
        
        # 设置窗口无边框和透明背景
        self.setWindowFlags(Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # 连接信号槽
        self.ui.closeButton.clicked.connect(self.close)
        self.ui.cancelButton.clicked.connect(self.close)
        self.ui.applyButton.clicked.connect(self.applyConfig)
        
        # 变量初始化
        self.oldPos = None
        
        # 统计数据
        self.stats = {
            "超速": 0,
            "逆行": 0,
            "违规变道": 0,
            "总数": 0
        }
    
    def applyConfig(self):
        # 获取配置
        config = {
            "speed_detection": self.ui.speedViolationCheck.isChecked(),
            "speed_limit": int(self.ui.speedLimitEdit.text()),
            "wrong_direction_detection": self.ui.wrongDirectionCheck.isChecked(),
            "lane_change_detection": self.ui.laneChangeCheck.isChecked(),
            "lane_change_threshold": int(self.ui.laneChangeThresholdEdit.text())
        }
        # 发送配置信号
        self.configConfirmed.emit(config)
        self.close()
    
    def updateStats(self, stats):
        # 更新统计数据
        self.stats = stats
        # 更新UI显示
        self.ui.speedViolationStats.setText(f"超速违规: {stats['超速']} 起")
        self.ui.wrongDirectionStats.setText(f"逆行违规: {stats['逆行']} 起")
        self.ui.laneChangeStats.setText(f"违规变道: {stats['违规变道']} 起")
        self.ui.totalViolationStats.setText(f"总违规数: {stats['总数']} 起")
    
    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.oldPos = event.globalPosition().toPoint()
    
    def mouseMoveEvent(self, event):
        if self.oldPos and event.buttons() == Qt.LeftButton:
            delta = event.globalPosition().toPoint() - self.oldPos
            self.move(self.x() + delta.x(), self.y() + delta.y())
            self.oldPos = event.globalPosition().toPoint()
