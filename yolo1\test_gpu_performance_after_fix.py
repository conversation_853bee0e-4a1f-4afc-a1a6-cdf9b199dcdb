#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的GPU性能
对比修复前后的检测速度
"""

import torch
import time
import numpy as np
from ultralytics import YOLO
import supervision as sv

def test_gpu_performance():
    """测试GPU性能"""
    print("=" * 60)
    print("测试修复后的GPU性能")
    print("=" * 60)
    
    # 检查环境
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA版本: {torch.version.cuda}")
        print(f"GPU设备: {torch.cuda.get_device_name(0)}")
        print(f"GPU数量: {torch.cuda.device_count()}")
    
    # 加载模型
    print("\n加载YOLO模型...")
    model = YOLO('yolov8n.pt')
    
    # 设置设备
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"使用设备: {device}")
    
    if device == 'cuda':
        model.to('cuda')
        print(f"模型设备: {next(model.model.parameters()).device}")
    
    # 创建测试图像
    test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
    print(f"测试图像尺寸: {test_image.shape}")
    
    # 预热GPU
    print("\n预热GPU...")
    for _ in range(3):
        _ = model.track(test_image, device=device, verbose=False)
    
    # 性能测试
    print("\n开始性能测试...")
    test_iterations = 10
    total_time = 0
    
    for i in range(test_iterations):
        start_time = time.time()
        
        # YOLO检测
        results = model.track(test_image, device=device, verbose=False)
        result = results[0]
        
        # 使用修复后的方法处理检测结果
        if result.boxes.id is not None:
            detections = sv.Detections.from_yolov8(result)
            detections.tracker_id = result.boxes.id.cpu().numpy().astype(int)
            detection_count = len(detections)
        else:
            detection_count = 0
        
        end_time = time.time()
        iteration_time = end_time - start_time
        total_time += iteration_time
        
        print(f"迭代 {i+1:2d}: {iteration_time:.4f}s, 检测到 {detection_count} 个目标")
    
    # 计算平均性能
    avg_time = total_time / test_iterations
    fps = 1.0 / avg_time
    
    print("\n" + "=" * 60)
    print("性能测试结果")
    print("=" * 60)
    print(f"总测试次数: {test_iterations}")
    print(f"总耗时: {total_time:.4f}s")
    print(f"平均耗时: {avg_time:.4f}s")
    print(f"平均FPS: {fps:.2f}")
    print(f"使用设备: {device}")
    
    if device == 'cuda':
        print(f"GPU内存使用: {torch.cuda.memory_allocated(0) / 1024**2:.1f} MB")
        print(f"GPU内存缓存: {torch.cuda.memory_reserved(0) / 1024**2:.1f} MB")
    
    return avg_time, fps

def compare_with_previous_results():
    """与之前的结果对比"""
    print("\n" + "=" * 60)
    print("与修复前性能对比")
    print("=" * 60)
    
    # 之前测试的结果（来自debug_compat_function.py的输出）
    previous_yolo_time = 7.7209  # 之前YOLO检测耗时
    previous_compat_time = 0.0011  # 之前兼容性函数耗时
    previous_total_time = previous_yolo_time + previous_compat_time
    
    # 当前测试结果
    current_time, current_fps = test_gpu_performance()
    
    print(f"\n修复前总耗时: {previous_total_time:.4f}s")
    print(f"修复后总耗时: {current_time:.4f}s")
    print(f"性能提升: {(previous_total_time - current_time) / previous_total_time * 100:.1f}%")
    print(f"速度提升倍数: {previous_total_time / current_time:.1f}x")
    
    if current_time < 1.0:  # 如果检测时间小于1秒，说明修复成功
        print("\n✅ GPU性能修复成功！")
        print("✅ 检测速度已恢复正常")
    else:
        print("\n❌ GPU性能仍有问题")
        print("❌ 需要进一步排查")

if __name__ == "__main__":
    try:
        compare_with_previous_results()
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()