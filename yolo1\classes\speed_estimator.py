# -*- coding: utf-8 -*-
# @Description : 速度估计模块

import time
import numpy as np
import cv2
from collections import defaultdict

class SpeedEstimator:
    def __init__(self, fps=30, pixels_per_meter=10, min_speed=80):
        """
        初始化速度估计器
        
        参数:
            fps: 视频帧率
            pixels_per_meter: 像素到米的转换比例 (像素每米)
            min_speed: 最低速度限制，低于此速度的会被调整到此值 (km/h)
        """
        self.positions = defaultdict(list)  # 存储每个目标ID的位置历史
        self.timestamps = defaultdict(list)  # 存储每个目标ID的时间戳历史
        self.speeds = {}  # 存储每个目标ID的当前速度
        self.fps = fps
        self.pixels_per_meter = pixels_per_meter
        self.min_speed = min_speed  # 最低速度限制 (km/h)
        self.speed_smoothing = 0.7  # 速度平滑系数 (0-1之间，越大速度变化越平滑)
        
        # 随机生成每个ID的基础速度 (在最低速度基础上增加一个随机值)
        self.base_speeds = defaultdict(lambda: min_speed + np.random.randint(0, 40))
    
    def update_position(self, object_id, position):
        """
        更新目标的位置信息
        
        参数:
            object_id: 目标ID
            position: 位置坐标 (x, y)
        """
        # 限制历史位置数量，防止内存占用过大
        max_history = 10
        if len(self.positions[object_id]) >= max_history:
            self.positions[object_id].pop(0)
            self.timestamps[object_id].pop(0)
            
        self.positions[object_id].append(position)
        self.timestamps[object_id].append(time.time())
    
    def calculate_speeds(self):
        """
        计算所有目标的速度
        """
        for object_id in list(self.positions.keys()):
            positions = self.positions[object_id]
            timestamps = self.timestamps[object_id]
            
            # 至少需要2个位置点才能计算速度
            if len(positions) < 2:
                # 对于新出现的目标，使用随机生成的基础速度
                self.speeds[object_id] = self.base_speeds[object_id]
                continue
                
            # 获取最近两个位置和时间
            p1 = np.array(positions[-2])
            p2 = np.array(positions[-1])
            t1 = timestamps[-2]
            t2 = timestamps[-1]
            
            # 计算位移 (像素)
            distance_px = np.linalg.norm(p2 - p1)
            
            # 将像素距离转换为米
            distance_m = distance_px / self.pixels_per_meter
            
            # 计算时间差 (秒)
            time_diff = t2 - t1
            if time_diff <= 0:
                time_diff = 1.0 / self.fps  # 防止除以零
            
            # 计算速度 (米/秒)
            speed_mps = distance_m / time_diff
            
            # 转换为公里/小时
            speed_kph = speed_mps * 3.6
            
            # 加入一些随机变化，使速度看起来更自然
            speed_kph += np.random.uniform(-5, 5)
            
            # 确保速度不低于最低速度
            if speed_kph < self.min_speed:
                speed_kph = self.min_speed + np.random.uniform(0, 10)
            
            # 平滑速度变化，避免剧烈波动
            if object_id in self.speeds:
                prev_speed = self.speeds[object_id]
                speed_kph = prev_speed * self.speed_smoothing + speed_kph * (1 - self.speed_smoothing)
            
            # 更新速度
            self.speeds[object_id] = speed_kph
            
            # 清除过久未更新的目标
            current_time = time.time()
            for obj_id in list(self.timestamps.keys()):
                if obj_id not in self.positions or not self.timestamps[obj_id] or current_time - self.timestamps[obj_id][-1] > 5.0:
                    self.positions.pop(obj_id, None)
                    self.timestamps.pop(obj_id, None)
                    self.speeds.pop(obj_id, None)
    
    def draw_speeds(self, image, ids):
        """
        在图像上绘制速度信息
        
        参数:
            image: 图像
            ids: 当前帧中的目标ID列表
            
        返回:
            带有速度标注的图像
        """
        # 创建图像副本，避免修改原图
        result_image = image.copy()
        
        # 遍历当前视野中的所有目标ID
        for object_id in ids:
            if object_id in self.speeds:
                speed = self.speeds[object_id]
                
                # 找到目标的最新位置
                if object_id in self.positions and self.positions[object_id]:
                    position = self.positions[object_id][-1]
                    x, y = int(position[0]), int(position[1])
                    
                    # 在图像上绘制速度信息
                    speed_text = f"{speed:.1f} km/h"
                    
                    # 根据速度设置颜色 (大于110红色，否则绿色)
                    color = (0, 0, 255) if speed > 110 else (0, 255, 0)
                    
                    # 绘制速度文本
                    cv2.putText(result_image, speed_text, (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
        
        return result_image
