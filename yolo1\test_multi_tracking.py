# -*- coding: utf-8 -*-
# u72ecu7acbu6d4bu8bd5u591au76eeu6807u8ffdu8e2au5bf9u8bddu6846

import sys
from PySide6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QDialog, QLabel

# u521bu5efau4e00u4e2au5b8cu5168u72ecu7acbu7684u6d4bu8bd5u811au672c

def create_test_window():
    app = QApplication(sys.argv)
    
    # u521bu5efau4e3bu7a97u53e3
    main_window = QMainWindow()
    main_window.setWindowTitle("u6d4bu8bd5u591au76eeu6807u8ffdu8e2a")
    main_window.resize(800, 600)
    
    # u521bu5efau4e2du5fc3u90e8u4ef6
    central_widget = QWidget()
    main_window.setCentralWidget(central_widget)
    
    # u521bu5efau5e03u5c40
    layout = QVBoxLayout(central_widget)
    
    # u521bu5efau6309u94ae
    button1 = QPushButton("u6a21u63cf1 - u4f7fu7528u666eu901a QDialog")
    button2 = QPushButton("u6a21u63cf2 - u663eu793au5f39u51fau7a97u53e3")
    
    layout.addWidget(QLabel("u8bf7u70b9u51fbu4e0bu65b9u6309u94aeu6d4bu8bd5u591au76eeu6807u8ffdu8e2au5bf9u8bddu6846"))
    layout.addWidget(button1)
    layout.addWidget(button2)
    
    # u5b9au4e49u5bf9u8bddu6846u5bf9u8c61
    dialog = QDialog(main_window)
    dialog.setWindowTitle("u591au76eeu6807u8ffdu8e2au5bf9u8bddu6846")
    dialog.resize(500, 400)
    
    # u7ed9u5bf9u8bddu6846u6dfbu52a0u5185u5bb9
    dialog_layout = QVBoxLayout(dialog)
    dialog_layout.addWidget(QLabel("u8fd9u662fu591au76eeu6807u8ffdu8e2au5bf9u8bddu6846uff08u6d4bu8bd5u7248uff09"))
    close_button = QPushButton("u5173u95ed")
    close_button.clicked.connect(dialog.close)
    dialog_layout.addWidget(close_button)
    
    # u8fdeu63a5u6309u94aeu4fe1u53f7
    button1.clicked.connect(dialog.show)
    
    # u6309u94ae2u663eu793au7684u5f39u51fau7a97u53e3
    def show_popup():
        popup = QDialog(main_window)
        popup.setWindowTitle("u5f39u51fau7a97u53e3")
        popup_layout = QVBoxLayout(popup)
        popup_layout.addWidget(QLabel("u8fd9u662fu4e00u4e2au6d4bu8bd5u5f39u51fau7a97u53e3"))
        popup.exec()
    
    button2.clicked.connect(show_popup)
    
    # u663eu793au4e3bu7a97u53e3
    main_window.show()
    
    # u8fd0u884cu5e94u7528
    sys.exit(app.exec())

if __name__ == "__main__":
    create_test_window()
