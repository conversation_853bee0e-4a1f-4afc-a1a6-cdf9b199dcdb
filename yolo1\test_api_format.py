#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API返回格式
"""

import requests
import json

def test_api_formats():
    """测试API返回格式"""
    base_url = "http://127.0.0.1:5500"
    
    print("🔐 获取token...")
    
    # 1. 登录获取token
    login_data = {"username": "admin", "password": "123456"}
    response = requests.post(f"{base_url}/api/v1/auth/login", json=login_data)
    
    if response.status_code != 200:
        print(f"❌ 登录失败: {response.status_code}")
        return
    
    login_result = response.json()
    if not login_result.get('success'):
        print(f"❌ 登录失败: {login_result.get('message')}")
        return
    
    token = login_result['data']['token']
    headers = {"Authorization": f"Bearer {token}"}
    print("✅ 登录成功")
    
    # 2. 测试各个API的返回格式
    apis_to_test = [
        {
            "name": "警报统计",
            "endpoint": "/api/v1/analysis/alarms",
            "expected_fields": ["total_alarms", "alarm_by_type", "alarm_by_severity", "alarm_by_monitor", "alarm_trend"]
        },
        {
            "name": "交通流量",
            "endpoint": "/api/v1/analysis/traffic-flow",
            "expected_type": "array",
            "expected_item_fields": ["monitor_id", "monitor_name", "hourly_flow", "daily_flow", "vehicle_types"]
        },
        {
            "name": "概览统计",
            "endpoint": "/api/v1/analysis/statistics/overview",
            "expected_fields": ["total_monitors", "online_monitors", "active_detections", "active_targets", "today_accidents", "active_alarms", "active_users"]
        },
        {
            "name": "系统用户",
            "endpoint": "/api/v1/system/users",
            "expected_fields": ["users", "total", "page", "size"]
        },
        {
            "name": "检测任务",
            "endpoint": "/api/v1/detection/tasks",
            "expected_fields": ["tasks", "total"]
        }
    ]
    
    for api in apis_to_test:
        print(f"\n🧪 测试 {api['name']} API...")
        print(f"   端点: {api['endpoint']}")
        
        try:
            response = requests.get(f"{base_url}{api['endpoint']}", headers=headers, timeout=10)
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"   完整响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                # 检查基本结构
                if result.get('success'):
                    print("   ✅ success字段正确")
                    
                    data = result.get('data')
                    if data is not None:
                        print("   ✅ data字段存在")
                        
                        # 检查期望的字段
                        if api.get('expected_type') == 'array':
                            if isinstance(data, list):
                                print(f"   ✅ data是数组，长度: {len(data)}")
                                if len(data) > 0 and api.get('expected_item_fields'):
                                    item = data[0]
                                    missing_fields = []
                                    for field in api['expected_item_fields']:
                                        if field not in item:
                                            missing_fields.append(field)
                                    
                                    if missing_fields:
                                        print(f"   ❌ 数组项缺少字段: {missing_fields}")
                                    else:
                                        print("   ✅ 数组项包含所有期望字段")
                            else:
                                print(f"   ❌ data应该是数组，实际是: {type(data)}")
                        
                        elif api.get('expected_fields'):
                            missing_fields = []
                            for field in api['expected_fields']:
                                if field not in data:
                                    missing_fields.append(field)
                            
                            if missing_fields:
                                print(f"   ❌ 缺少字段: {missing_fields}")
                                print(f"   实际字段: {list(data.keys()) if isinstance(data, dict) else type(data)}")
                            else:
                                print("   ✅ 包含所有期望字段")
                    else:
                        print("   ❌ 缺少data字段")
                else:
                    print(f"   ❌ success字段错误: {result.get('success')}")
                    print(f"   错误信息: {result.get('message')}")
            else:
                print(f"   ❌ HTTP错误: {response.status_code}")
                print(f"   响应: {response.text}")
                
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")

def main():
    """主函数"""
    print("=" * 80)
    print("🚀 API 返回格式测试")
    print("=" * 80)
    
    test_api_formats()
    
    print("\n" + "=" * 80)
    print("💡 如果API格式不正确:")
    print("   1. 检查后端代码是否已保存并重启")
    print("   2. 检查数据库字段名是否正确")
    print("   3. 检查SQL查询是否有错误")
    print("   4. 清除前端缓存")
    print("=" * 80)

if __name__ == "__main__":
    main()
