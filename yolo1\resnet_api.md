# ResNet API 文档

## 概述

本文档描述了多源多感知检测平台(MTAS)的后端API接口，主要用于高速公路监控场景的对接。API遵循RESTful设计原则，所有响应均使用JSON格式。

**版本**: v2.0.0
**发布日期**: 2025-04-30

## 基础信息

- 基础URL: `http://{host}:{port}`
- 默认端口: 5500
- 响应格式: JSON
- 认证方式: 会话认证(Session)

## 通用响应格式

所有API响应遵循以下格式：

```json
{
  "code": 200,       // 状态码，200表示成功，其他表示失败
  "message": "操作成功", // 操作信息
  "data": {}        // 返回数据，根据接口不同而不同
}
```

## 认证相关API

### 1. 登录

- **URL**: `/login`
- **方法**: POST
- **描述**: 用户登录并创建会话
- **请求参数**:

```json
{
  "username": "admin",  // 用户名
  "password": "123456"  // 密码
}
```

- **成功响应**:

```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "id": 1,
    "avatar": "https://example.com/avatar.jpg",
    "username": "admin"
  }
}
```

### 2. 登出

- **URL**: `/logOut`
- **方法**: GET
- **描述**: 注销用户会话
- **成功响应**:

```json
{
  "code": 200,
  "message": "账号已退出！",
  "data": "账号已退出！"
}
```

## 监控管理API

### 1. 提交监控表单

- **URL**: `/submitMonitorForm`
- **方法**: POST
- **描述**: 创建新的监控点配置
- **请求参数**:

```json
{
  "threshold": 20,                 // 警报阈值
  "conf_threshold": 0.4,           // 置信度阈值
  "iou_threshold": 0.5,            // IOU阈值
  "person": "管理员",              // 配置人员
  "video": "ws://127.0.0.1/live/highway1.mp4", // 推流地址
  "url": "rtsp://127.0.0.1:554/live/highway1", // RTSP地址
  "rtsp_format": "standard",       // RTSP格式类型
  "is_alarm": "开启",              // 警报状态
  "mode": "准确模式",              // 算法模式
  "show_labels": 1,                // 是否显示标签
  "location": "高速路段A",         // 监控地点
  "highway_section": "K268+500",  // 高速路段
  "camera_position": "高杆监控点",  // 摄像头位置
  "remark": "高速公路测试点"         // 备注
}
```

- **成功响应**:

```json
{
  "code": 200,
  "message": "提交成功",
  "data": "添加监控表单成功！"
}
```

### 2. 更新监控表单

- **URL**: `/updateMonitorForm`
- **方法**: POST
- **描述**: 更新现有监控点配置
- **请求参数**:

```json
{
  "id": 1,                        // 监控点ID
  "threshold": 20,                // 警报阈值
  "conf_threshold": 0.4,          // 置信度阈值
  "iou_threshold": 0.5,           // IOU阈值
  "is_alarm": "开启",              // 警报状态
  "mode": "准确模式",              // 算法模式
  "show_labels": 1,               // 是否显示标签
  "highway_section": "K268+500", // 高速路段
  "remark": "已更新配置"            // 备注
}
```

- **成功响应**:

```json
{
  "code": 200,
  "message": "更新成功",
  "data": "更新监控表单成功！"
}
```

### 3. 获取监控列表（分页）

- **URL**: `/getMonitorList`
- **方法**: GET
- **描述**: 分页获取监控点列表
- **请求参数**:
  - `page`: 页码，默认值为1

- **成功响应**:

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total": 10,           // 总记录数
    "page": 1,             // 当前页码
    "size": 10,            // 每页大小
    "list": [              // 数据列表
      {
        "id": 1,
        "threshold": 20,
        "conf_threshold": 0.4,
        "iou_threshold": 0.5,
        "person": "管理员",
        "video": "ws://127.0.0.1/live/highway1.mp4",
        "url": "rtsp://127.0.0.1:554/live/highway1",
        "rtsp_format": "standard",
        "connection_status": "connected",
        "is_alarm": "开启",
        "mode": "准确模式",
        "show_labels": 1,
        "location": "高速路段A",
        "highway_section": "K268+500",
        "camera_position": "高杆监控点",
        "create_time": "2025-04-30 14:00:00",
        "create_by": "admin",
        "remark": "高速公路测试点"
      }
    ]
  }
}
```

## RTSP相关API

### 1. 测试RTSP连接

- **URL**: `/testRTSPConnection`
- **方法**: POST
- **描述**: 测试RTSP连接是否可用
- **请求参数**:

```json
{
  "url": "*************",      // RTSP地址或IP
  "format_type": "standard",  // RTSP格式类型
  "username": "admin",        // 用户名(可选)
  "password": "admin",        // 密码(可选)
  "channel": "1",             // 通道号(可选)
  "port": 554,                // 端口号(可选)
  "custom_url": null          // 自定义URL(可选)
}
```

- **成功响应**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "success": true,            // 连接是否成功
    "message": "连接成功",        // 状态消息
    "formatted_url": "rtsp://admin:admin@*************:554/live/stream1", // 格式化后的URL
    "frame_info": {            // 帧信息
      "width": 1920,
      "height": 1080,
      "fps": 25.0
    }
  }
}
```

### 2. 获取RTSP格式列表

- **URL**: `/getRTSPFormats`
- **方法**: GET
- **描述**: 获取支持的RTSP格式列表
- **成功响应**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "formats": ["standard", "hikvision", "dahua", "axis", "uniview", "custom"],
    "templates": {
      "standard": "rtsp://{ip}:{port}/live/{stream}",
      "hikvision": "rtsp://{username}:{password}@{ip}:{port}/Streaming/Channels/{channel}",
      "dahua": "rtsp://{username}:{password}@{ip}:{port}/cam/realmonitor?channel={channel}&subtype=0",
      "axis": "rtsp://{username}:{password}@{ip}/axis-media/media.amp",
      "uniview": "rtsp://{username}:{password}@{ip}:{port}/video",
      "custom": "{custom_url}"
    }
  }
}
```

## 警报管理API

### 1. 获取警报列表（分页）

- **URL**: `/getAlarmList`
- **方法**: GET
- **描述**: 分页获取警报记录列表
- **请求参数**:
  - `page`: 页码，默认值为1

- **成功响应**:

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total": 25,           // 总记录数
    "page": 1,             // 当前页码
    "size": 10,            // 每页大小
    "list": [              // 数据列表
      {
        "id": 1,
        "location": "高速路段A",
        "highway_section": "K268+500",
        "description": "车流量:25",
        "vehicle_count": 25,
        "detection_details": "{...}",   // JSON格式的检测详情
        "confidence_level": 0.85,       // 检测置信度
        "threshold": 20,
        "photo": "http://127.0.0.1:5500/static/after_img/2025-04-30/2025-04-30-14-00-00_alarm.jpg",
        "pid": 1,
        "create_time": "2025-04-30 14:00:00",
        "remark": "自动检测警报"
      }
    ]
  }
}
```

### 2. 图像识别（Base64）

- **URL**: `/recognize`
- **方法**: POST
- **描述**: 上传Base64编码的图像进行识别
- **请求参数**:

```json
{
  "img": "data:image/jpeg;base64,/9j/4AAQSkZ...", // Base64编码的图像
  "conf_threshold": 0.4,                          // 置信度阈值(可选)
  "iou_threshold": 0.5                           // IOU阈值(可选)
}
```

- **成功响应**:

```json
{
  "code": 200,
  "message": "识别成功",
  "data": {
    "before_img": "http://127.0.0.1:5500/static/before_img/2025-04-30/2025-04-30-14-00-00_xxx.jpg",
    "after_img": "http://127.0.0.1:5500/static/after_img/2025-04-30/2025-04-30-14-00-00_xxx.jpg",
    "vehicle_count": 15,
    "detection_details": {...},  // 检测详细信息
    "confidence_level": 0.82     // 平均置信度
  }
}
```

### 3. 摄像头拍照识别

- **URL**: `/recognizePhoto`
- **方法**: POST
- **描述**: 上传摄像头拍照进行识别
- **请求参数**:

```json
{
  "img": "data:image/jpeg;base64,/9j/4AAQSkZ...", // Base64编码的图像
  "conf_threshold": 0.4,                          // 置信度阈值(可选)
  "iou_threshold": 0.5,                          // IOU阈值(可选)
  "location": "高速路段A",                      // 位置信息
  "highway_section": "K268+500"                // 路段信息
}
```

- **成功响应**:

```json
{
  "code": 200,
  "message": "识别成功",
  "data": {
    "before_img": "http://127.0.0.1:5500/static/before_img/2025-04-30/2025-04-30-14-00-00_xxx.jpg",
    "after_img": "http://127.0.0.1:5500/static/after_img/2025-04-30/2025-04-30-14-00-00_xxx.jpg",
    "vehicle_count": 15,
    "detection_details": {...},  // 检测详细信息
    "confidence_level": 0.82     // 平均置信度
  }
}
```

## 图像处理配置 API

### 1. 保存图像处理配置

- **URL**: `/saveImageProcessingConfig`
- **方法**: POST
- **描述**: 保存图像处理相关配置
- **请求参数**:

```json
{
  "monitor_id": 1,               // 监控点ID
  "show_labels": true,           // 是否显示标签
  "show_boxes": true,            // 是否显示边框
  "show_confidence": false,      // 是否显示置信度
  "draw_on_original": false,     // 是否在原始图像上绘制
  "box_color": "#FF0000",       // 框的颜色
  "font_size": 1.0,              // 字体大小
  "line_thickness": 2            // 线条粗细
}
```

- **成功响应**:

```json
{
  "code": 200,
  "message": "保存成功",
  "data": "图像处理配置已更新"
}
```

### 2. 获取图像处理配置

- **URL**: `/getImageProcessingConfig`
- **方法**: GET
- **描述**: 获取图像处理配置
- **请求参数**:
  - `monitor_id`: 监控点ID

- **成功响应**:

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "monitor_id": 1,
    "show_labels": true,
    "show_boxes": true,
    "show_confidence": false,
    "draw_on_original": false,
    "box_color": "#FF0000",
    "font_size": 1.0,
    "line_thickness": 2
  }
}
```

## 系统配置 API

### 1. 获取系统配置

- **URL**: `/getSystemConfig`
- **方法**: GET
- **描述**: 获取系统全局配置
- **成功响应**:

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "api_version": "2.0.0",
    "server_info": {
      "host": "127.0.0.1",
      "port": 5500
    },
    "image_storage": {
      "before_path": "static/before_img",
      "after_path": "static/after_img"
    },
    "model_info": {
      "name": "YOLOv8",
      "version": "8.0.0",
      "default_conf": 0.3,
      "default_iou": 0.5
    },
    "rtsp_defaults": {
      "default_format": "standard",
      "default_port": 554,
      "timeout": 3
    }
  }
}
```

## 错误代码

| 状态码 | 描述 |
|------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 认证失败或未登录 |
| 403 | 无操作权限 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 注意事项

1. 所有使用认证的API都需要先调用`/login`接口进行登录
2. 服务端会限制对非授权资源的访问
3. 所有图像识别的接口都有可能需要较长时间执行，建议前端实现运行中的加载动画
4. RTSP连接测试会消耗服务器资源，建议限制调用频率
5. `/recognize`和`/recognizePhoto`接口的响应时间取决于图像大小和复杂度
