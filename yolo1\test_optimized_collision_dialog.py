# -*- coding: utf-8 -*-
# @Description : 测试优化后的车辆碰撞检测对话框
# @Date : 2025年6月21日

import sys
import os
from datetime import datetime
from PySide6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ui.dialog.collision_detection_dialog import CollisionDetectionDialog

class TestMainWindow(QMainWindow):
    """测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("碰撞检测对话框测试")
        self.setGeometry(100, 100, 400, 300)
        
        # 设置现代化样式
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 248, 255, 0.95),
                    stop:1 rgba(219, 234, 254, 0.95));
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgb(59, 130, 246), stop:1 rgb(37, 99, 235));
                color: white;
                border-radius: 10px;
                padding: 15px 30px;
                font-weight: 600;
                font-size: 16px;
                border: none;
                box-shadow: 0 4px 6px rgba(59, 130, 246, 0.3);
                font-family: 'Microsoft YaHei UI', 'PingFang SC', sans-serif;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgb(37, 99, 235), stop:1 rgb(29, 78, 216));
                transform: translateY(-2px);
                box-shadow: 0 6px 8px rgba(59, 130, 246, 0.4);
            }
            QPushButton:pressed {
                background: rgb(29, 78, 216);
                transform: translateY(0px);
                box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
            }
        """)
        
        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setAlignment(Qt.AlignCenter)
        
        # 测试按钮
        test_button = QPushButton("🚗 打开碰撞检测对话框")
        test_button.clicked.connect(self.open_collision_dialog)
        layout.addWidget(test_button)
        
    def open_collision_dialog(self):
        """打开碰撞检测对话框"""
        dialog = CollisionDetectionDialog(self)
        
        # 模拟一些碰撞数据用于测试
        self.simulate_collision_data(dialog)
        
        dialog.exec()
        
    def simulate_collision_data(self, dialog):
        """模拟碰撞数据"""
        # 模拟碰撞事件
        collision_data = [
            {
                'id1': 'Car_001',
                'id2': 'Car_002',
                'position': [320, 240],
                'distance': 25.5,
                'speed_change1': 18.2,
                'speed_change2': 0.3,
                'time': datetime.now(),
                'severity': '严重'
            },
            {
                'id1': 'Car_003',
                'id2': 'Car_004',
                'position': [450, 180],
                'distance': 45.8,
                'speed_change1': 12.1,
                'speed_change2': 0.15,
                'time': datetime.now(),
                'severity': '中等'
            },
            {
                'id1': 'Car_005',
                'id2': 'Car_006',
                'position': [280, 350],
                'distance': 65.2,
                'speed_change1': 8.5,
                'speed_change2': 0.08,
                'time': datetime.now(),
                'severity': '轻微'
            }
        ]
        
        # 添加模拟数据到对话框
        for collision in collision_data:
            dialog.add_collision_result(collision)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序字体
    font = QFont("Microsoft YaHei UI", 10)
    app.setFont(font)
    
    # 创建主窗口
    window = TestMainWindow()
    window.show()
    
    print("\n=== 碰撞检测对话框优化测试 ===")
    print("✅ 右侧面板已优化为现代化浅色透明风格")
    print("✅ 与左侧面板设计风格保持一致")
    print("✅ 包含以下优化:")
    print("   - 浅蓝色渐变背景")
    print("   - 现代化边框和阴影")
    print("   - 统一的字体和颜色方案")
    print("   - 优化的按钮样式")
    print("   - 改进的交互效果")
    print("\n点击按钮查看优化后的对话框效果...")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()