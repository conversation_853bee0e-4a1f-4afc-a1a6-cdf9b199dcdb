# -*- coding: utf-8 -*-
# <AUTHOR> pan
# @Description : RTSP u5de5u5177u7c7b

import cv2
import re
import time

class RTSPUtils:
    """RTSPu5de5u5177u7c7b - u5904u7406RTSPu8fdeu63a5u76f8u5173u903bu8f91"""
    
    # u5e38u89c1RTSPu683cu5f0fu6a21u677f
    RTSP_FORMATS = {
        'standard': 'rtsp://{ip}:{port}/live/{stream}',
        'hikvision': 'rtsp://{username}:{password}@{ip}:{port}/Streaming/Channels/{channel}',
        'dahua': 'rtsp://{username}:{password}@{ip}:{port}/cam/realmonitor?channel={channel}&subtype=0',
        'axis': 'rtsp://{username}:{password}@{ip}/axis-media/media.amp',
        'uniview': 'rtsp://{username}:{password}@{ip}:{port}/video',
        'custom': '{custom_url}'
    }
    
    @staticmethod
    def format_rtsp_url(ip, port=554, format_type='standard', stream='stream1', 
                        username='admin', password='admin', channel='1', custom_url=None):
        """u683cu5f0fu5316RTSP URL"""
        if format_type not in RTSPUtils.RTSP_FORMATS:
            format_type = 'standard'
            
        # u5982u679cu662fu81eau5b9au4e49u683cu5f0fu4e14u63d0u4f9bu4e86URL
        if format_type == 'custom' and custom_url:
            return custom_url
            
        # u68c0u67e5IP:Portu683cu5f0fu5e76u62c6u5206
        if ':' in ip and port == 554:
            # u5c1du8bd5u5206u79bbIPu548cu7aefu53e3
            try:
                ip_parts = ip.split(':')
                ip = ip_parts[0]
                port = int(ip_parts[1])
            except:
                # u5982u679cu5206u5272u5931u8d25uff0cu4fddu6301u539fu6837
                pass
                
        # u4f7fu7528u6a21u677fu683cu5f0fu5316URL
        template = RTSPUtils.RTSP_FORMATS[format_type]
        
        return template.format(
            ip=ip,
            port=port,
            username=username,
            password=password,
            channel=channel,
            stream=stream,
            custom_url=custom_url
        )
    
    @staticmethod
    def test_rtsp_connection(url, timeout=3):
        """u6d4bu8bd5RTSPu8fdeu63a5u662fu5426u53efu7528
        
        Args:
            url: RTSP URL
            timeout: u8fdeu63a5u8d85u65f6u65f6u95f4(u79d2)
            
        Returns:
            dict: {
                'success': bool,  # u662fu5426u8fdeu63a5u6210u529f
                'message': str,   # u6d88u606f
                'frame_info': dict # u5e27u4fe1u606fuff0cu5982u5206u8fa8u7387u7b49
            }
        """
        try:
            # u6253u5f00RTSPu6d41
            cap = cv2.VideoCapture(url)
            
            # u8bbeu7f6eu8d85u65f6
            start_time = time.time()
            success = False
            
            # u5c1du8bd5u8bfbu53d6u7b2cu4e00u5e27
            while time.time() - start_time < timeout:
                ret, frame = cap.read()
                if ret:
                    success = True
                    break
                time.sleep(0.1)
            
            if not success:
                return {
                    'success': False,
                    'message': 'u65e0u6cd5u8bfbu53d6u89c6u9891u6d41uff0cu8bf7u68c0u67e5URLu683cu5f0fu6216u7f51u7edcu8fdeu63a5',
                    'frame_info': None
                }
            
            # u83b7u53d6u89c6u9891u4fe1u606f
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            
            # u91cau653eu8d44u6e90
            cap.release()
            
            return {
                'success': True,
                'message': 'u8fdeu63a5u6210u529f',
                'frame_info': {
                    'width': width,
                    'height': height,
                    'fps': fps
                }
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'u8fdeu63a5u5931u8d25: {str(e)}',
                'frame_info': None
            }
