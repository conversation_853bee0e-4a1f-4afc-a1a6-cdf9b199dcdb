Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple
Requirement already satisfied: opencv-python==******** in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (********)
Requirement already satisfied: pillow==9.5.0 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (9.5.0)
Requirement already satisfied: ultralytics in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (8.3.156)
Requirement already satisfied: numpy>=1.17.0 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from opencv-python==********) (1.24.3)
Requirement already satisfied: matplotlib>=3.3.0 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from ultralytics) (3.7.2)
Requirement already satisfied: pyyaml>=5.3.1 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from ultralytics) (6.0.2)
Requirement already satisfied: requests>=2.23.0 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from ultralytics) (2.32.4)
Requirement already satisfied: scipy>=1.4.1 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from ultralytics) (1.10.1)
Requirement already satisfied: torch>=1.8.0 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from ultralytics) (2.1.2+cu118)
Requirement already satisfied: torchvision>=0.9.0 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from ultralytics) (0.22.1)
Requirement already satisfied: tqdm>=4.64.0 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from ultralytics) (4.67.1)
Requirement already satisfied: psutil in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from ultralytics) (7.0.0)
Requirement already satisfied: py-cpuinfo in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from ultralytics) (9.0.0)
Requirement already satisfied: pandas>=1.1.4 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from ultralytics) (2.3.0)
Requirement already satisfied: ultralytics-thop>=2.0.0 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from ultralytics) (2.0.14)
Requirement already satisfied: contourpy>=1.0.1 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from matplotlib>=3.3.0->ultralytics) (1.3.0)
Requirement already satisfied: cycler>=0.10 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from matplotlib>=3.3.0->ultralytics) (0.12.1)
Requirement already satisfied: fonttools>=4.22.0 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from matplotlib>=3.3.0->ultralytics) (4.58.4)
Requirement already satisfied: kiwisolver>=1.0.1 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from matplotlib>=3.3.0->ultralytics) (1.4.7)
Requirement already satisfied: packaging>=20.0 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from matplotlib>=3.3.0->ultralytics) (25.0)
Requirement already satisfied: pyparsing<3.1,>=2.3.1 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from matplotlib>=3.3.0->ultralytics) (3.0.9)
Requirement already satisfied: python-dateutil>=2.7 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from matplotlib>=3.3.0->ultralytics) (2.9.0.post0)
Requirement already satisfied: importlib-resources>=3.2.0 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from matplotlib>=3.3.0->ultralytics) (6.5.2)
Requirement already satisfied: zipp>=3.1.0 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from importlib-resources>=3.2.0->matplotlib>=3.3.0->ultralytics) (3.23.0)
Requirement already satisfied: pytz>=2020.1 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from pandas>=1.1.4->ultralytics) (2025.2)
Requirement already satisfied: tzdata>=2022.7 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from pandas>=1.1.4->ultralytics) (2025.2)
Requirement already satisfied: six>=1.5 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from python-dateutil>=2.7->matplotlib>=3.3.0->ultralytics) (1.17.0)
Requirement already satisfied: charset_normalizer<4,>=2 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from requests>=2.23.0->ultralytics) (3.4.2)
Requirement already satisfied: idna<4,>=2.5 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from requests>=2.23.0->ultralytics) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from requests>=2.23.0->ultralytics) (2.5.0)
Requirement already satisfied: certifi>=2017.4.17 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from requests>=2.23.0->ultralytics) (2025.6.15)
Requirement already satisfied: filelock in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from torch>=1.8.0->ultralytics) (3.18.0)
Requirement already satisfied: typing-extensions in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from torch>=1.8.0->ultralytics) (4.14.0)
Requirement already satisfied: sympy in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from torch>=1.8.0->ultralytics) (1.14.0)
Requirement already satisfied: networkx in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from torch>=1.8.0->ultralytics) (3.2.1)
Requirement already satisfied: jinja2 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from torch>=1.8.0->ultralytics) (3.1.6)
Requirement already satisfied: fsspec in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from torch>=1.8.0->ultralytics) (2025.5.1)
Collecting torch>=1.8.0 (from ultralytics)
  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/ed/e3/80230d0eec3a4dd1b5d2b423e663026452ac8ffb64aeac1619febc1b4ac7/torch-2.7.1-cp39-cp39-win_amd64.whl (216.0 MB)
     -------------------------------------- 216.0/216.0 MB 5.5 MB/s eta 0:00:00
Requirement already satisfied: mpmath<1.4,>=1.1.0 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from sympy->torch>=1.8.0->ultralytics) (1.3.0)
Requirement already satisfied: colorama in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from tqdm>=4.64.0->ultralytics) (0.4.6)
Requirement already satisfied: MarkupSafe>=2.0 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from jinja2->torch>=1.8.0->ultralytics) (3.0.2)
Installing collected packages: torch
  Attempting uninstall: torch
    Found existing installation: torch 2.1.2+cu118
    Uninstalling torch-2.1.2+cu118:
      Successfully uninstalled torch-2.1.2+cu118
Successfully installed torch-2.7.1
