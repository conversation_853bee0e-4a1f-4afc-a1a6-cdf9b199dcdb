/* 全局样式 */
#Main_QF {
    background-color: qlineargradient(x0:0, y0:1, x1:1, y1:1,stop:0.4 rgb(240, 245, 248), stop:1 rgb(214, 227, 231));
    border: 0px solid red;
    border-radius: 20px;
}

/* 标题样式 */
#Title {
    font: 600 14pt "Microsoft YaHei";
    color: #2c3e50;
}

/* 信息卡片样式 */
#Class_QF {
    background-color: rgba(149, 117, 205, 0.85);
    border-radius: 15px;
}

#Target_QF {
    background-color: rgba(255, 145, 124, 0.85);
    border-radius: 15px;
}

#Fps_QF {
    background-color: rgba(147, 129, 255, 0.85);
    border-radius: 15px;
}

#Model_QF {
    background-color: rgba(79, 195, 189, 0.85);
    border-radius: 15px;
}

/* 侧边栏按钮样式 */
#LeftMenuBg QPushButton {
    background-repeat: no-repeat;
    background-position: left center;
    border: none;
    border-left: 20px solid transparent;
    text-align: left;
    padding-left: 40px;
    color: white;
    font: 500 11pt "Microsoft YaHei";
    border-radius: 10px;
    margin: 2px 10px;
}

#LeftMenuBg QPushButton:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

#LeftMenuBg QPushButton:pressed {
    background-color: rgba(255, 255, 255, 0.3);
}

/* 进度条样式 */
QProgressBar {
    border: none;
    background-color: rgba(255, 255, 255, 0.3);
    height: 10px;
    border-radius: 5px;
}

QProgressBar::chunk {
    background-color: #ff6b6b;
    border-radius: 5px;
}

/* 开关样式 */
QCheckBox {
    spacing: 8px;
    color: white;
    font: 400 10pt "Microsoft YaHei";
}

QCheckBox::indicator {
    width: 30px;
    height: 15px;
    border-radius: 8px;
    background-color: rgba(255, 255, 255, 0.3);
}

QCheckBox::indicator:checked {
    background-color: #4fd1c5;
}