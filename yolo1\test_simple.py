# -*- coding: utf-8 -*-
# @Description : 简单测试修复结果
# @Date : 2025年6月21日

import sys
import os

def test_imports():
    """测试基本导入"""
    print("🔍 测试基本导入...")
    
    try:
        import supervision as sv
        print("✅ supervision导入成功")
        
        # 检查API
        has_ultralytics = hasattr(sv.Detections, 'from_ultralytics')
        has_yolov8 = hasattr(sv.Detections, 'from_yolov8')
        
        print(f"   from_ultralytics: {has_ultralytics}")
        print(f"   from_yolov8: {has_yolov8}")
        
        if has_ultralytics:
            print("✅ 推荐使用新版API")
        elif has_yolov8:
            print("⚠️ 使用旧版API")
        else:
            print("❌ 需要手动创建")
            
        return True
        
    except Exception as e:
        print(f"❌ supervision导入失败: {e}")
        return False

def test_yolo_import():
    """测试YOLO导入"""
    print("\n🎯 测试YOLO导入...")
    
    try:
        from ultralytics import YOLO
        print("✅ YOLO导入成功")
        return True
    except Exception as e:
        print(f"❌ YOLO导入失败: {e}")
        return False

def test_main_imports():
    """测试主要模块导入"""
    print("\n📁 测试主要模块导入...")
    
    try:
        # 测试classes.yolo导入
        sys.path.append('.')
        from classes.yolo import YoloPredictor
        print("✅ classes.yolo导入成功")
        
        # 测试是否有兼容性函数
        if hasattr(YoloPredictor, 'create_detections_from_result'):
            print("✅ 兼容性函数存在")
        else:
            print("⚠️ 兼容性函数不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 主要模块导入失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 简单修复验证测试")
    print("="*50)
    
    # 测试基本导入
    imports_ok = test_imports()
    
    # 测试YOLO导入
    yolo_ok = test_yolo_import()
    
    # 测试主要模块
    main_ok = test_main_imports()
    
    # 总结
    print(f"\n📊 测试结果:")
    print(f"   基本导入: {'✅' if imports_ok else '❌'}")
    print(f"   YOLO导入: {'✅' if yolo_ok else '❌'}")
    print(f"   主要模块: {'✅' if main_ok else '❌'}")
    
    all_ok = imports_ok and yolo_ok and main_ok
    
    if all_ok:
        print("\n🎊 基本测试通过！")
        print("\n现在可以尝试启动主程序:")
        print("   python main.py")
    else:
        print("\n⚠️ 部分测试失败")
    
    return all_ok

if __name__ == "__main__":
    main()
