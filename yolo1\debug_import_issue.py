#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试导入问题 - 逐步导入模块找出问题
"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(override=True, dotenv_path='config/end-back.env')

# 添加backend路径
sys.path.append('backend')

def test_import_step_by_step():
    """逐步导入模块测试"""
    print("🔍 逐步导入模块测试")
    print("=" * 50)
    
    try:
        print("1. 导入基础模块...")
        from flask import Flask
        print("   ✅ Flask导入成功")
        
        from flask_cors import CORS
        print("   ✅ CORS导入成功")
        
        print("\n2. 导入utils模块...")
        try:
            from utils.response import success_response, error_response
            print("   ✅ response模块导入成功")
        except Exception as e:
            print(f"   ❌ response模块导入失败: {e}")
            return False
        
        try:
            from utils.database import DatabaseManager
            print("   ✅ DatabaseManager导入成功")
        except Exception as e:
            print(f"   ❌ DatabaseManager导入失败: {e}")
            return False
        
        try:
            from utils.database import get_db_connection
            print("   ✅ get_db_connection导入成功")
        except Exception as e:
            print(f"   ❌ get_db_connection导入失败: {e}")
            return False
        
        try:
            from utils.database import init_database
            print("   ✅ init_database导入成功")
        except Exception as e:
            print(f"   ❌ init_database导入失败: {e}")
            return False
        
        print("\n3. 导入API模块...")
        try:
            from api import api_v1
            print("   ✅ api_v1蓝图导入成功")
        except Exception as e:
            print(f"   ❌ api_v1蓝图导入失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        print("\n4. 测试数据库连接...")
        try:
            # 测试数据库连接
            with get_db_connection() as db:
                db.execute("SELECT 1")
                result = db.fetchone()
                print(f"   ✅ 数据库连接测试成功: {result}")
        except Exception as e:
            print(f"   ❌ 数据库连接测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        print("\n5. 创建Flask应用...")
        try:
            app = Flask(__name__)
            app.config['SECRET_KEY'] = 'yolo-highway-monitoring-system-2025'
            CORS(app, supports_credentials=True, origins="*")
            print("   ✅ Flask应用创建成功")
        except Exception as e:
            print(f"   ❌ Flask应用创建失败: {e}")
            return False
        
        print("\n6. 注册蓝图...")
        try:
            app.register_blueprint(api_v1)
            print("   ✅ 蓝图注册成功")
        except Exception as e:
            print(f"   ❌ 蓝图注册失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        print("\n7. 测试路由...")
        with app.test_client() as client:
            try:
                # 测试根路径
                response = client.get('/')
                print(f"   根路径状态码: {response.status_code}")
                
                # 测试登录路径（GET请求，应该返回405）
                response = client.get('/api/v1/auth/login')
                print(f"   登录路径GET状态码: {response.status_code}")
                
                # 测试登录路径（POST请求）
                response = client.post('/api/v1/auth/login', 
                                     json={'username': 'admin', 'password': '123456'},
                                     content_type='application/json')
                print(f"   登录路径POST状态码: {response.status_code}")
                print(f"   登录响应: {response.get_json()}")
                
            except Exception as e:
                print(f"   ❌ 路由测试失败: {e}")
                import traceback
                traceback.print_exc()
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_individual_api_modules():
    """单独测试各个API模块"""
    print("\n🔍 单独测试各个API模块")
    print("=" * 50)
    
    api_modules = [
        'auth',
        'monitor', 
        'detection',
        'analysis',
        'system',
        'realtime',
        'violation',
        'accident',
        'tracking',
        'config'
    ]
    
    for module_name in api_modules:
        try:
            print(f"导入 api.{module_name}...")
            module = __import__(f'api.{module_name}', fromlist=[module_name])
            print(f"   ✅ api.{module_name} 导入成功")
        except Exception as e:
            print(f"   ❌ api.{module_name} 导入失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    return True

def main():
    """主函数"""
    print("=" * 80)
    print("🚀 调试导入问题")
    print("=" * 80)
    
    # 1. 逐步导入测试
    step_success = test_import_step_by_step()
    
    if not step_success:
        print("\n❌ 逐步导入测试失败")
        return
    
    # 2. 单独测试API模块
    api_success = test_individual_api_modules()
    
    print("\n" + "=" * 80)
    if step_success and api_success:
        print("🎉 所有导入测试成功！")
        print("💡 问题可能在于:")
        print("   1. Flask应用的配置")
        print("   2. 请求处理过程中的某个环节")
        print("   3. 数据库连接的上下文管理")
    else:
        print("⚠️ 导入测试发现问题")
        print("💡 请根据上述错误信息修复相应模块")
    print("=" * 80)

if __name__ == "__main__":
    main()
