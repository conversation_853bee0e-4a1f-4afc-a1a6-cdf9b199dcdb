#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试重新设计的行人检测对话框
简洁功能版 - 删除多余样式，专注核心功能
"""

import sys
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

# 添加项目路径
sys.path.append('.')
from ui.dialog.simple_pedestrian_dialog import SimplePedestrianDialog

class TestMainWindow(QMainWindow):
    """测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("行人检测对话框测试 - 简洁版")
        self.setGeometry(100, 100, 300, 200)
        
        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 布局
        layout = QVBoxLayout(central_widget)
        layout.setAlignment(Qt.AlignCenter)
        
        # 说明标签
        info_label = QLabel("行人检测对话框 - 简洁功能版")
        info_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: rgb(50, 50, 50);
                margin: 20px;
            }
        """)
        info_label.setAlignment(Qt.AlignCenter)
        
        # 测试按钮
        test_btn = QPushButton("打开行人检测对话框")
        test_btn.setFixedSize(200, 50)
        test_btn.setStyleSheet("""
            QPushButton {
                background: rgba(0, 120, 200, 0.8);
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: rgba(0, 140, 220, 0.9);
            }
            QPushButton:pressed {
                background: rgba(0, 100, 180, 0.9);
            }
        """)
        test_btn.clicked.connect(self.show_pedestrian_dialog)
        
        layout.addWidget(info_label)
        layout.addWidget(test_btn)
        
        # 输出重新设计说明
        print("\n=== 行人检测对话框重新设计 - 简洁功能版 ===")
        print("✓ 删除了大量多余的复杂样式")
        print("✓ 简化了UI布局，使用QGroupBox组织功能模块")
        print("✓ 保留了核心算法功能：置信度配置、检测统计、控制按钮")
        print("✓ 窗口尺寸从600x450缩小到400x300，更加紧凑")
        print("✓ 使用简洁的颜色方案，避免文字遮挡")
        print("✓ 功能模块清晰：检测配置 -> 检测统计 -> 控制按钮")
        print("✓ 移除了不必要的图标和装饰元素")
        print("✓ 保持了拖拽功能和基本交互")
        print("\n点击按钮查看重新设计的效果...\n")
        
    def show_pedestrian_dialog(self):
        """显示行人检测对话框"""
        dialog = SimplePedestrianDialog(self)
        
        # 连接信号
        dialog.detectionStarted.connect(self.on_detection_started)
        dialog.detectionStopped.connect(self.on_detection_stopped)
        dialog.pedestrianDetected.connect(self.on_pedestrian_detected)
        
        dialog.exec()
        
    def on_detection_started(self, config):
        """检测开始"""
        print(f"检测开始 - 配置: {config}")
        
    def on_detection_stopped(self):
        """检测停止"""
        print("检测停止")
        
    def on_pedestrian_detected(self, count):
        """检测到行人"""
        print(f"检测到行人: {count} 人")

if __name__ == '__main__':
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyleSheet("""
        QMainWindow {
            background: rgba(240, 240, 240, 1);
        }
    """)
    
    window = TestMainWindow()
    window.show()
    
    sys.exit(app.exec())