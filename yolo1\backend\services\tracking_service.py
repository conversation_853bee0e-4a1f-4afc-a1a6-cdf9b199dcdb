# @Description : 追踪算法管理服务
# @Date : 2025年6月20日

import json
import threading
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
import cv2
import numpy as np
from utils.database import get_db_connection

class TrackingService:
    """追踪算法管理服务类"""
    
    def __init__(self):
        self.tracking_instances = {}   # monitor_id -> tracker instance
        self.tracking_threads = {}     # monitor_id -> thread
        self.tracking_status = {}      # monitor_id -> status
        self.performance_data = {}     # monitor_id -> performance metrics
        
        # 支持的追踪算法
        self.available_algorithms = [
            {
                'name': 'bytetrack',
                'display_name': 'ByteTrack',
                'description': '高性能多目标追踪算法',
                'parameters': {
                    'track_thresh': {'type': 'float', 'default': 0.5, 'range': [0.1, 0.9]},
                    'track_buffer': {'type': 'int', 'default': 30, 'range': [10, 100]},
                    'match_thresh': {'type': 'float', 'default': 0.8, 'range': [0.1, 0.9]}
                }
            },
            {
                'name': 'deepsort',
                'display_name': 'DeepSORT',
                'description': '基于深度学习的目标追踪算法',
                'parameters': {
                    'max_dist': {'type': 'float', 'default': 0.2, 'range': [0.1, 0.5]},
                    'min_confidence': {'type': 'float', 'default': 0.3, 'range': [0.1, 0.9]},
                    'nms_max_overlap': {'type': 'float', 'default': 1.0, 'range': [0.1, 1.0]}
                }
            },
            {
                'name': 'sort',
                'display_name': 'SORT',
                'description': '简单在线实时追踪算法',
                'parameters': {
                    'max_age': {'type': 'int', 'default': 1, 'range': [1, 10]},
                    'min_hits': {'type': 'int', 'default': 3, 'range': [1, 10]},
                    'iou_threshold': {'type': 'float', 'default': 0.3, 'range': [0.1, 0.9]}
                }
            }
        ]
    
    def get_available_algorithms(self) -> List[Dict[str, Any]]:
        """获取支持的追踪算法列表"""

    def get_all_active_targets(self, target_type: Optional[str] = None, min_confidence: Optional[float] = None) -> List[Dict[str, Any]]:
        """获取所有活动监控点的追踪目标，支持筛选"""
        all_targets = []
        for monitor_id, instance in self.tracking_instances.items():
            if self.tracking_status.get(monitor_id) == 'running' and hasattr(instance.get('tracker'), 'get_current_targets'):
                try:
                    targets = instance['tracker'].get_current_targets() # {id, type, confidence, bbox}
                    for target in targets:
                        # 筛选
                        if target_type and target.get('type') != target_type:
                            continue
                        if min_confidence and target.get('confidence', 0) < min_confidence:
                            continue
                        
                        target['monitor_id'] = monitor_id
                        all_targets.append(target)
                except Exception as e:
                    print(f"Error getting targets from monitor {monitor_id}: {e}")
        return all_targets

    def get_target_details(self, target_id: int) -> Optional[Dict[str, Any]]:
        """获取特定追踪目标的详细信息"""
        # 首先，在实时追踪目标中查找
        for monitor_id, instance in self.tracking_instances.items():
            if self.tracking_status.get(monitor_id) == 'running' and hasattr(instance.get('tracker'), 'get_target_info'):
                target_info = instance['tracker'].get_target_info(target_id)
                if target_info:
                    # 补充历史轨迹信息
                    with get_db_connection() as db:
                        history_track = db.get_list(
                            "SELECT x, y, timestamp FROM tracking_history WHERE target_id = %s ORDER BY timestamp DESC LIMIT 100",
                            (target_id,)
                        )
                        target_info['history_track'] = history_track
                    return target_info

        # 如果实时目标中没有，则从数据库中查找最后一次出现的信息
        with get_db_connection() as db:
            last_seen = db.get_one("SELECT * FROM tracked_targets WHERE id = %s", (target_id,))
            if last_seen:
                history_track = db.get_list(
                    "SELECT x, y, timestamp FROM tracking_history WHERE target_id = %s ORDER BY timestamp DESC LIMIT 100",
                    (target_id,)
                )
                last_seen['history_track'] = history_track
                return last_seen
        
        return None

        return self.available_algorithms
    
    def switch_algorithm(self, monitor_id: int, algorithm_name: str, config_params: Dict[str, Any]) -> Dict[str, Any]:
        """切换追踪算法"""
        try:
            # 验证算法是否支持
            algorithm_info = None
            for alg in self.available_algorithms:
                if alg['name'] == algorithm_name:
                    algorithm_info = alg
                    break
            
            if not algorithm_info:
                return {
                    'success': False,
                    'message': f'不支持的追踪算法: {algorithm_name}'
                }
            
            # 停止当前追踪
            if monitor_id in self.tracking_status and self.tracking_status[monitor_id] == 'running':
                self.stop_tracking(monitor_id)
            
            # 创建新的追踪器实例
            tracker = self._create_tracker(algorithm_name, config_params)
            self.tracking_instances[monitor_id] = {
                'tracker': tracker,
                'algorithm': algorithm_name,
                'config': config_params,
                'switch_time': datetime.now()
            }
            
            return {
                'success': True,
                'data': {
                    'monitor_id': monitor_id,
                    'algorithm': algorithm_name,
                    'config': config_params,
                    'switch_time': datetime.now().isoformat()
                },
                'message': f'成功切换到 {algorithm_info["display_name"]} 算法'
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'切换追踪算法失败: {str(e)}'
            }
    
    def start_tracking(self, monitor_id: int, algorithm_name: str = 'bytetrack') -> Dict[str, Any]:
        """启动追踪"""
        try:
            if monitor_id in self.tracking_status and self.tracking_status[monitor_id] == 'running':
                return {
                    'success': False,
                    'message': '追踪已在运行中'
                }
            
            # 获取监控点信息
            with get_db_connection() as db:
                monitor = db.get_one(
                    "SELECT * FROM monitor WHERE id = %s",
                    (monitor_id,)
                )
                
                if not monitor:
                    return {
                        'success': False,
                        'message': '监控点不存在'
                    }
            
            # 如果没有追踪器实例，创建一个
            if monitor_id not in self.tracking_instances:
                tracker = self._create_tracker(algorithm_name, {})
                self.tracking_instances[monitor_id] = {
                    'tracker': tracker,
                    'algorithm': algorithm_name,
                    'config': {},
                    'switch_time': datetime.now()
                }
            
            self.tracking_status[monitor_id] = 'running'
            
            # 启动追踪线程
            thread = threading.Thread(
                target=self._tracking_worker,
                args=(monitor_id, monitor['rtsp_url'])
            )
            thread.daemon = True
            thread.start()
            
            self.tracking_threads[monitor_id] = thread
            
            return {
                'success': True,
                'data': {
                    'monitor_id': monitor_id,
                    'algorithm': self.tracking_instances[monitor_id]['algorithm'],
                    'status': 'running',
                    'start_time': datetime.now().isoformat()
                },
                'message': '追踪启动成功'
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'启动追踪失败: {str(e)}'
            }
    
    def stop_tracking(self, monitor_id: int) -> Dict[str, Any]:
        """停止追踪"""
        try:
            if monitor_id not in self.tracking_status or self.tracking_status[monitor_id] != 'running':
                return {
                    'success': False,
                    'message': '追踪未在运行'
                }
            
            # 停止追踪
            self.tracking_status[monitor_id] = 'stopped'
            
            # 清理线程
            if monitor_id in self.tracking_threads:
                del self.tracking_threads[monitor_id]
            
            return {
                'success': True,
                'data': {
                    'monitor_id': monitor_id,
                    'status': 'stopped',
                    'stop_time': datetime.now().isoformat()
                },
                'message': '追踪停止成功'
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'停止追踪失败: {str(e)}'
            }
    
    def get_tracking_status(self, monitor_id: int) -> Dict[str, Any]:
        """获取追踪状态"""
        if monitor_id in self.tracking_instances:
            instance = self.tracking_instances[monitor_id]
            return {
                'monitor_id': monitor_id,
                'status': self.tracking_status.get(monitor_id, 'stopped'),
                'algorithm': instance['algorithm'],
                'config': instance['config'],
                'switch_time': instance['switch_time'].isoformat(),
                'performance': self.performance_data.get(monitor_id, {})
            }
        else:
            return {
                'monitor_id': monitor_id,
                'status': 'stopped',
                'algorithm': None,
                'config': {},
                'performance': {}
            }
    
    def get_all_tracking_status(self) -> Dict[str, Any]:
        """获取所有监控点的追踪状态"""
        all_status = {}
        for monitor_id in self.tracking_instances:
            all_status[monitor_id] = self.get_tracking_status(monitor_id)
        return all_status
    
    def _create_tracker(self, algorithm_name: str, config_params: Dict[str, Any]):
        """创建追踪器实例"""
        # 这里应该根据算法名称创建相应的追踪器
        # 由于具体实现依赖于追踪算法库，这里返回一个模拟对象
        return {
            'algorithm': algorithm_name,
            'config': config_params,
            'created_at': datetime.now()
        }
    
    def _tracking_worker(self, monitor_id: int, rtsp_url: str):
        """追踪工作线程"""
        try:
            cap = cv2.VideoCapture(rtsp_url)
            start_time = time.time()
            frame_count = 0
            
            while self.tracking_status.get(monitor_id) == 'running':
                ret, frame = cap.read()
                if not ret:
                    time.sleep(0.1)
                    continue
                
                # 执行追踪
                # 这里应该调用实际的追踪算法
                frame_count += 1
                
                # 更新性能指标
                if frame_count % 30 == 0:  # 每30帧更新一次
                    elapsed_time = time.time() - start_time
                    fps = frame_count / elapsed_time
                    
                    self.performance_data[monitor_id] = {
                        'fps': fps,
                        'frame_count': frame_count,
                        'elapsed_time': elapsed_time,
                        'last_update': datetime.now().isoformat()
                    }
                    
                    # 保存性能数据到数据库
                    self._save_performance_data(monitor_id)
                
                time.sleep(0.033)  # 约30fps
                
        except Exception as e:
            print(f"追踪工作线程错误: {str(e)}")
        finally:
            if 'cap' in locals():
                cap.release()
            self.tracking_status[monitor_id] = 'stopped'
    
    def _save_performance_data(self, monitor_id: int):
        """保存性能数据到数据库"""
        try:
            if monitor_id not in self.performance_data:
                return
            
            perf_data = self.performance_data[monitor_id]
            instance = self.tracking_instances[monitor_id]
            
            with get_db_connection() as db:
                db.execute(
                    """INSERT INTO tracking_performance 
                       (monitor_id, algorithm_name, fps, accuracy, cpu_usage, memory_usage, 
                        avg_track_length, stat_date, stat_hour) 
                       VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)""",
                    (
                        monitor_id,
                        instance['algorithm'],
                        perf_data['fps'],
                        0.95,  # 模拟准确率
                        50.0,  # 模拟CPU使用率
                        200.0, # 模拟内存使用量
                        10.5,  # 模拟平均追踪长度
                        datetime.now().date(),
                        datetime.now().hour
                    )
                )
        except Exception as e:
            print(f"保存性能数据失败: {str(e)}")