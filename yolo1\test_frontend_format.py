#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试前端期望的API格式
"""

import requests
import json

def test_frontend_apis():
    """测试前端期望的API格式"""
    base_url = "http://127.0.0.1:5500"
    
    print("🔐 获取token...")
    
    # 1. 登录获取token
    login_data = {"username": "admin", "password": "123456"}
    response = requests.post(f"{base_url}/api/v1/auth/login", json=login_data)
    
    if response.status_code != 200:
        print(f"❌ 登录失败: {response.status_code}")
        return
    
    login_result = response.json()
    if not login_result.get('success'):
        print(f"❌ 登录失败: {login_result.get('message')}")
        return
    
    token = login_result['data']['token']
    headers = {"Authorization": f"Bearer {token}"}
    print("✅ 登录成功")
    
    # 2. 测试前端关键API
    frontend_apis = [
        {
            "name": "实时警报",
            "endpoint": "/api/v1/accident/alerts/realtime?limit=5",
            "expected_format": "数组格式，包含 alert_type, monitor_name, confidence 等字段"
        },
        {
            "name": "事故记录", 
            "endpoint": "/api/v1/accident/records?page=1&page_size=5",
            "expected_format": "对象格式，包含 records 数组"
        },
        {
            "name": "概览统计",
            "endpoint": "/api/v1/analysis/statistics/overview",
            "expected_format": "对象格式，包含 total_monitors, online_monitors 等"
        },
        {
            "name": "警报统计",
            "endpoint": "/api/v1/analysis/alarms",
            "expected_format": "对象格式，包含 total_alarms, alarm_by_type 等"
        },
        {
            "name": "交通流量",
            "endpoint": "/api/v1/analysis/traffic-flow",
            "expected_format": "数组格式，每项包含 monitor_id, hourly_flow 等"
        }
    ]
    
    for api in frontend_apis:
        print(f"\n🧪 测试 {api['name']} API...")
        print(f"   端点: {api['endpoint']}")
        print(f"   期望格式: {api['expected_format']}")
        
        try:
            response = requests.get(f"{base_url}{api['endpoint']}", headers=headers, timeout=5)
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('success'):
                    data = result.get('data')
                    print(f"   ✅ API成功")
                    
                    # 分析数据格式
                    if isinstance(data, list):
                        print(f"   📊 数据格式: 数组，长度 {len(data)}")
                        if len(data) > 0:
                            first_item = data[0]
                            print(f"   📋 数组项字段: {list(first_item.keys())}")
                            
                            # 检查关键字段
                            if api['name'] == '实时警报':
                                required_fields = ['alert_type', 'monitor_name', 'confidence', 'severity']
                                missing = [f for f in required_fields if f not in first_item]
                                if missing:
                                    print(f"   ❌ 缺少字段: {missing}")
                                else:
                                    print(f"   ✅ 包含所有关键字段")
                    
                    elif isinstance(data, dict):
                        print(f"   📊 数据格式: 对象")
                        print(f"   📋 对象字段: {list(data.keys())}")
                        
                        # 检查关键字段
                        if api['name'] == '概览统计':
                            required_fields = ['total_monitors', 'online_monitors', 'active_alarms']
                            missing = [f for f in required_fields if f not in data]
                            if missing:
                                print(f"   ❌ 缺少字段: {missing}")
                            else:
                                print(f"   ✅ 包含所有关键字段")
                        
                        elif api['name'] == '警报统计':
                            required_fields = ['total_alarms', 'alarm_by_type', 'alarm_by_severity']
                            missing = [f for f in required_fields if f not in data]
                            if missing:
                                print(f"   ❌ 缺少字段: {missing}")
                            else:
                                print(f"   ✅ 包含所有关键字段")
                    
                    else:
                        print(f"   📊 数据格式: {type(data)}")
                    
                    # 显示简化的数据示例
                    if isinstance(data, list) and len(data) > 0:
                        print(f"   📄 数据示例: {json.dumps(data[0], ensure_ascii=False, indent=2)}")
                    elif isinstance(data, dict):
                        # 只显示前几个字段
                        sample = {k: v for i, (k, v) in enumerate(data.items()) if i < 3}
                        print(f"   📄 数据示例: {json.dumps(sample, ensure_ascii=False, indent=2)}")
                
                else:
                    print(f"   ❌ API失败: {result.get('message')}")
            
            else:
                print(f"   ❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")

def main():
    """主函数"""
    print("=" * 80)
    print("🚀 前端API格式测试")
    print("=" * 80)
    
    test_frontend_apis()
    
    print("\n" + "=" * 80)
    print("💡 前端显示问题可能原因:")
    print("   1. API返回格式与前端期望不匹配")
    print("   2. 前端缓存了旧的API响应")
    print("   3. 前端JavaScript解析数据时出错")
    print("   4. 前端组件状态管理问题")
    print("\n🔧 解决方案:")
    print("   1. 清除浏览器缓存和localStorage")
    print("   2. 检查浏览器控制台错误")
    print("   3. 确认API格式符合前端期望")
    print("   4. 重新启动前端开发服务器")
    print("=" * 80)

if __name__ == "__main__":
    main()
