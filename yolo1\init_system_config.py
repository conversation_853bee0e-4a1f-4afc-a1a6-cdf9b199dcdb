#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
初始化系统配置表
"""

import pymysql
from datetime import datetime
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(override=True, dotenv_path='config/end-back.env')

def get_db_config():
    """获取数据库配置"""
    return {
        'host': '127.0.0.1',
        'port': 3306,
        'user': 'root',
        'password': '123456',
        'database': 'yolo',
        'charset': 'utf8mb4',
        'autocommit': True,
        'cursorclass': pymysql.cursors.DictCursor
    }

def create_system_config_table():
    """创建系统配置表"""
    print("🔧 创建系统配置表...")
    
    config = get_db_config()
    connection = pymysql.connect(**config)
    
    try:
        with connection.cursor() as cursor:
            # 检查表是否存在
            cursor.execute("SHOW TABLES LIKE 'system_config'")
            if cursor.fetchone():
                print("   ✅ system_config表已存在")
            else:
                # 创建表
                cursor.execute("""
                    CREATE TABLE system_config (
                        id INT PRIMARY KEY AUTO_INCREMENT,
                        config_key VARCHAR(100) UNIQUE NOT NULL,
                        config_value TEXT,
                        description VARCHAR(255),
                        create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                        update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                    )
                """)
                print("   ✅ system_config表创建成功")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"   ❌ 创建system_config表失败: {e}")
        return False

def init_system_config_data():
    """初始化系统配置数据"""
    print("🔧 初始化系统配置数据...")
    
    config = get_db_config()
    connection = pymysql.connect(**config)
    
    try:
        with connection.cursor() as cursor:
            # 默认系统配置
            default_configs = [
                ('system_name', '高速公路智能监控系统', '系统名称'),
                ('max_login_attempts', '5', '最大登录尝试次数'),
                ('session_timeout', '3600', '会话超时时间(秒)'),
                ('password_policy', '{"min_length": 6, "require_special": false, "require_number": true}', '密码策略'),
                ('email_settings', '{"smtp_server": "smtp.qq.com", "smtp_port": 587, "use_tls": true}', '邮件设置'),
                ('backup_settings', '{"auto_backup": true, "backup_interval": 24, "backup_path": "/backup"}', '备份设置'),
                ('alert_settings', '{"email_alerts": true, "sms_alerts": false, "push_alerts": true}', '警报设置'),
                ('detection_settings', '{"conf_threshold": 0.4, "iou_threshold": 0.5, "max_detections": 100}', '检测设置'),
                ('tracking_settings', '{"tracker_type": "bytetrack", "track_thresh": 0.5, "track_buffer": 30}', '追踪设置'),
                ('video_settings', '{"max_streams": 6, "resolution": "1920x1080", "fps": 25}', '视频设置')
            ]
            
            # 插入配置数据
            for config_key, config_value, description in default_configs:
                cursor.execute("""
                    INSERT INTO system_config (config_key, config_value, description, create_time)
                    VALUES (%s, %s, %s, %s)
                    ON DUPLICATE KEY UPDATE 
                    config_value = VALUES(config_value),
                    description = VALUES(description),
                    update_time = CURRENT_TIMESTAMP
                """, (config_key, config_value, description, datetime.now()))
            
            print(f"   ✅ 成功初始化{len(default_configs)}项系统配置")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"   ❌ 初始化系统配置数据失败: {e}")
        return False

def check_system_config():
    """检查系统配置"""
    print("🔍 检查系统配置...")
    
    config = get_db_config()
    connection = pymysql.connect(**config)
    
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT * FROM system_config ORDER BY config_key")
            configs = cursor.fetchall()
            
            print(f"   系统配置项总数: {len(configs)}")
            for config_item in configs:
                print(f"     {config_item['config_key']}: {config_item['config_value'][:50]}...")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"   ❌ 检查系统配置失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 初始化系统配置")
    print("=" * 60)
    
    success_count = 0
    total_count = 3
    
    # 1. 创建系统配置表
    if create_system_config_table():
        success_count += 1
    
    # 2. 初始化系统配置数据
    if init_system_config_data():
        success_count += 1
    
    # 3. 检查系统配置
    if check_system_config():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"📋 初始化完成: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("🎉 系统配置初始化成功!")
        print("💡 现在可以重新启动后端服务")
    else:
        print("⚠️ 部分初始化失败，请检查错误信息")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
