<template>
  <div class="accident-page-safe">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>事故检测（安全模式）</h2>
      <a-space>
        <a-button @click="loadData" :loading="loading">
          <template #icon><icon-refresh /></template>
          手动加载数据
        </a-button>
        <a-button @click="switchToNormalMode" type="primary">
          切换到正常模式
        </a-button>
      </a-space>
    </div>

    <!-- 状态提示 -->
    <a-alert type="info" show-icon style="margin-bottom: 16px;">
      <template #title>安全模式</template>
      <div>
        <p>当前处于安全模式，不会自动加载可能导致卡死的API</p>
        <p>请先使用"事故API诊断"工具检查API状态，确认正常后再切换到正常模式</p>
        <p>诊断工具：<a href="#/accident-api-test" target="_blank">事故API诊断</a></p>
      </div>
    </a-alert>

    <!-- 数据加载状态 -->
    <a-row :gutter="24" style="margin-bottom: 24px;">
      <a-col :span="8">
        <a-card title="监控点列表" size="small">
          <div class="status-item">
            <a-tag :color="monitors.length > 0 ? 'green' : 'gray'">
              {{ monitors.length > 0 ? '已加载' : '未加载' }}
            </a-tag>
            <span>{{ monitors.length }} 个监控点</span>
          </div>
          <a-button size="small" @click="loadMonitors" :loading="monitorsLoading">
            加载监控点
          </a-button>
        </a-card>
      </a-col>
      
      <a-col :span="8">
        <a-card title="实时警报" size="small">
          <div class="status-item">
            <a-tag :color="realTimeAlerts.length > 0 ? 'green' : 'gray'">
              {{ realTimeAlerts.length > 0 ? '已加载' : '未加载' }}
            </a-tag>
            <span>{{ realTimeAlerts.length }} 条警报</span>
          </div>
          <a-button size="small" @click="loadAlerts" :loading="alertsLoading">
            加载警报
          </a-button>
        </a-card>
      </a-col>
      
      <a-col :span="8">
        <a-card title="事故记录" size="small">
          <div class="status-item">
            <a-tag :color="accidentRecords.length > 0 ? 'green' : 'gray'">
              {{ accidentRecords.length > 0 ? '已加载' : '未加载' }}
            </a-tag>
            <span>{{ accidentRecords.length }} 条记录</span>
          </div>
          <a-button size="small" @click="loadRecords" :loading="recordsLoading">
            加载记录
          </a-button>
        </a-card>
      </a-col>
    </a-row>

    <!-- 监控点选择 -->
    <a-card title="事故检测控制" style="margin-bottom: 24px;">
      <a-form layout="inline">
        <a-form-item label="选择监控点">
          <a-select
            v-model="selectedMonitorId"
            placeholder="请选择监控点"
            style="width: 200px;"
            :loading="monitorsLoading"
          >
            <a-option
              v-for="monitor in monitors"
              :key="monitor.id"
              :value="monitor.id"
            >
              {{ monitor.name }}
            </a-option>
          </a-select>
        </a-form-item>
        
        <a-form-item>
          <a-button
            type="primary"
            @click="startDetection"
            :disabled="!selectedMonitorId"
            :loading="detectionLoading"
          >
            开始检测
          </a-button>
        </a-form-item>
        
        <a-form-item>
          <a-button
            @click="stopDetection"
            :disabled="!isDetecting"
          >
            停止检测
          </a-button>
        </a-form-item>
      </a-form>
      
      <div v-if="isDetecting" class="detection-status">
        <a-tag color="green">检测中</a-tag>
        <span>任务ID: {{ currentTaskId }}</span>
      </div>
    </a-card>

    <!-- 实时警报 -->
    <a-card title="实时警报" style="margin-bottom: 24px;">
      <a-spin :loading="alertsLoading">
        <div v-if="realTimeAlerts.length === 0" class="no-data">
          <a-empty description="暂无实时警报" />
        </div>
        <div v-else class="alerts-list">
          <div
            v-for="alert in realTimeAlerts"
            :key="alert.alert_id"
            class="alert-item"
          >
            <div class="alert-header">
              <a-tag :color="getSeverityColor(alert.severity)">
                {{ alert.severity }}
              </a-tag>
              <span class="alert-time">{{ alert.create_time }}</span>
            </div>
            <div class="alert-content">
              <p><strong>类型:</strong> {{ alert.alert_type }}</p>
              <p><strong>位置:</strong> 监控点 {{ alert.monitor_id }}</p>
              <p><strong>描述:</strong> {{ alert.description }}</p>
            </div>
          </div>
        </div>
      </a-spin>
    </a-card>

    <!-- 事故记录 -->
    <a-card title="事故记录">
      <a-table
        :columns="recordColumns"
        :data="accidentRecords"
        :loading="recordsLoading"
        :pagination="false"
        size="small"
      >
        <template #accident_type="{ record }">
          <a-tag>{{ record.accident_type }}</a-tag>
        </template>
        
        <template #severity="{ record }">
          <a-tag :color="getSeverityColor(record.severity)">
            {{ record.severity }}
          </a-tag>
        </template>
        
        <template #status="{ record }">
          <a-tag :color="getStatusColor(record.status)">
            {{ record.status }}
          </a-tag>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { Message } from '@arco-design/web-vue'
import { IconRefresh } from '@arco-design/web-vue/es/icon'
import { useRouter } from 'vue-router'
import { monitorApi } from '@/api/monitor'
import { accidentApi } from '@/api/accident'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const monitorsLoading = ref(false)
const alertsLoading = ref(false)
const recordsLoading = ref(false)
const detectionLoading = ref(false)

const selectedMonitorId = ref(null)
const isDetecting = ref(false)
const currentTaskId = ref(null)

const monitors = ref([])
const realTimeAlerts = ref([])
const accidentRecords = ref([])

// 表格列配置
const recordColumns = [
  { title: 'ID', dataIndex: 'record_id', width: 120 },
  { title: '监控点', dataIndex: 'monitor_id', width: 100 },
  { title: '事故类型', dataIndex: 'accident_type', slotName: 'accident_type', width: 120 },
  { title: '严重程度', dataIndex: 'severity', slotName: 'severity', width: 100 },
  { title: '状态', dataIndex: 'status', slotName: 'status', width: 100 },
  { title: '描述', dataIndex: 'description', ellipsis: true },
  { title: '创建时间', dataIndex: 'create_time', width: 180 }
]

// 获取严重程度颜色
const getSeverityColor = (severity: string) => {
  switch (severity) {
    case 'high': return 'red'
    case 'medium': return 'orange'
    case 'low': return 'blue'
    default: return 'gray'
  }
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  switch (status) {
    case 'confirmed': return 'red'
    case 'resolved': return 'green'
    case 'pending': return 'orange'
    case 'false_alarm': return 'gray'
    default: return 'blue'
  }
}

// 加载监控点（带超时保护）
const loadMonitors = async () => {
  monitorsLoading.value = true
  try {
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('获取监控点超时')), 5000)
    })

    const apiPromise = monitorApi.getMonitorList({ page: 1, size: 100 })
    const response = await Promise.race([apiPromise, timeoutPromise])
    
    if (response.success) {
      monitors.value = response.data.monitors?.filter(m => m.connection_status === 'online') || []
      Message.success(`加载了 ${monitors.value.length} 个在线监控点`)
    } else {
      Message.error('获取监控点失败: ' + response.message)
    }
  } catch (error) {
    console.error('获取监控点异常:', error)
    Message.error('获取监控点失败: ' + error.message)
  } finally {
    monitorsLoading.value = false
  }
}

// 加载实时警报（带超时保护）
const loadAlerts = async () => {
  alertsLoading.value = true
  try {
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('获取实时警报超时')), 5000)
    })

    const apiPromise = accidentApi.getRealTimeAlerts({ limit: 10 })
    const response = await Promise.race([apiPromise, timeoutPromise])
    
    if (response.success) {
      realTimeAlerts.value = response.data.alerts || []
      Message.success(`加载了 ${realTimeAlerts.value.length} 条实时警报`)
    } else {
      Message.error('获取实时警报失败: ' + response.message)
    }
  } catch (error) {
    console.error('获取实时警报异常:', error)
    Message.error('获取实时警报失败: ' + error.message)
  } finally {
    alertsLoading.value = false
  }
}

// 加载事故记录（带超时保护）
const loadRecords = async () => {
  recordsLoading.value = true
  try {
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('获取事故记录超时')), 5000)
    })

    const apiPromise = accidentApi.getAccidentRecords({ page: 1, page_size: 10 })
    const response = await Promise.race([apiPromise, timeoutPromise])
    
    if (response.success) {
      accidentRecords.value = response.data.records || []
      Message.success(`加载了 ${accidentRecords.value.length} 条事故记录`)
    } else {
      Message.error('获取事故记录失败: ' + response.message)
    }
  } catch (error) {
    console.error('获取事故记录异常:', error)
    Message.error('获取事故记录失败: ' + error.message)
  } finally {
    recordsLoading.value = false
  }
}

// 加载所有数据
const loadData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadMonitors(),
      loadAlerts(),
      loadRecords()
    ])
    Message.success('所有数据加载完成')
  } catch (error) {
    Message.error('数据加载失败')
  } finally {
    loading.value = false
  }
}

// 开始检测
const startDetection = async () => {
  if (!selectedMonitorId.value) {
    Message.warning('请选择监控点')
    return
  }

  detectionLoading.value = true
  try {
    const response = await accidentApi.startAccidentDetection({
      monitor_id: selectedMonitorId.value,
      config: {
        monitor_id: selectedMonitorId.value,
        enable_collision_detection: true,
        enable_congestion_detection: true,
        enable_abnormal_behavior_detection: true,
        collision_threshold: 0.8,
        congestion_threshold: 0.7,
        min_detection_area: 100
      }
    })

    if (response.success) {
      isDetecting.value = true
      currentTaskId.value = response.data.task_id
      Message.success('事故检测已开始')
    } else {
      Message.error('启动事故检测失败: ' + response.message)
    }
  } catch (error) {
    Message.error('启动事故检测失败: ' + error.message)
  } finally {
    detectionLoading.value = false
  }
}

// 停止检测
const stopDetection = async () => {
  try {
    if (currentTaskId.value) {
      await accidentApi.stopAccidentDetection({
        task_id: currentTaskId.value
      })
    }
    
    isDetecting.value = false
    currentTaskId.value = null
    Message.success('事故检测已停止')
  } catch (error) {
    Message.error('停止事故检测失败: ' + error.message)
  }
}

// 切换到正常模式
const switchToNormalMode = () => {
  router.push('/accident')
}
</script>

<style scoped>
.accident-page-safe {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.detection-status {
  margin-top: 16px;
  padding: 12px;
  background: #f0f8ff;
  border-radius: 6px;
  border: 1px solid #91d5ff;
}

.no-data {
  text-align: center;
  padding: 40px;
}

.alerts-list {
  max-height: 400px;
  overflow-y: auto;
}

.alert-item {
  padding: 12px;
  border: 1px solid #e5e6eb;
  border-radius: 6px;
  margin-bottom: 8px;
}

.alert-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.alert-time {
  font-size: 12px;
  color: #666;
}

.alert-content p {
  margin: 4px 0;
  font-size: 12px;
}
</style>
