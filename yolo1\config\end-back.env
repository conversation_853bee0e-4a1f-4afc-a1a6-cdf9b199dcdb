# 基于Yolov8与ByteTrack的高速公路智慧监控平台 - 后端配置文件

#---------------------------------------------------服务器配置
HOST_NAME=127.0.0.1        # 主机名
PORT=5500                  # HTTP服务端口
DEBUG=false                # 调试模式
TOLERANT_TIME_ERROR=60     # 调用接口时附带的时间戳参数与服务器时间之间的最大允许误差（单位：s）

# 图片存储路径配置
BEFORE_IMG_PATH=before_img        # 原始图片存储路径
AFTER_IMG_PATH=after_img          # 处理后图片存储路径

#---------------------------------------------------数据库配置
MYSQL_HOST=127.0.0.1       # SQL主机
MYSQL_PORT=3306            # 连接端口
MYSQL_USER=root            # 用户名（统一命名）
MYSQL_PASSWORD=123456      # 密码（统一命名）
MYSQL_DATABASE=yolo        # 数据库名（统一命名）
MYSQL_CHARSET=utf8mb4      # 字符集

# 兼容旧版本的变量名
MYSQL_user=root            # 兼容旧版本
MYSQL_password=123456      # 兼容旧版本
MYSQL_db=yolo             # 兼容旧版本
MYSQL_charset=utf8mb4      # 兼容旧版本

#---------------------------------------------------Redis配置
REDIS_HOST=127.0.0.1       # Redis主机
REDIS_PORT=6379            # Redis端口
REDIS_PASSWORD=            # Redis密码（可选）
REDIS_DB=0                 # Redis数据库编号

#---------------------------------------------------安全配置
SECRET_KEY=yolo-highway-monitoring-system-secret-key-2025
JWT_SECRET_KEY=jwt-secret-key-for-yolo-system-2025
JWT_EXPIRATION_HOURS=24    # JWT令牌过期时间（小时）

#---------------------------------------------------YOLO模型配置
DEFAULT_MODEL_PATH=./models/car.pt    # 默认模型路径
MODEL_CONF_THRESHOLD=0.4              # 默认置信度阈值
MODEL_IOU_THRESHOLD=0.5               # 默认IOU阈值

#---------------------------------------------------文件上传配置
MAX_CONTENT_LENGTH=104857600          # 最大上传文件大小（100MB）
UPLOAD_FOLDER=uploads                 # 上传文件夹
ALLOWED_EXTENSIONS=jpg,jpeg,png,bmp,mp4,avi,mov

#---------------------------------------------------日志配置
LOG_LEVEL=INFO                        # 日志级别
LOG_MAX_BYTES=10485760               # 日志文件最大大小（10MB）
LOG_BACKUP_COUNT=5                   # 日志备份文件数量

#---------------------------------------------------监控配置
RTSP_CONNECTION_TIMEOUT=10           # RTSP连接超时时间（秒）
RTSP_READ_TIMEOUT=5                  # RTSP读取超时时间（秒）
MONITOR_HEALTH_CHECK_INTERVAL=60     # 监控点健康检查间隔（秒）

#---------------------------------------------------系统配置
SYSTEM_NAME=基于Yolov8与ByteTrack的高速公路智慧监控平台
SYSTEM_VERSION=1.0.0
SYSTEM_DESCRIPTION=基于Yolov8与ByteTrack的高速公路智慧监控平台


#---------------------------------------------------Conda环境配置
CONDA_ENV_NAME=ByteTrack
PYTHON_VERSION=3.9.16
USE_CONDA=true

#---------------------------------------------------CUDA配置 (Conda)
USE_CUDA=true
CUDA_DEVICE=0
YOLO_DEVICE=cuda
YOLO_HALF_PRECISION=true
YOLO_BATCH_SIZE=1
TORCH_CUDNN_BENCHMARK=true
