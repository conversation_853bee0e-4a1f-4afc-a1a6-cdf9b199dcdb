# -*- coding: utf-8 -*-
# @Description : 完整API接口测试
# @Date : 2025年6月21日

import requests
import json
import time

class APITester:
    def __init__(self, base_url="http://127.0.0.1:5500"):
        self.base_url = base_url
        self.token = None
        
    def test_request(self, endpoint, method='GET', data=None, headers=None):
        """测试API请求"""
        url = f"{self.base_url}{endpoint}"
        
        try:
            if method == 'GET':
                response = requests.get(url, headers=headers, timeout=10)
            elif method == 'POST':
                response = requests.post(url, json=data, headers=headers, timeout=10)
            
            print(f"\n{'='*60}")
            print(f"🔍 测试: {method} {endpoint}")
            print(f"📊 状态码: {response.status_code}")
            
            try:
                response_data = response.json()
                print(f"📝 响应格式: {'✅ 正确' if 'code' in response_data else '❌ 错误'}")
                
                if response_data.get('code') == 200:
                    print("✅ 接口调用成功")
                    if 'data' in response_data:
                        data_type = type(response_data['data'])
                        if isinstance(response_data['data'], dict):
                            print(f"📦 数据类型: 对象 ({len(response_data['data'])} 个字段)")
                        elif isinstance(response_data['data'], list):
                            print(f"📦 数据类型: 数组 ({len(response_data['data'])} 个元素)")
                        else:
                            print(f"📦 数据类型: {data_type.__name__}")
                    return True, response_data
                else:
                    print(f"❌ 接口调用失败: {response_data.get('message', '未知错误')}")
                    return False, response_data
                    
            except json.JSONDecodeError:
                print(f"📄 非JSON响应: {response.text[:100]}...")
                return response.status_code == 200, response.text
                
        except Exception as e:
            print(f"\n{'='*60}")
            print(f"🔍 测试: {method} {endpoint}")
            print(f"❌ 请求异常: {e}")
            return False, None
    
    def test_login(self, username="admin", password="123456"):
        """测试登录"""
        print(f"\n🔐 测试登录: {username}")
        
        success, data = self.test_request(
            '/api/v1/auth/login', 
            'POST', 
            {'username': username, 'password': password}
        )
        
        if success and data and data.get('code') == 200:
            self.token = data['data']['token']
            print(f"🎫 获取Token: {self.token[:20]}...")
            return True
        
        return False
    
    def get_auth_headers(self):
        """获取认证头"""
        if self.token:
            return {'Authorization': f'Bearer {self.token}'}
        return {}
    
    def run_complete_test(self):
        """运行完整测试"""
        print("🎯 基于Yolov8与ByteTrack的高速公路智慧监控平台 - 完整API测试")
        print("="*80)
        
        # 1. 基础接口测试
        print("\n📋 1. 基础接口测试")
        basic_tests = [
            ('/', 'GET', None, '首页接口'),
            ('/health', 'GET', None, '健康检查'),
            ('/api/v1/docs', 'GET', None, 'API文档'),
            ('/api/v1/system/health-check', 'GET', None, '系统健康检查'),
        ]
        
        basic_success = 0
        for endpoint, method, data, description in basic_tests:
            print(f"\n📌 {description}")
            success, _ = self.test_request(endpoint, method, data)
            if success:
                basic_success += 1
        
        print(f"\n📊 基础接口测试结果: {basic_success}/{len(basic_tests)} 成功")
        
        # 2. 认证接口测试
        print("\n📋 2. 认证接口测试")
        
        # 测试错误登录
        print(f"\n📌 错误登录测试")
        self.test_request('/api/v1/auth/login', 'POST', {'username': 'wrong', 'password': 'wrong'})
        
        # 测试正确登录
        print(f"\n📌 正确登录测试")
        login_success = self.test_login()
        
        if not login_success:
            print("❌ 登录失败，无法继续测试需要认证的接口")
            return
        
        # 测试用户信息
        print(f"\n📌 用户信息测试")
        headers = self.get_auth_headers()
        self.test_request('/api/v1/auth/profile', 'GET', None, headers)
        
        # 测试登出
        print(f"\n📌 登出测试")
        self.test_request('/api/v1/auth/logout', 'POST', None, headers)
        
        # 3. 数据接口测试
        print("\n📋 3. 数据接口测试")
        
        # 重新登录获取新Token
        self.test_login()
        headers = self.get_auth_headers()
        
        data_tests = [
            ('/api/v1/monitor/list', 'GET', None, '监控点列表'),
            ('/api/v1/monitor/list?page=1&size=5', 'GET', None, '监控点分页'),
            ('/api/v1/analysis/alarms', 'GET', None, '警报列表'),
            ('/api/v1/analysis/alarms?page=1&size=5', 'GET', None, '警报分页'),
        ]
        
        data_success = 0
        for endpoint, method, data, description in data_tests:
            print(f"\n📌 {description}")
            success, response_data = self.test_request(endpoint, method, data, headers)
            if success and response_data:
                # 检查分页数据
                if 'pagination' in response_data.get('data', {}):
                    pagination = response_data['data']['pagination']
                    print(f"📄 分页信息: 第{pagination['page']}页, 共{pagination['total']}条")
                
                # 检查列表数据
                if 'list' in response_data.get('data', {}):
                    list_data = response_data['data']['list']
                    print(f"📋 列表数据: {len(list_data)}条记录")
                    
                data_success += 1
        
        print(f"\n📊 数据接口测试结果: {data_success}/{len(data_tests)} 成功")
        
        # 4. 系统接口测试
        print("\n📋 4. 系统接口测试")
        
        system_tests = [
            ('/api/v1/system/info', 'GET', None, '系统信息'),
        ]
        
        system_success = 0
        for endpoint, method, data, description in system_tests:
            print(f"\n📌 {description}")
            success, _ = self.test_request(endpoint, method, data, headers)
            if success:
                system_success += 1
        
        print(f"\n📊 系统接口测试结果: {system_success}/{len(system_tests)} 成功")
        
        # 5. 总结
        total_tests = len(basic_tests) + 4 + len(data_tests) + len(system_tests)  # 4个认证测试
        total_success = basic_success + (4 if login_success else 0) + data_success + system_success
        
        print(f"\n🎉 测试完成总结")
        print("="*80)
        print(f"📊 总体结果: {total_success}/{total_tests} 个接口正常")
        print(f"✅ 基础接口: {basic_success}/{len(basic_tests)} 正常")
        print(f"🔐 认证功能: {'正常' if login_success else '异常'}")
        print(f"📊 数据接口: {data_success}/{len(data_tests)} 正常")
        print(f"⚙️ 系统接口: {system_success}/{len(system_tests)} 正常")
        
        if total_success >= total_tests * 0.8:  # 80%以上成功
            print("\n🎊 系统API基本正常，可以对接前端！")
            print("\n📋 前端对接信息:")
            print(f"   🌐 API基础地址: {self.base_url}/api/v1")
            print(f"   🔑 登录接口: POST /auth/login")
            print(f"   📹 监控点接口: GET /monitor/list")
            print(f"   🚨 警报接口: GET /analysis/alarms")
            print(f"   💡 认证方式: Bearer Token")
            print(f"   📚 完整文档: docs/完整API接口文档.md")
        else:
            print("\n⚠️ 系统存在问题，需要修复后再对接前端")
            
        return total_success >= total_tests * 0.8

def main():
    """主函数"""
    tester = APITester()
    
    print("等待服务器启动...")
    time.sleep(2)
    
    try:
        success = tester.run_complete_test()
        
        if success:
            print("\n🚀 测试账号:")
            print("   admin / 123456 (超级管理员)")
            print("   operator / operator (操作员)")
            print("   viewer / hello (观察员)")
            
            print("\n📖 使用说明:")
            print("   1. 启动后端: python frontend_api_server.py")
            print("   2. 查看文档: docs/完整API接口文档.md")
            print("   3. 复制API客户端代码到前端项目")
            print("   4. 使用测试账号验证功能")
            
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")

if __name__ == "__main__":
    main()
