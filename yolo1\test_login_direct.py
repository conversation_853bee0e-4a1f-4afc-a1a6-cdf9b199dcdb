#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试登录逻辑 - 模拟登录接口的完全相同环境
"""

import os
import sys
import jwt
from datetime import datetime, timedelta
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(override=True, dotenv_path='config/end-back.env')

# 添加backend路径
sys.path.append('backend')

def test_login_logic():
    """直接测试登录逻辑"""
    print("🔍 直接测试登录逻辑")
    print("=" * 50)
    
    try:
        # 导入必要的模块
        from utils.response import success_response, error_response
        from utils.database import get_db_connection
        
        print("✅ 模块导入成功")
        
        # 模拟登录参数
        username = "admin"
        password = "123456"
        
        print(f"📝 测试登录: {username} / {password}")
        
        # 检查参数
        if not username or not password:
            print("❌ 用户名和密码不能为空")
            return False
        
        print("✅ 参数检查通过")
        
        # 数据库查询
        print("\n🔍 执行数据库查询...")
        try:
            with get_db_connection() as db:
                print(f"   数据库连接对象: {type(db)}")
                print(f"   数据库连接对象是否为None: {db is None}")
                
                if db is None:
                    print("   ❌ 数据库连接对象为None")
                    return False
                
                print(f"   数据库连接对象方法: {[m for m in dir(db) if not m.startswith('_')]}")
                
                # 执行查询
                print("   执行查询: SELECT * FROM user WHERE username=%s AND password=%s")
                user = db.get_one(
                    "SELECT * FROM user WHERE username=%s AND password=%s", 
                    (username, password)
                )
                print(f"   查询结果: {user}")
                
                if not user:
                    print("   ❌ 用户名或密码错误")
                    return False
                
                print("   ✅ 用户验证成功")
                
                # 生成JWT令牌
                print("\n🔑 生成JWT令牌...")
                secret_key = os.getenv('SECRET_KEY', 'yolo-highway-monitoring-system-2025')
                print(f"   SECRET_KEY: {secret_key}")
                
                token = jwt.encode({
                    'user_id': user['id'],
                    'username': user['username'],
                    'exp': datetime.utcnow() + timedelta(hours=24)
                }, secret_key, algorithm='HS256')
                
                print(f"   生成的Token: {token[:50]}...")
                
                # 构造响应
                response_data = {
                    'token': token,
                    'user': {
                        'id': user['id'],
                        'username': user['username'],
                        'email': user['email'],
                        'grade': user['grade'],
                        'avatar': user.get('avatar', 'default_avatar.jpg')
                    }
                }
                
                print(f"   响应数据: {response_data}")
                print("   ✅ 登录逻辑完全成功")
                
                return True
                
        except Exception as e:
            print(f"   ❌ 数据库查询失败: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    except Exception as e:
        print(f"❌ 模块导入或初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 直接登录逻辑测试")
    print("=" * 60)
    
    success = test_login_logic()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 登录逻辑测试成功！")
        print("✅ 登录接口应该可以正常工作")
        print("💡 如果API还是失败，可能是Flask应用层面的问题")
    else:
        print("⚠️ 登录逻辑测试失败")
        print("💡 需要修复数据库连接或查询问题")
    print("=" * 60)

if __name__ == "__main__":
    main()
