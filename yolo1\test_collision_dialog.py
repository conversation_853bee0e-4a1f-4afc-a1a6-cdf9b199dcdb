#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试碰撞检测对话框
"""

import sys
from PySide6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PySide6.QtCore import Qt

try:
    from ui.dialog.collision_detection_dialog import CollisionDetectionDialog
    print("✅ 碰撞检测对话框导入成功")
except Exception as e:
    print(f"❌ 碰撞检测对话框导入失败: {e}")
    sys.exit(1)

class TestMainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("测试碰撞检测对话框")
        self.setGeometry(100, 100, 400, 200)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建测试按钮
        self.test_button = QPushButton("打开碰撞检测对话框")
        self.test_button.clicked.connect(self.open_collision_dialog)
        layout.addWidget(self.test_button)
        
        self.collision_dialog = None
    
    def open_collision_dialog(self):
        """打开碰撞检测对话框"""
        try:
            print("正在创建碰撞检测对话框...")
            self.collision_dialog = CollisionDetectionDialog(self)
            print("✅ 碰撞检测对话框创建成功")
            
            # 连接信号
            self.collision_dialog.detection_started.connect(self.on_detection_started)
            self.collision_dialog.detection_stopped.connect(self.on_detection_stopped)
            self.collision_dialog.collision_detected.connect(self.on_collision_detected)
            print("✅ 信号连接成功")
            
            # 显示对话框
            self.collision_dialog.show()
            print("✅ 对话框显示成功")
            
        except Exception as e:
            print(f"❌ 创建或显示对话框失败: {e}")
            import traceback
            traceback.print_exc()
    
    def on_detection_started(self):
        print("🟢 检测已开始")
    
    def on_detection_stopped(self):
        print("🔴 检测已停止")
    
    def on_collision_detected(self, collision_info):
        print(f"⚠️ 检测到碰撞: {collision_info}")

def main():
    app = QApplication(sys.argv)
    
    # 创建主窗口
    window = TestMainWindow()
    window.show()
    
    print("🚀 测试应用启动成功")
    print("点击按钮测试碰撞检测对话框...")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()