# -*- coding: utf-8 -*-
# @Description : CUDA安装验证脚本
# @Date : 2025年6月20日

import sys
import time
import numpy as np

def test_basic_imports():
    """测试基础包导入"""
    print("=" * 60)
    print("测试基础包导入")
    print("=" * 60)
    
    packages = [
        ("numpy", "NumPy"),
        ("cv2", "OpenCV"),
        ("PIL", "Pillow"),
        ("torch", "PyTorch"),
        ("torchvision", "TorchVision"),
        ("ultralytics", "Ultralytics")
    ]
    
    success_count = 0
    for package, name in packages:
        try:
            if package == "cv2":
                import cv2
                print(f"✓ {name}: {cv2.__version__}")
            elif package == "PIL":
                import PIL
                print(f"✓ {name}: {PIL.__version__}")
            elif package == "torch":
                import torch
                print(f"✓ {name}: {torch.__version__}")
            elif package == "torchvision":
                import torchvision
                print(f"✓ {name}: {torchvision.__version__}")
            elif package == "ultralytics":
                import ultralytics
                print(f"✓ {name}: {ultralytics.__version__}")
            else:
                exec(f"import {package}")
                version = eval(f"{package}.__version__")
                print(f"✓ {name}: {version}")
            success_count += 1
        except ImportError as e:
            print(f"✗ {name}: 导入失败 - {e}")
        except Exception as e:
            print(f"⚠ {name}: 导入成功但获取版本失败 - {e}")
            success_count += 1
    
    print(f"\n导入成功: {success_count}/{len(packages)}")
    return success_count == len(packages)

def test_cuda_availability():
    """测试CUDA可用性"""
    print("\n" + "=" * 60)
    print("测试CUDA可用性")
    print("=" * 60)
    
    try:
        import torch
        
        print(f"PyTorch版本: {torch.__version__}")
        print(f"CUDA编译支持: {torch.cuda.is_available()}")
        
        if torch.cuda.is_available():
            print(f"CUDA版本: {torch.version.cuda}")
            print(f"cuDNN版本: {torch.backends.cudnn.version()}")
            print(f"GPU数量: {torch.cuda.device_count()}")
            
            for i in range(torch.cuda.device_count()):
                props = torch.cuda.get_device_properties(i)
                print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
                print(f"  显存: {props.total_memory / 1024**3:.1f} GB")
                print(f"  计算能力: {props.major}.{props.minor}")
                print(f"  多处理器数量: {props.multi_processor_count}")
            
            return True
        else:
            print("CUDA不可用，可能的原因:")
            print("1. 没有安装CUDA Toolkit")
            print("2. 没有安装NVIDIA驱动")
            print("3. PyTorch是CPU版本")
            return False
            
    except ImportError:
        print("PyTorch未安装")
        return False
    except Exception as e:
        print(f"CUDA检查失败: {e}")
        return False

def test_cuda_performance():
    """测试CUDA性能"""
    print("\n" + "=" * 60)
    print("测试CUDA性能")
    print("=" * 60)
    
    try:
        import torch
        
        if not torch.cuda.is_available():
            print("CUDA不可用，跳过性能测试")
            return False
        
        device = torch.device("cuda:0")
        size = 1000
        iterations = 10
        
        print(f"矩阵大小: {size}x{size}")
        print(f"测试迭代: {iterations}次")
        
        # CPU性能测试
        print("\nCPU性能测试...")
        cpu_times = []
        for i in range(iterations):
            start = time.time()
            a = torch.randn(size, size)
            b = torch.randn(size, size)
            c = torch.mm(a, b)
            cpu_times.append(time.time() - start)
        
        cpu_avg = np.mean(cpu_times)
        print(f"CPU平均时间: {cpu_avg:.4f}s")
        
        # GPU性能测试
        print("\nGPU性能测试...")
        gpu_times = []
        
        # 预热GPU
        for _ in range(3):
            a = torch.randn(size, size).to(device)
            b = torch.randn(size, size).to(device)
            c = torch.mm(a, b)
            torch.cuda.synchronize()
        
        for i in range(iterations):
            start = time.time()
            a = torch.randn(size, size).to(device)
            b = torch.randn(size, size).to(device)
            torch.cuda.synchronize()
            c = torch.mm(a, b)
            torch.cuda.synchronize()
            gpu_times.append(time.time() - start)
        
        gpu_avg = np.mean(gpu_times)
        speedup = cpu_avg / gpu_avg
        
        print(f"GPU平均时间: {gpu_avg:.4f}s")
        print(f"加速比: {speedup:.2f}x")
        
        if speedup > 2.0:
            print("✓ GPU加速效果良好")
            return True
        else:
            print("⚠ GPU加速效果不明显")
            return False
            
    except Exception as e:
        print(f"性能测试失败: {e}")
        return False

def test_yolo_cuda():
    """测试YOLO CUDA支持"""
    print("\n" + "=" * 60)
    print("测试YOLO CUDA支持")
    print("=" * 60)
    
    try:
        from ultralytics import YOLO
        import torch
        
        print("正在加载YOLO模型...")
        
        # 尝试加载模型
        try:
            model = YOLO("yolov8n.pt")  # 使用轻量级模型
            print("✓ YOLO模型加载成功")
        except Exception as e:
            print(f"✗ YOLO模型加载失败: {e}")
            print("模型将自动下载，请稍等...")
            return False
        
        # 测试CPU推理
        print("\n测试CPU推理...")
        model.to("cpu")
        test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
        
        start = time.time()
        results_cpu = model(test_image, verbose=False)
        cpu_time = time.time() - start
        print(f"CPU推理时间: {cpu_time:.4f}s")
        
        # 测试GPU推理
        if torch.cuda.is_available():
            print("\n测试GPU推理...")
            model.to("cuda")
            
            # 预热
            for _ in range(3):
                model(test_image, verbose=False)
            
            start = time.time()
            results_gpu = model(test_image, verbose=False)
            gpu_time = time.time() - start
            
            speedup = cpu_time / gpu_time
            print(f"GPU推理时间: {gpu_time:.4f}s")
            print(f"推理加速比: {speedup:.2f}x")
            
            if speedup > 1.5:
                print("✓ YOLO GPU加速效果良好")
                return True
            else:
                print("⚠ YOLO GPU加速效果不明显")
                return False
        else:
            print("CUDA不可用，无法测试GPU推理")
            return False
            
    except ImportError as e:
        print(f"Ultralytics导入失败: {e}")
        return False
    except Exception as e:
        print(f"YOLO测试失败: {e}")
        return False

def test_custom_yolo_detector():
    """测试自定义YOLO检测器"""
    print("\n" + "=" * 60)
    print("测试自定义YOLO检测器")
    print("=" * 60)
    
    try:
        from utils.yolo_cuda import CudaYOLODetector
        
        # 创建检测器
        detector = CudaYOLODetector(device="auto")
        
        # 获取设备信息
        device_info = detector.get_device_info()
        print("设备信息:")
        for key, value in device_info.items():
            print(f"  {key}: {value}")
        
        # 创建测试图像
        test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
        
        # 执行检测
        print("\n执行检测测试...")
        result = detector.detect_image(test_image)
        
        if "error" in result:
            print(f"✗ 检测失败: {result['error']}")
            return False
        else:
            print("✓ 检测成功")
            print(f"  检测到目标数量: {result['statistics']['total_objects']}")
            print(f"  推理时间: {result['performance']['inference_time']:.4f}s")
            print(f"  使用设备: {result['performance']['device']}")
            print(f"  FPS: {result['performance']['fps']:.1f}")
            
            # 性能基准测试
            print("\n执行性能基准测试...")
            benchmark = detector.benchmark(iterations=5)
            
            if "error" in benchmark:
                print(f"✗ 基准测试失败: {benchmark['error']}")
            else:
                print("✓ 基准测试完成")
                print(f"  平均推理时间: {benchmark['avg_inference_time']:.4f}s")
                print(f"  平均FPS: {benchmark['avg_fps']:.1f}")
                print(f"  使用设备: {benchmark['device']}")
            
            return True
            
    except ImportError as e:
        print(f"自定义检测器导入失败: {e}")
        print("这是正常的，如果其他测试通过")
        return True
    except Exception as e:
        print(f"自定义检测器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("基于Yolov8与ByteTrack的高速公路智慧监控平台 - CUDA安装验证")
    print("=" * 80)
    
    tests = [
        ("基础包导入", test_basic_imports),
        ("CUDA可用性", test_cuda_availability),
        ("CUDA性能", test_cuda_performance),
        ("YOLO CUDA", test_yolo_cuda),
        ("自定义检测器", test_custom_yolo_detector)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"\n测试 '{test_name}' 发生异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    
    passed = 0
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("\n🎉 恭喜！CUDA环境配置完美！")
        print("您可以运行以下命令启动系统:")
        print("python start_server.py")
    elif passed >= len(results) - 1:
        print("\n✅ CUDA环境基本配置成功！")
        print("可以启动系统，部分高级功能可能不可用")
        print("python start_server.py")
    else:
        print("\n❌ CUDA环境配置有问题")
        print("请检查:")
        print("1. NVIDIA驱动是否正确安装")
        print("2. CUDA Toolkit是否安装")
        print("3. PyTorch CUDA版本是否正确安装")
        print("4. 运行: python install_cuda_dependencies.py")

if __name__ == "__main__":
    main()
