#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试后端数据库修复
"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(override=True, dotenv_path='config/end-back.env')

# 添加backend路径
sys.path.append('backend')

def test_database_fix():
    """测试数据库修复"""
    print("🔍 测试后端数据库修复...")
    
    try:
        # 导入数据库模块
        from utils.database import DatabaseManager, get_db_connection, init_database
        
        print("✅ 数据库模块导入成功")
        
        # 测试数据库管理器
        db_manager = DatabaseManager()
        print(f"✅ 数据库管理器配置: {db_manager.config}")
        
        # 测试直接连接
        connection = db_manager.get_connection()
        print("✅ 数据库管理器连接成功")
        connection.close()
        
        # 测试上下文管理器
        print("\n🔍 测试上下文管理器...")
        with get_db_connection() as db:
            print("✅ 上下文管理器创建成功")
            db.execute("SELECT 1 as test")
            result = db.fetchone()
            print(f"✅ 查询测试成功: {result}")
        
        # 测试init_database函数
        print("\n🔍 测试init_database函数...")
        success = init_database()
        if success:
            print("✅ init_database函数测试成功")
        else:
            print("❌ init_database函数测试失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 后端数据库修复测试")
    print("=" * 60)
    
    success = test_database_fix()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 数据库修复测试成功!")
        print("✅ 现在可以启动后端服务:")
        print("   python backend/app_enhanced.py")
    else:
        print("⚠️ 数据库修复测试失败")
    print("=" * 60)

if __name__ == "__main__":
    main()
