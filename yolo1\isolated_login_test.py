#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完全独立的登录测试 - 不依赖任何现有模块
"""

import pymysql
import jwt
import time
from datetime import datetime, timedelta
from flask import Flask, request, jsonify
from flask_cors import CORS

app = Flask(__name__)
app.config['SECRET_KEY'] = 'yolo-highway-monitoring-system-2025'

# 启用CORS
CORS(app, supports_credentials=True, origins="*")

def get_db_config():
    """获取数据库配置"""
    return {
        'host': '127.0.0.1',
        'port': 3306,
        'user': 'root',
        'password': '123456',
        'database': 'yolo',
        'charset': 'utf8mb4',
        'autocommit': True,
        'cursorclass': pymysql.cursors.DictCursor
    }

def success_response(data=None, message="操作成功", code=200):
    """成功响应"""
    return jsonify({
        'code': code,
        'message': message,
        'data': data,
        'success': True,
        'timestamp': int(time.time() * 1000)
    })

def error_response(message="操作失败", code=400, data=None):
    """错误响应"""
    return jsonify({
        'code': code,
        'message': message,
        'data': data,
        'success': False,
        'timestamp': int(time.time() * 1000)
    })

@app.route('/')
def index():
    """首页"""
    return jsonify({
        'message': '独立登录测试服务',
        'version': '1.0.0',
        'status': 'running',
        'timestamp': datetime.now().isoformat()
    })

@app.route('/test-db')
def test_db():
    """测试数据库连接"""
    try:
        config = get_db_config()
        connection = pymysql.connect(**config)
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT COUNT(*) as count FROM user")
            result = cursor.fetchone()
            
        connection.close()
        
        return success_response({
            'database': 'connected',
            'user_count': result['count']
        }, '数据库连接成功')
        
    except Exception as e:
        return error_response(f'数据库连接失败: {str(e)}')

@app.route('/login', methods=['POST'])
def login():
    """独立登录测试"""
    print("🔍 独立登录函数开始执行")
    
    try:
        print("1. 获取请求数据...")
        data = request.get_json()
        print(f"   请求数据: {data}")
        
        if not data:
            print("   ❌ 请求数据为空")
            return error_response('请求数据格式错误')
        
        username = data.get('username')
        password = data.get('password')
        print(f"   用户名: {username}, 密码: {password}")
        
        if not username or not password:
            print("   ❌ 用户名或密码为空")
            return error_response('用户名和密码不能为空')
        
        print("2. 连接数据库...")
        config = get_db_config()
        print(f"   数据库配置: {config}")
        
        try:
            connection = pymysql.connect(**config)
            print("   ✅ 数据库连接成功")
            
            with connection.cursor() as cursor:
                print("   ✅ 游标创建成功")
                
                sql = "SELECT * FROM user WHERE username=%s AND password=%s"
                print(f"   执行SQL: {sql}")
                print(f"   参数: ({username}, {password})")
                
                cursor.execute(sql, (username, password))
                print("   ✅ SQL执行成功")
                
                user = cursor.fetchone()
                print(f"   查询结果: {user}")
            
            connection.close()
            print("   ✅ 数据库连接关闭")
            
        except Exception as db_error:
            print(f"   ❌ 数据库操作失败: {str(db_error)}")
            import traceback
            traceback.print_exc()
            return error_response(f'数据库操作失败: {str(db_error)}')

        if not user:
            print("   ❌ 用户验证失败")
            return error_response('用户名或密码错误')
        
        print("3. 生成JWT令牌...")
        try:
            token = jwt.encode({
                'user_id': user['id'],
                'username': user['username'],
                'exp': datetime.utcnow() + timedelta(hours=24)
            }, app.config['SECRET_KEY'], algorithm='HS256')
            print(f"   ✅ Token生成成功: {token[:30]}...")
        except Exception as jwt_error:
            print(f"   ❌ Token生成失败: {str(jwt_error)}")
            return error_response(f'Token生成失败: {str(jwt_error)}')
        
        print("4. 返回成功响应...")
        response_data = {
            'token': token,
            'user': {
                'id': user['id'],
                'username': user['username'],
                'email': user['email'],
                'grade': user['grade'],
                'avatar': user.get('avatar', 'default_avatar.jpg')
            }
        }
        print(f"   响应数据: {response_data}")
        
        return success_response(response_data, '登录成功')
        
    except Exception as e:
        print(f"❌ 登录函数异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return error_response(f'登录失败: {str(e)}')

@app.route('/api/v1/auth/login', methods=['POST'])
def api_login():
    """API格式的登录接口"""
    print("🔍 API登录函数开始执行")
    return login()

if __name__ == '__main__':
    print("=" * 60)
    print("🚀 启动独立登录测试服务")
    print("=" * 60)
    print("服务地址: http://127.0.0.1:5502")
    print("测试端点:")
    print("  GET  /           - 首页")
    print("  GET  /test-db    - 测试数据库")
    print("  POST /login      - 登录测试")
    print("  POST /api/v1/auth/login - API登录测试")
    print("=" * 60)
    
    app.run(host='127.0.0.1', port=5502, debug=True)
