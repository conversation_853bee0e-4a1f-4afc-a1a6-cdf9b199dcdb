#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试监控点状态UI修改效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QWidget
from ui.dialog.multi_tracking_dialog import MultiTrackingForm

def main():
    app = QApplication(sys.argv)
    
    # 创建窗口
    window = QWidget()
    window.setWindowTitle("监控点状态测试 - 取消滚动条，网格布局")
    
    # 设置UI
    ui = MultiTrackingForm()
    ui.setupUi(window)
    
    # 显示窗口
    window.show()
    
    # 启动模拟数据（如果有的话）
    if hasattr(ui, 'mockTrackingData'):
        ui.mockTrackingData()
    
    sys.exit(app.exec())

if __name__ == '__main__':
    main()