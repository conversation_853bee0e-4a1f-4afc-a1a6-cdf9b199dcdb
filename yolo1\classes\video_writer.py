import cv2
import os
from datetime import datetime

class VideoWriter:
    def __init__(self, output_path=None, fps=30, frame_size=None):
        """初始化视频写入器
        
        Args:
            output_path: 输出视频文件路径，如果为None则自动生成
            fps: 视频帧率
            frame_size: 视频帧大小 (width, height)
        """
        self.fps = fps
        self.frame_size = frame_size
        self.writer = None
        self.output_path = output_path or self._generate_output_path()
        
    def _generate_output_path(self):
        """生成输出文件路径"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'pre_result')
        os.makedirs(output_dir, exist_ok=True)
        return os.path.join(output_dir, f'video_result_{timestamp}.mp4')
    
    def init_writer(self, frame=None):
        """初始化视频写入器
        
        Args:
            frame: 首帧，用于获取视频尺寸（如果未指定frame_size）
        """
        if frame is not None and self.frame_size is None:
            self.frame_size = (frame.shape[1], frame.shape[0])
            
        if self.frame_size is None:
            raise ValueError('必须指定frame_size或提供首帧')
            
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        self.writer = cv2.VideoWriter(
            self.output_path,
            fourcc,
            self.fps,
            self.frame_size
        )
        
    def write(self, frame):
        """写入一帧
        
        Args:
            frame: 要写入的帧
        """
        if self.writer is None:
            self.init_writer(frame)
        self.writer.write(frame)
        
    def release(self):
        """释放资源"""
        if self.writer is not None:
            self.writer.release()
            self.writer = None
            
    def __enter__(self):
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.release()