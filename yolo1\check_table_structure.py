#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库表结构 - 确认实际字段名
"""

import pymysql
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(override=True, dotenv_path='config/end-back.env')

def get_db_config():
    """获取数据库配置"""
    return {
        'host': '127.0.0.1',
        'port': 3306,
        'user': 'root',
        'password': '123456',
        'database': 'yolo',
        'charset': 'utf8mb4',
        'autocommit': True,
        'cursorclass': pymysql.cursors.DictCursor
    }

def check_table_structure():
    """检查所有表结构"""
    print("🔍 检查数据库表结构")
    print("=" * 60)
    
    config = get_db_config()
    connection = pymysql.connect(**config)
    
    try:
        with connection.cursor() as cursor:
            # 获取所有表名
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            table_names = [list(table.values())[0] for table in tables]
            
            print(f"数据库表总数: {len(table_names)}")
            print(f"表列表: {', '.join(table_names)}")
            
            # 检查重要表的结构
            important_tables = ['monitor', 'alarm', 'accident_record', 'detection_task', 'traffic_statistics']
            
            for table_name in important_tables:
                if table_name in table_names:
                    print(f"\n📋 {table_name} 表结构:")
                    cursor.execute(f"DESCRIBE {table_name}")
                    columns = cursor.fetchall()
                    
                    for col in columns:
                        print(f"   {col['Field']} - {col['Type']} - {col['Null']} - {col['Key']} - {col['Default']}")
                    
                    # 显示示例数据
                    cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
                    sample_data = cursor.fetchall()
                    if sample_data:
                        print(f"   示例数据 ({len(sample_data)} 条):")
                        for i, row in enumerate(sample_data):
                            print(f"     [{i+1}] {row}")
                    else:
                        print("   ⚠️ 表中无数据")
                else:
                    print(f"\n❌ {table_name} 表不存在")
        
        connection.close()
        
    except Exception as e:
        print(f"❌ 检查表结构失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("=" * 80)
    print("🚀 数据库表结构检查")
    print("=" * 80)
    
    check_table_structure()
    
    print("\n" + "=" * 80)
    print("💡 根据上述表结构信息，我们需要:")
    print("   1. 确认实际的字段名")
    print("   2. 修正测试数据脚本")
    print("   3. 更新API接口")
    print("=" * 80)

if __name__ == "__main__":
    main()
