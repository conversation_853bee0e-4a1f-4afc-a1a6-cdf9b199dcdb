<template>
  <div class="api-integration-test">
    <a-card title="API对接完整性验证">
      <a-alert type="warning" show-icon style="margin-bottom: 16px;">
        <template #title>发现问题</template>
        <div>
          <p>只有监控管理模块有测试数据，其他模块均无数据</p>
          <p>需要验证每个API模块的对接情况</p>
        </div>
      </a-alert>
      
      <a-tabs v-model:active-key="activeTab" type="card">
        <!-- API模块验证 -->
        <a-tab-pane key="modules" title="模块验证">
          <div class="test-section">
            <h3>各API模块对接状态</h3>
            
            <div class="module-grid">
              <div 
                v-for="module in apiModules" 
                :key="module.name"
                class="module-card"
                :class="{ 
                  'success': module.status === 'success',
                  'failed': module.status === 'failed',
                  'testing': module.status === 'testing'
                }"
              >
                <div class="module-header">
                  <h4>{{ module.title }}</h4>
                  <a-tag :color="getStatusColor(module.status)">
                    {{ getStatusText(module.status) }}
                  </a-tag>
                </div>
                
                <div class="module-info">
                  <p><strong>API路径:</strong> {{ module.path }}</p>
                  <p><strong>测试端点:</strong> {{ module.testEndpoint }}</p>
                  <p><strong>预期数据:</strong> {{ module.expectedData }}</p>
                </div>
                
                <div class="module-actions">
                  <a-button 
                    size="small" 
                    @click="testModule(module)"
                    :loading="module.status === 'testing'"
                  >
                    测试模块
                  </a-button>
                  <a-button 
                    size="small" 
                    type="text"
                    @click="viewModuleDetails(module)"
                  >
                    查看详情
                  </a-button>
                </div>
                
                <div class="module-result" v-if="module.result">
                  <h5>测试结果:</h5>
                  <div class="result-summary">
                    <p><strong>状态码:</strong> {{ module.result.status }}</p>
                    <p><strong>响应时间:</strong> {{ module.result.duration }}ms</p>
                    <p><strong>数据条数:</strong> {{ module.result.dataCount || 0 }}</p>
                  </div>
                  
                  <a-collapse size="small">
                    <a-collapse-item header="响应数据" key="data">
                      <pre class="response-data">{{ JSON.stringify(module.result.data, null, 2) }}</pre>
                    </a-collapse-item>
                  </a-collapse>
                </div>
              </div>
            </div>
            
            <a-divider />
            
            <div class="global-actions">
              <a-space>
                <a-button @click="testAllModules" :loading="allTesting" type="primary">
                  测试所有模块
                </a-button>
                <a-button @click="generateIntegrationReport">
                  生成对接报告
                </a-button>
                <a-button @click="checkBackendData">
                  检查后端数据
                </a-button>
              </a-space>
            </div>
          </div>
        </a-tab-pane>

        <!-- 数据完整性检查 -->
        <a-tab-pane key="data-check" title="数据检查">
          <div class="test-section">
            <h3>后端数据完整性检查</h3>
            
            <a-alert type="info" show-icon style="margin-bottom: 16px;">
              <template #title>数据检查说明</template>
              <div>
                <p>检查后端是否有各模块的测试数据</p>
                <p>如果某个模块返回空数据，可能是后端数据库中没有相应的测试数据</p>
              </div>
            </a-alert>
            
            <div class="data-check-list">
              <div v-for="check in dataChecks" :key="check.name" class="check-item">
                <div class="check-header">
                  <h4>{{ check.title }}</h4>
                  <a-button 
                    size="small" 
                    @click="runDataCheck(check)"
                    :loading="check.loading"
                  >
                    检查数据
                  </a-button>
                </div>
                
                <div class="check-details">
                  <p><strong>检查内容:</strong> {{ check.description }}</p>
                  <p><strong>API端点:</strong> <code>{{ check.endpoint }}</code></p>
                  <p><strong>预期结果:</strong> {{ check.expected }}</p>
                </div>
                
                <div class="check-result" v-if="check.result">
                  <a-tag :color="check.result.hasData ? 'green' : 'red'">
                    {{ check.result.hasData ? '有数据' : '无数据' }}
                  </a-tag>
                  <span class="data-count">
                    数据条数: {{ check.result.count || 0 }}
                  </span>
                  
                  <div class="result-details" v-if="!check.result.hasData">
                    <h5>可能的原因:</h5>
                    <ul>
                      <li>后端数据库中没有相应的测试数据</li>
                      <li>API端点返回格式不正确</li>
                      <li>数据库查询逻辑有问题</li>
                      <li>权限验证失败</li>
                    </ul>
                    
                    <h5>建议的解决方案:</h5>
                    <ul>
                      <li>检查后端数据库中是否有 {{ check.table }} 表的数据</li>
                      <li>运行后端的数据初始化脚本</li>
                      <li>检查API的查询逻辑和权限验证</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </a-tab-pane>

        <!-- 后端数据初始化 -->
        <a-tab-pane key="data-init" title="数据初始化">
          <div class="test-section">
            <h3>后端测试数据初始化建议</h3>
            
            <a-alert type="warning" show-icon style="margin-bottom: 16px;">
              <template #title>数据缺失问题</template>
              <div>
                <p>如果API对接正常但返回空数据，说明后端数据库中缺少测试数据</p>
                <p>请将以下SQL脚本发给后端开发者执行</p>
              </div>
            </a-alert>
            
            <a-collapse>
              <a-collapse-item header="用户表初始化数据" key="users">
                <pre class="sql-script">
-- 用户表测试数据
INSERT INTO users (username, password_hash, grade, email, create_time) VALUES
('admin', 'hashed_123456', 'admin', '<EMAIL>', '2024-01-01 00:00:00'),
('operator1', 'hashed_123456', 'operator', '<EMAIL>', '2024-01-01 00:00:00'),
('operator2', 'hashed_123456', 'operator', '<EMAIL>', '2024-01-01 00:00:00');
                </pre>
              </a-collapse-item>
              
              <a-collapse-item header="监控点表初始化数据" key="monitors">
                <pre class="sql-script">
-- 监控点表测试数据
INSERT INTO monitor (name, location, highway_section, camera_position, url, threshold, person, status) VALUES
('杭州收费站监控点', '杭州收费站', 'K0+000', '收费站出入口', 'rtsp://admin:123456@*************:554/stream1', 15, 'admin', 1),
('富阳互通监控点', '富阳互通', 'K15+200', '互通匝道', 'rtsp://admin:123456@*************:554/stream1', 20, 'operator1', 1),
('桐庐服务区监控点', '桐庐服务区', 'K32+500', '服务区入口', 'rtsp://admin:123456@*************:554/stream1', 18, 'admin', 1);
                </pre>
              </a-collapse-item>
              
              <a-collapse-item header="检测记录表初始化数据" key="detection">
                <pre class="sql-script">
-- 检测记录表测试数据
INSERT INTO unified_record (record_type, monitor_id, task_id, status, confidence, create_time) VALUES
('detection', 1, 'TASK_001', 'completed', 0.85, '2024-12-24 10:00:00'),
('detection', 2, 'TASK_002', 'completed', 0.92, '2024-12-24 10:15:00'),
('detection', 3, 'TASK_003', 'running', 0.78, '2024-12-24 10:30:00');
                </pre>
              </a-collapse-item>
              
              <a-collapse-item header="事故记录表初始化数据" key="accident">
                <pre class="sql-script">
-- 事故记录表测试数据
INSERT INTO unified_record (record_type, monitor_id, accident_type, severity, description, status, create_time) VALUES
('accident', 1, 'collision', 'high', '两车追尾事故', 'resolved', '2024-12-24 09:15:00'),
('accident', 2, 'breakdown', 'medium', '车辆故障', 'processing', '2024-12-24 09:30:00');
                </pre>
              </a-collapse-item>
              
              <a-collapse-item header="系统配置表初始化数据" key="config">
                <pre class="sql-script">
-- 系统配置表测试数据
INSERT INTO system_config (config_key, config_value, description) VALUES
('password_policy', '{"min_length": 6, "require_special": false}', '密码策略配置'),
('email_settings', '{"smtp_host": "smtp.qq.com", "smtp_port": 587}', '邮件服务配置'),
('system_name', '高速公路智能监控系统', '系统名称');
                </pre>
              </a-collapse-item>
            </a-collapse>
            
            <a-divider />
            
            <div class="init-actions">
              <a-space>
                <a-button @click="copyAllSqlScripts" type="primary">
                  复制所有SQL脚本
                </a-button>
                <a-button @click="generateDataInitScript">
                  生成数据初始化脚本
                </a-button>
                <a-button @click="testDataAfterInit">
                  初始化后重新测试
                </a-button>
              </a-space>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { Message } from '@arco-design/web-vue'
import axios from 'axios'

const activeTab = ref('modules')
const allTesting = ref(false)

// API模块配置
const apiModules = reactive([
  {
    name: 'auth',
    title: '认证模块',
    path: '/api/v1/auth',
    testEndpoint: '/auth/profile',
    expectedData: '用户信息',
    status: 'pending',
    result: null
  },
  {
    name: 'monitor',
    title: '监控管理',
    path: '/api/v1/monitor',
    testEndpoint: '/monitor/list',
    expectedData: '监控点列表',
    status: 'pending',
    result: null
  },
  {
    name: 'detection',
    title: '检测中心',
    path: '/api/v1/detection',
    testEndpoint: '/detection/tasks',
    expectedData: '检测任务列表',
    status: 'pending',
    result: null
  },
  {
    name: 'tracking',
    title: '多目标追踪',
    path: '/api/v1/tracking',
    testEndpoint: '/tracking/targets/active',
    expectedData: '活跃目标列表',
    status: 'pending',
    result: null
  },
  {
    name: 'accident',
    title: '事故检测',
    path: '/api/v1/accident',
    testEndpoint: '/accident/records',
    expectedData: '事故记录列表',
    status: 'pending',
    result: null
  },
  {
    name: 'analysis',
    title: '数据分析',
    path: '/api/v1/analysis',
    testEndpoint: '/analysis/statistics/overview',
    expectedData: '统计概览数据',
    status: 'pending',
    result: null
  },
  {
    name: 'system',
    title: '系统管理',
    path: '/api/v1/system',
    testEndpoint: '/system/users',
    expectedData: '用户管理数据',
    status: 'pending',
    result: null
  }
])

// 数据检查配置
const dataChecks = reactive([
  {
    name: 'users',
    title: '用户数据检查',
    description: '检查用户表是否有测试用户',
    endpoint: '/api/v1/system/users',
    expected: '至少有admin和operator用户',
    table: 'users',
    loading: false,
    result: null
  },
  {
    name: 'monitors',
    title: '监控点数据检查',
    description: '检查监控点表是否有测试数据',
    endpoint: '/api/v1/monitor/list',
    expected: '至少有3个测试监控点',
    table: 'monitor',
    loading: false,
    result: null
  },
  {
    name: 'detection_tasks',
    title: '检测任务数据检查',
    description: '检查检测任务表是否有历史数据',
    endpoint: '/api/v1/detection/tasks',
    expected: '有检测任务记录',
    table: 'unified_record (detection)',
    loading: false,
    result: null
  },
  {
    name: 'accident_records',
    title: '事故记录数据检查',
    description: '检查事故记录表是否有测试数据',
    endpoint: '/api/v1/accident/records',
    expected: '有事故记录数据',
    table: 'unified_record (accident)',
    loading: false,
    result: null
  }
])

// 获取状态颜色
const getStatusColor = (status: string) => {
  switch (status) {
    case 'success': return 'green'
    case 'failed': return 'red'
    case 'testing': return 'blue'
    default: return 'gray'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'success': return '成功'
    case 'failed': return '失败'
    case 'testing': return '测试中'
    default: return '未测试'
  }
}

// 测试单个模块
const testModule = async (module: any) => {
  module.status = 'testing'
  const startTime = Date.now()

  try {
    const response = await axios.get(module.testEndpoint, {
      timeout: 5000,
      headers: {
        'Authorization': 'Bearer test-token'
      }
    })

    const duration = Date.now() - startTime
    const data = response.data

    // 分析数据
    let dataCount = 0
    if (data.success && data.data) {
      if (Array.isArray(data.data)) {
        dataCount = data.data.length
      } else if (data.data.monitors) {
        dataCount = data.data.monitors.length
      } else if (data.data.users) {
        dataCount = data.data.users.length
      } else if (data.data.records) {
        dataCount = data.data.records.length
      } else if (data.data.tasks) {
        dataCount = data.data.tasks.length
      } else if (typeof data.data === 'object') {
        dataCount = Object.keys(data.data).length
      }
    }

    module.result = {
      status: response.status,
      duration,
      data: data,
      dataCount,
      hasData: dataCount > 0 || (data.success && data.data)
    }

    module.status = data.success ? 'success' : 'failed'

    if (data.success) {
      Message.success(`${module.title} 测试成功，数据条数: ${dataCount}`)
    } else {
      Message.warning(`${module.title} API正常但无数据`)
    }

  } catch (error: any) {
    const duration = Date.now() - startTime

    module.result = {
      status: error.response?.status || 'Network Error',
      duration,
      data: error.response?.data || { error: error.message },
      dataCount: 0,
      hasData: false
    }

    module.status = 'failed'
    Message.error(`${module.title} 测试失败: ${error.message}`)
  }
}

// 测试所有模块
const testAllModules = async () => {
  allTesting.value = true

  Message.info('开始测试所有API模块...')

  for (const module of apiModules) {
    await testModule(module)
    // 添加延迟避免请求过于频繁
    await new Promise(resolve => setTimeout(resolve, 500))
  }

  allTesting.value = false

  const successCount = apiModules.filter(m => m.status === 'success').length
  const hasDataCount = apiModules.filter(m => m.result?.hasData).length

  Message.success(`测试完成！${successCount}/${apiModules.length} 个模块API正常，${hasDataCount}/${apiModules.length} 个模块有数据`)
}

// 查看模块详情
const viewModuleDetails = (module: any) => {
  if (module.result) {
    console.log(`${module.title} 详细结果:`, module.result)
    Message.info(`${module.title} 详细结果已输出到控制台`)
  } else {
    Message.warning(`${module.title} 尚未测试`)
  }
}

// 运行数据检查
const runDataCheck = async (check: any) => {
  check.loading = true

  try {
    const response = await axios.get(check.endpoint, {
      timeout: 5000,
      headers: {
        'Authorization': 'Bearer test-token'
      }
    })

    const data = response.data
    let count = 0
    let hasData = false

    if (data.success && data.data) {
      if (Array.isArray(data.data)) {
        count = data.data.length
        hasData = count > 0
      } else if (data.data.monitors) {
        count = data.data.monitors.length
        hasData = count > 0
      } else if (data.data.users) {
        count = data.data.users.length
        hasData = count > 0
      } else if (data.data.records) {
        count = data.data.records.length
        hasData = count > 0
      } else if (data.data.tasks) {
        count = data.data.tasks.length
        hasData = count > 0
      } else if (typeof data.data === 'object') {
        hasData = Object.keys(data.data).length > 0
        count = Object.keys(data.data).length
      }
    }

    check.result = {
      hasData,
      count,
      response: data
    }

    if (hasData) {
      Message.success(`${check.title}: 发现 ${count} 条数据`)
    } else {
      Message.warning(`${check.title}: 无数据，需要初始化`)
    }

  } catch (error: any) {
    check.result = {
      hasData: false,
      count: 0,
      error: error.message
    }
    Message.error(`${check.title} 检查失败: ${error.message}`)
  } finally {
    check.loading = false
  }
}

// 检查后端数据
const checkBackendData = async () => {
  Message.info('开始检查后端数据完整性...')

  for (const check of dataChecks) {
    await runDataCheck(check)
    await new Promise(resolve => setTimeout(resolve, 300))
  }

  const hasDataCount = dataChecks.filter(c => c.result?.hasData).length
  Message.success(`数据检查完成！${hasDataCount}/${dataChecks.length} 个模块有数据`)
}

// 生成对接报告
const generateIntegrationReport = () => {
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      total_modules: apiModules.length,
      api_success: apiModules.filter(m => m.status === 'success').length,
      has_data: apiModules.filter(m => m.result?.hasData).length,
      data_checks: dataChecks.filter(c => c.result?.hasData).length
    },
    modules: apiModules.map(m => ({
      name: m.name,
      title: m.title,
      status: m.status,
      endpoint: m.testEndpoint,
      has_data: m.result?.hasData || false,
      data_count: m.result?.dataCount || 0,
      response_time: m.result?.duration || 0
    })),
    data_checks: dataChecks.map(c => ({
      name: c.name,
      title: c.title,
      has_data: c.result?.hasData || false,
      count: c.result?.count || 0,
      table: c.table
    })),
    recommendations: generateRecommendations()
  }

  const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `api-integration-report-${Date.now()}.json`
  link.click()
  URL.revokeObjectURL(url)

  Message.success('API对接报告已生成')
}

// 生成建议
const generateRecommendations = () => {
  const recommendations = []

  const failedModules = apiModules.filter(m => m.status === 'failed')
  const noDataModules = apiModules.filter(m => m.status === 'success' && !m.result?.hasData)

  if (failedModules.length > 0) {
    recommendations.push(`修复失败的API模块: ${failedModules.map(m => m.title).join(', ')}`)
  }

  if (noDataModules.length > 0) {
    recommendations.push(`为以下模块添加测试数据: ${noDataModules.map(m => m.title).join(', ')}`)
  }

  const noDataChecks = dataChecks.filter(c => c.result && !c.result.hasData)
  if (noDataChecks.length > 0) {
    recommendations.push(`初始化以下数据表: ${noDataChecks.map(c => c.table).join(', ')}`)
  }

  return recommendations
}

// 复制SQL脚本
const copyAllSqlScripts = () => {
  const scripts = [
    "-- 用户表测试数据",
    "INSERT INTO users (username, password_hash, grade, email, create_time) VALUES",
    "('admin', 'hashed_123456', 'admin', '<EMAIL>', '2024-01-01 00:00:00'),",
    "('operator1', 'hashed_123456', 'operator', '<EMAIL>', '2024-01-01 00:00:00'),",
    "('operator2', 'hashed_123456', 'operator', '<EMAIL>', '2024-01-01 00:00:00');",
    "",
    "-- 监控点表测试数据",
    "INSERT INTO monitor (name, location, highway_section, camera_position, url, threshold, person, status) VALUES",
    "('杭州收费站监控点', '杭州收费站', 'K0+000', '收费站出入口', 'rtsp://admin:123456@*************:554/stream1', 15, 'admin', 1),",
    "('富阳互通监控点', '富阳互通', 'K15+200', '互通匝道', 'rtsp://admin:123456@*************:554/stream1', 20, 'operator1', 1),",
    "('桐庐服务区监控点', '桐庐服务区', 'K32+500', '服务区入口', 'rtsp://admin:123456@*************:554/stream1', 18, 'admin', 1);",
    "",
    "-- 检测记录表测试数据",
    "INSERT INTO unified_record (record_type, monitor_id, task_id, status, confidence, create_time) VALUES",
    "('detection', 1, 'TASK_001', 'completed', 0.85, '2024-12-24 10:00:00'),",
    "('detection', 2, 'TASK_002', 'completed', 0.92, '2024-12-24 10:15:00'),",
    "('detection', 3, 'TASK_003', 'running', 0.78, '2024-12-24 10:30:00');",
    "",
    "-- 事故记录表测试数据",
    "INSERT INTO unified_record (record_type, monitor_id, accident_type, severity, description, status, create_time) VALUES",
    "('accident', 1, 'collision', 'high', '两车追尾事故', 'resolved', '2024-12-24 09:15:00'),",
    "('accident', 2, 'breakdown', 'medium', '车辆故障', 'processing', '2024-12-24 09:30:00');"
  ].join('\n')

  navigator.clipboard.writeText(scripts).then(() => {
    Message.success('SQL脚本已复制到剪贴板')
  }).catch(() => {
    Message.error('复制失败，请手动复制')
  })
}

// 生成数据初始化脚本
const generateDataInitScript = () => {
  const script = `
# 后端数据初始化脚本
# 请后端开发者执行以下步骤

## 1. 检查数据库连接
确保数据库服务正常运行，连接配置正确

## 2. 执行SQL脚本
运行上面的SQL脚本，创建测试数据

## 3. 验证数据
SELECT COUNT(*) FROM users;
SELECT COUNT(*) FROM monitor;
SELECT COUNT(*) FROM unified_record;

## 4. 重启后端服务
确保新数据被正确加载

## 5. 重新测试前端
访问 http://localhost:3000/api-integration-test 重新测试
`

  const blob = new Blob([script], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = 'backend-data-init-guide.md'
  link.click()
  URL.revokeObjectURL(url)

  Message.success('数据初始化指南已生成')
}

// 初始化后重新测试
const testDataAfterInit = async () => {
  Message.info('重新测试所有模块...')
  await testAllModules()
  await checkBackendData()
}
</script>

<style scoped>
.api-integration-test {
  padding: 20px;
}

.test-section {
  margin-bottom: 24px;
}

.test-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.module-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.module-card {
  padding: 16px;
  border: 1px solid #e5e6eb;
  border-radius: 8px;
  background: #fff;
}

.module-card.success {
  border-color: #52c41a;
  background: #f6ffed;
}

.module-card.failed {
  border-color: #ff4d4f;
  background: #fff2f0;
}

.module-card.testing {
  border-color: #1890ff;
  background: #f0f8ff;
}

.module-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.module-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.module-info {
  margin-bottom: 12px;
}

.module-info p {
  margin: 4px 0;
  font-size: 12px;
  color: #666;
}

.module-actions {
  margin-bottom: 12px;
}

.module-result {
  border-top: 1px solid #e5e6eb;
  padding-top: 12px;
}

.module-result h5 {
  margin: 0 0 8px 0;
  font-size: 12px;
  font-weight: 600;
}

.result-summary p {
  margin: 4px 0;
  font-size: 12px;
}

.response-data {
  font-family: 'Courier New', monospace;
  font-size: 11px;
  max-height: 200px;
  overflow-y: auto;
  margin: 0;
}

.global-actions {
  text-align: center;
  margin: 24px 0;
}

.data-check-list {
  margin-top: 16px;
}

.check-item {
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid #e5e6eb;
  border-radius: 8px;
}

.check-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.check-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.check-details p {
  margin: 4px 0;
  font-size: 12px;
  color: #666;
}

.check-result {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #e5e6eb;
}

.data-count {
  margin-left: 12px;
  font-size: 12px;
  color: #666;
}

.result-details {
  margin-top: 12px;
}

.result-details h5 {
  margin: 8px 0 4px 0;
  font-size: 12px;
  font-weight: 600;
}

.result-details ul {
  margin: 4px 0;
  padding-left: 20px;
}

.result-details li {
  margin: 2px 0;
  font-size: 12px;
}

.sql-script {
  background: #f7f8fa;
  padding: 12px;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  margin: 0;
  border: 1px solid #e5e6eb;
  overflow-x: auto;
}

.init-actions {
  text-align: center;
  margin: 24px 0;
}
</style>
