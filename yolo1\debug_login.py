#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试登录接口响应格式
"""

import requests
import json

def debug_login():
    """调试登录接口"""
    base_url = "http://127.0.0.1:5500"
    
    print("🔍 调试登录接口响应格式")
    print("=" * 50)
    
    try:
        login_data = {"username": "admin", "password": "123456"}
        response = requests.post(f"{base_url}/api/v1/auth/login", json=login_data, timeout=5)
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应文本: {response.text}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"JSON数据类型: {type(data)}")
                print(f"JSON数据内容: {json.dumps(data, indent=2, ensure_ascii=False)}")
                
                # 检查数据结构
                if isinstance(data, dict):
                    print(f"顶级键: {list(data.keys())}")
                    
                    # 检查不同的可能格式
                    if 'data' in data:
                        print(f"data字段: {data['data']}")
                        if isinstance(data['data'], dict) and 'token' in data['data']:
                            print(f"Token: {data['data']['token'][:30]}...")
                        else:
                            print("❌ data字段中没有token")
                    else:
                        print("❌ 响应中没有data字段")
                        
                    # 检查其他可能的token位置
                    if 'token' in data:
                        print(f"直接token字段: {data['token'][:30]}...")
                    
                else:
                    print("❌ 响应不是字典格式")
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
        else:
            print(f"❌ 登录失败，状态码: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

if __name__ == "__main__":
    debug_login()
