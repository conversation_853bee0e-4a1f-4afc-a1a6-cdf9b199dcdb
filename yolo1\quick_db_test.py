#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速数据库测试 - 确定正确的字段名
"""

import os
import sys
import pymysql
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(override=True, dotenv_path='config/end-back.env')

def test_table_query():
    """测试表查询的正确字段名"""
    config = {
        'host': os.getenv('MYSQL_HOST', '127.0.0.1'),
        'port': int(os.getenv('MYSQL_PORT', 3306)),
        'user': os.getenv('MYSQL_USER', os.getenv('MYSQL_user', 'root')),
        'password': os.getenv('MYSQL_PASSWORD', os.getenv('MYSQL_password', '123456')),
        'database': os.getenv('MYSQL_DATABASE', os.getenv('MYSQL_db', 'yolo')),
        'charset': 'utf8mb4',
        'autocommit': True,
        'cursorclass': pymysql.cursors.DictCursor
    }
    
    try:
        connection = pymysql.connect(**config)
        with connection.cursor() as cursor:
            # 测试查询
            cursor.execute(
                "SELECT table_name FROM information_schema.tables "
                "WHERE table_schema = DATABASE() ORDER BY table_name LIMIT 1"
            )
            result = cursor.fetchone()
            
            print("查询结果:")
            print(f"类型: {type(result)}")
            print(f"内容: {result}")
            
            if result:
                print("可用字段:")
                for key in result.keys():
                    print(f"  {key}: {result[key]}")
        
        connection.close()
        
    except Exception as e:
        print(f"测试失败: {e}")

if __name__ == "__main__":
    test_table_query()
