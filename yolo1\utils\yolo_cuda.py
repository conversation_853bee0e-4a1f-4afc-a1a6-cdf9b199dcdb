# -*- coding: utf-8 -*-
# @Description : CUDA加速的YOLO检测工具
# @Date : 2025年6月20日

import os
import cv2
import numpy as np
from typing import List, Dict, Any, Tuple, Optional
import time

class CudaYOLODetector:
    """CUDA加速的YOLO检测器"""
    
    def __init__(self, model_path: str = "./models/car.pt", device: str = "auto"):
        """
        初始化YOLO检测器
        
        Args:
            model_path: 模型文件路径
            device: 计算设备 ("auto", "cuda", "cpu")
        """
        self.model_path = model_path
        self.model = None
        self.device = self._get_device(device)
        self.load_model()
    
    def _get_device(self, device: str) -> str:
        """获取计算设备"""
        if device == "auto":
            try:
                import torch
                if torch.cuda.is_available():
                    return "cuda"
                else:
                    return "cpu"
            except ImportError:
                return "cpu"
        return device
    
    def load_model(self):
        """加载YOLO模型"""
        try:
            from ultralytics import YOLO
            
            # 检查模型文件是否存在
            if not os.path.exists(self.model_path):
                print(f"模型文件不存在: {self.model_path}")
                print("使用默认YOLOv8n模型...")
                self.model_path = "yolov8n.pt"
            
            # 加载模型
            self.model = YOLO(self.model_path)
            
            # 设置设备
            if self.device == "cuda":
                try:
                    self.model.to("cuda")
                    print(f"✓ YOLO模型已加载到GPU: {self.model_path}")
                except Exception as e:
                    print(f"GPU加载失败，回退到CPU: {e}")
                    self.device = "cpu"
                    self.model.to("cpu")
            else:
                self.model.to("cpu")
                print(f"✓ YOLO模型已加载到CPU: {self.model_path}")
            
            return True
            
        except ImportError as e:
            print(f"Ultralytics导入失败: {e}")
            return False
        except Exception as e:
            print(f"模型加载失败: {e}")
            return False
    
    def detect_image(self, image: np.ndarray, conf_threshold: float = 0.4, 
                    iou_threshold: float = 0.5) -> Dict[str, Any]:
        """
        检测图像中的目标
        
        Args:
            image: 输入图像
            conf_threshold: 置信度阈值
            iou_threshold: IOU阈值
        
        Returns:
            检测结果字典
        """
        if self.model is None:
            return {"error": "模型未加载"}
        
        try:
            start_time = time.time()
            
            # 执行检测
            results = self.model(
                image,
                conf=conf_threshold,
                iou=iou_threshold,
                device=self.device,
                verbose=False
            )
            
            inference_time = time.time() - start_time
            
            # 解析结果
            detections = []
            if len(results) > 0:
                result = results[0]
                
                if result.boxes is not None:
                    boxes = result.boxes.xyxy.cpu().numpy()  # 边界框
                    confidences = result.boxes.conf.cpu().numpy()  # 置信度
                    class_ids = result.boxes.cls.cpu().numpy().astype(int)  # 类别ID
                    
                    for i, (box, conf, cls_id) in enumerate(zip(boxes, confidences, class_ids)):
                        x1, y1, x2, y2 = box
                        detections.append({
                            "id": i,
                            "bbox": [float(x1), float(y1), float(x2), float(y2)],
                            "confidence": float(conf),
                            "class_id": int(cls_id),
                            "class_name": self.model.names[cls_id] if cls_id in self.model.names else "unknown"
                        })
            
            # 统计信息
            class_counts = {}
            total_confidence = 0
            for det in detections:
                class_name = det["class_name"]
                class_counts[class_name] = class_counts.get(class_name, 0) + 1
                total_confidence += det["confidence"]
            
            avg_confidence = total_confidence / len(detections) if detections else 0
            
            return {
                "detections": detections,
                "statistics": {
                    "total_objects": len(detections),
                    "class_counts": class_counts,
                    "avg_confidence": avg_confidence,
                    "max_confidence": max([d["confidence"] for d in detections]) if detections else 0,
                    "min_confidence": min([d["confidence"] for d in detections]) if detections else 0
                },
                "performance": {
                    "inference_time": inference_time,
                    "device": self.device,
                    "fps": 1.0 / inference_time if inference_time > 0 else 0
                },
                "parameters": {
                    "conf_threshold": conf_threshold,
                    "iou_threshold": iou_threshold,
                    "model_path": self.model_path
                }
            }
            
        except Exception as e:
            return {"error": f"检测失败: {str(e)}"}
    
    def draw_detections(self, image: np.ndarray, detections: List[Dict], 
                       show_labels: bool = True, show_conf: bool = True) -> np.ndarray:
        """
        在图像上绘制检测结果
        
        Args:
            image: 原始图像
            detections: 检测结果列表
            show_labels: 是否显示标签
            show_conf: 是否显示置信度
        
        Returns:
            绘制了检测框的图像
        """
        result_image = image.copy()
        
        # 定义颜色（BGR格式）
        colors = [
            (0, 255, 0),    # 绿色
            (255, 0, 0),    # 蓝色
            (0, 0, 255),    # 红色
            (255, 255, 0),  # 青色
            (255, 0, 255),  # 洋红色
            (0, 255, 255),  # 黄色
        ]
        
        for i, detection in enumerate(detections):
            bbox = detection["bbox"]
            confidence = detection["confidence"]
            class_name = detection["class_name"]
            
            # 获取颜色
            color = colors[i % len(colors)]
            
            # 绘制边界框
            x1, y1, x2, y2 = map(int, bbox)
            cv2.rectangle(result_image, (x1, y1), (x2, y2), color, 2)
            
            # 绘制标签
            if show_labels:
                label = class_name
                if show_conf:
                    label += f" {confidence:.2f}"
                
                # 计算文本大小
                font = cv2.FONT_HERSHEY_SIMPLEX
                font_scale = 0.6
                thickness = 1
                (text_width, text_height), _ = cv2.getTextSize(label, font, font_scale, thickness)
                
                # 绘制文本背景
                cv2.rectangle(result_image, (x1, y1 - text_height - 10), 
                            (x1 + text_width, y1), color, -1)
                
                # 绘制文本
                cv2.putText(result_image, label, (x1, y1 - 5), 
                          font, font_scale, (255, 255, 255), thickness)
        
        return result_image
    
    def detect_and_draw(self, image: np.ndarray, conf_threshold: float = 0.4,
                       iou_threshold: float = 0.5, show_labels: bool = True) -> Tuple[np.ndarray, Dict]:
        """
        检测并绘制结果
        
        Args:
            image: 输入图像
            conf_threshold: 置信度阈值
            iou_threshold: IOU阈值
            show_labels: 是否显示标签
        
        Returns:
            (结果图像, 检测结果字典)
        """
        # 执行检测
        detection_result = self.detect_image(image, conf_threshold, iou_threshold)
        
        if "error" in detection_result:
            return image, detection_result
        
        # 绘制结果
        result_image = self.draw_detections(
            image, 
            detection_result["detections"], 
            show_labels=show_labels
        )
        
        return result_image, detection_result
    
    def get_device_info(self) -> Dict[str, Any]:
        """获取设备信息"""
        info = {
            "current_device": self.device,
            "model_loaded": self.model is not None
        }
        
        try:
            import torch
            info.update({
                "torch_version": torch.__version__,
                "cuda_available": torch.cuda.is_available(),
                "cuda_version": torch.version.cuda if torch.cuda.is_available() else None,
                "gpu_count": torch.cuda.device_count() if torch.cuda.is_available() else 0
            })
            
            if torch.cuda.is_available():
                info["gpu_info"] = []
                for i in range(torch.cuda.device_count()):
                    gpu_info = {
                        "id": i,
                        "name": torch.cuda.get_device_name(i),
                        "memory_total": torch.cuda.get_device_properties(i).total_memory,
                        "memory_allocated": torch.cuda.memory_allocated(i),
                        "memory_cached": torch.cuda.memory_reserved(i)
                    }
                    info["gpu_info"].append(gpu_info)
        except ImportError:
            info["torch_available"] = False
        
        return info
    
    def benchmark(self, image_size: Tuple[int, int] = (640, 640), 
                 iterations: int = 10) -> Dict[str, Any]:
        """
        性能基准测试
        
        Args:
            image_size: 测试图像尺寸
            iterations: 测试迭代次数
        
        Returns:
            基准测试结果
        """
        if self.model is None:
            return {"error": "模型未加载"}
        
        # 创建测试图像
        test_image = np.random.randint(0, 255, (*image_size, 3), dtype=np.uint8)
        
        # 预热
        for _ in range(3):
            self.detect_image(test_image)
        
        # 基准测试
        times = []
        for i in range(iterations):
            start_time = time.time()
            result = self.detect_image(test_image)
            end_time = time.time()
            
            if "error" not in result:
                times.append(end_time - start_time)
        
        if not times:
            return {"error": "基准测试失败"}
        
        avg_time = np.mean(times)
        std_time = np.std(times)
        min_time = np.min(times)
        max_time = np.max(times)
        
        return {
            "device": self.device,
            "image_size": image_size,
            "iterations": len(times),
            "avg_inference_time": avg_time,
            "std_inference_time": std_time,
            "min_inference_time": min_time,
            "max_inference_time": max_time,
            "avg_fps": 1.0 / avg_time,
            "model_path": self.model_path
        }

# 全局检测器实例
_global_detector = None

def get_detector(model_path: str = "./models/car.pt", device: str = "auto") -> CudaYOLODetector:
    """获取全局检测器实例"""
    global _global_detector
    
    if _global_detector is None:
        _global_detector = CudaYOLODetector(model_path, device)
    
    return _global_detector

def detect_image_simple(image: np.ndarray, conf_threshold: float = 0.4) -> Dict[str, Any]:
    """简化的图像检测接口"""
    detector = get_detector()
    return detector.detect_image(image, conf_threshold)

def detect_and_draw_simple(image: np.ndarray, conf_threshold: float = 0.4) -> Tuple[np.ndarray, Dict]:
    """简化的检测和绘制接口"""
    detector = get_detector()
    return detector.detect_and_draw(image, conf_threshold)
