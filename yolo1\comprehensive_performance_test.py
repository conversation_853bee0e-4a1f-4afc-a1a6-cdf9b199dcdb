#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合性能测试脚本 - 对比yolo和yolo1项目的GPU推理性能
测试所有可能影响GPU推理的因素
"""

import sys
import os
import time
import numpy as np
import cv2
import torch
from ultralytics import YOLO
import supervision as sv
from pathlib import Path

def test_basic_gpu_performance():
    """测试基础GPU推理性能"""
    print("=== 基础GPU推理性能测试 ===")
    
    # 检查CUDA
    print(f"CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"GPU设备: {torch.cuda.get_device_name()}")
        print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")
    
    # 加载模型
    model_path = "./models/car.pt"
    if not os.path.exists(model_path):
        model_path = "yolov8n.pt"
    
    print(f"加载模型: {model_path}")
    model = YOLO(model_path)
    
    # 创建测试图像
    test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
    
    # GPU预热
    print("GPU预热中...")
    for _ in range(10):
        _ = model(test_image, device='cuda', verbose=False)
    
    # 性能测试
    test_iterations = 100
    times = []
    
    print(f"开始性能测试 ({test_iterations}次迭代)...")
    
    for i in range(test_iterations):
        start_time = time.time()
        result = model(test_image, device='cuda', verbose=False)
        end_time = time.time()
        
        iteration_time = end_time - start_time
        times.append(iteration_time)
        
        if i % 20 == 0:
            print(f"迭代 {i+1:3d}: {iteration_time:.4f}s")
    
    # 统计结果
    avg_time = np.mean(times)
    min_time = np.min(times)
    max_time = np.max(times)
    std_time = np.std(times)
    avg_fps = 1.0 / avg_time
    
    print(f"\n性能统计:")
    print(f"平均耗时: {avg_time:.4f}s")
    print(f"最小耗时: {min_time:.4f}s")
    print(f"最大耗时: {max_time:.4f}s")
    print(f"标准差: {std_time:.4f}s")
    print(f"平均FPS: {avg_fps:.2f}")
    
    return avg_fps, avg_time

def test_supervision_compatibility():
    """测试supervision库兼容性对性能的影响"""
    print("\n=== Supervision兼容性性能测试 ===")
    
    # 加载模型
    model_path = "./models/car.pt"
    if not os.path.exists(model_path):
        model_path = "yolov8n.pt"
    
    model = YOLO(model_path)
    test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
    
    # 测试不同的supervision方法
    methods = [
        ("sv.Detections.from_yolov8", lambda r: sv.Detections.from_yolov8(r)),
        ("sv.Detections.from_ultralytics", lambda r: sv.Detections.from_ultralytics(r))
    ]
    
    results = {}
    
    for method_name, method_func in methods:
        print(f"\n测试方法: {method_name}")
        
        # GPU预热
        for _ in range(5):
            result = model(test_image, device='cuda', verbose=False)
            try:
                _ = method_func(result[0])
            except Exception as e:
                print(f"方法 {method_name} 不可用: {e}")
                continue
        
        # 性能测试
        times = []
        test_iterations = 50
        
        for i in range(test_iterations):
            start_time = time.time()
            
            # 推理
            result = model(test_image, device='cuda', verbose=False)
            
            # 转换检测结果
            try:
                detections = method_func(result[0])
            except Exception as e:
                print(f"转换失败: {e}")
                break
            
            end_time = time.time()
            times.append(end_time - start_time)
        
        if times:
            avg_time = np.mean(times)
            avg_fps = 1.0 / avg_time
            results[method_name] = {'fps': avg_fps, 'time': avg_time}
            print(f"平均耗时: {avg_time:.4f}s, 平均FPS: {avg_fps:.2f}")
    
    return results

def test_memory_usage():
    """测试GPU内存使用情况"""
    print("\n=== GPU内存使用测试 ===")
    
    if not torch.cuda.is_available():
        print("CUDA不可用，跳过内存测试")
        return
    
    # 清空GPU缓存
    torch.cuda.empty_cache()
    
    # 记录初始内存
    initial_memory = torch.cuda.memory_allocated()
    print(f"初始GPU内存使用: {initial_memory / 1024**2:.1f}MB")
    
    # 加载模型
    model_path = "./models/car.pt"
    if not os.path.exists(model_path):
        model_path = "yolov8n.pt"
    
    model = YOLO(model_path)
    
    # 记录模型加载后内存
    model_memory = torch.cuda.memory_allocated()
    print(f"模型加载后GPU内存使用: {model_memory / 1024**2:.1f}MB")
    print(f"模型占用内存: {(model_memory - initial_memory) / 1024**2:.1f}MB")
    
    # 测试推理内存使用
    test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
    
    # 单次推理
    result = model(test_image, device='cuda', verbose=False)
    inference_memory = torch.cuda.memory_allocated()
    print(f"推理后GPU内存使用: {inference_memory / 1024**2:.1f}MB")
    print(f"推理额外内存: {(inference_memory - model_memory) / 1024**2:.1f}MB")
    
    # 测试内存泄漏
    print("\n测试内存泄漏...")
    for i in range(20):
        _ = model(test_image, device='cuda', verbose=False)
        if i % 5 == 0:
            current_memory = torch.cuda.memory_allocated()
            print(f"迭代 {i+1:2d}: {current_memory / 1024**2:.1f}MB")
    
    final_memory = torch.cuda.memory_allocated()
    print(f"最终GPU内存使用: {final_memory / 1024**2:.1f}MB")
    
    if final_memory > inference_memory * 1.1:
        print("⚠️  检测到可能的内存泄漏")
    else:
        print("✅ 未检测到明显内存泄漏")

def test_batch_processing():
    """测试批处理性能"""
    print("\n=== 批处理性能测试 ===")
    
    model_path = "./models/car.pt"
    if not os.path.exists(model_path):
        model_path = "yolov8n.pt"
    
    model = YOLO(model_path)
    
    batch_sizes = [1, 2, 4, 8]
    
    for batch_size in batch_sizes:
        print(f"\n测试批大小: {batch_size}")
        
        # 创建批量图像
        batch_images = [np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8) for _ in range(batch_size)]
        
        # GPU预热
        for _ in range(5):
            _ = model(batch_images, device='cuda', verbose=False)
        
        # 性能测试
        times = []
        test_iterations = 20
        
        for i in range(test_iterations):
            start_time = time.time()
            results = model(batch_images, device='cuda', verbose=False)
            end_time = time.time()
            
            times.append(end_time - start_time)
        
        avg_time = np.mean(times)
        fps_per_image = batch_size / avg_time
        
        print(f"批大小 {batch_size}: 平均耗时 {avg_time:.4f}s, 每图像FPS: {fps_per_image:.2f}")

def main():
    """主测试函数"""
    print("yolo1项目 - 综合GPU推理性能测试")
    print("=" * 50)
    
    # 检查当前目录
    current_dir = os.getcwd()
    print(f"当前目录: {current_dir}")
    
    # 检查模型文件
    model_files = ["./models/car.pt", "yolov8n.pt"]
    available_models = [f for f in model_files if os.path.exists(f)]
    print(f"可用模型: {available_models}")
    
    if not available_models:
        print("❌ 未找到可用的模型文件")
        return
    
    try:
        # 基础性能测试
        basic_fps, basic_time = test_basic_gpu_performance()
        
        # Supervision兼容性测试
        supervision_results = test_supervision_compatibility()
        
        # 内存使用测试
        test_memory_usage()
        
        # 批处理测试
        test_batch_processing()
        
        # 总结报告
        print("\n" + "=" * 50)
        print("测试总结报告")
        print("=" * 50)
        print(f"基础GPU推理性能: {basic_fps:.2f} FPS")
        
        if supervision_results:
            print("\nSupervision方法性能对比:")
            for method, result in supervision_results.items():
                print(f"  {method}: {result['fps']:.2f} FPS")
        
        # 性能评估
        if basic_fps >= 60:
            print(f"\n✅ 性能优秀: {basic_fps:.2f} FPS >= 60 FPS")
        elif basic_fps >= 30:
            print(f"\n⚠️  性能良好: {basic_fps:.2f} FPS >= 30 FPS")
        else:
            print(f"\n❌ 性能不足: {basic_fps:.2f} FPS < 30 FPS")
            
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()