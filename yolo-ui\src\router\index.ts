import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/auth/LoginView.vue'),
      meta: { requiresAuth: false }
    },
    {
      path: '/',
      redirect: '/dashboard'
    },
    {
      path: '/',
      component: () => import('@/layouts/MainLayout.vue'),
      meta: { requiresAuth: true },
      children: [
        {
          path: 'dashboard',
          name: 'dashboard',
          component: () => import('@/views/dashboard/DashboardView.vue'),
          meta: { requiresAuth: true }
        },
        {
          path: 'monitor',
          name: 'monitor',
          component: () => import('@/views/monitor/MonitorView.vue'),
          meta: { requiresAuth: true }
        },
        {
          path: 'detection',
          name: 'detection',
          component: () => import('@/views/detection/DetectionMain.vue'),
          meta: { requiresAuth: true }
        },
        {
          path: 'tracking',
          name: 'tracking',
          component: () => import('@/views/tracking/TrackingView.vue'),
          meta: { requiresAuth: true }
        },
        {
          path: 'accident',
          name: 'accident',
          component: () => import('@/views/accident/AccidentView.vue'),
          meta: { requiresAuth: true }
        },
        {
          path: 'analysis',
          name: 'analysis',
          component: () => import('@/views/analysis/AnalysisView.vue'),
          meta: { requiresAuth: true }
        },
        {
          path: 'system',
          name: 'system',
          component: () => import('@/views/system/SystemView.vue'),
          meta: { requiresAuth: true, requiresAdmin: true }
        },
        {
          path: 'profile',
          name: 'profile',
          component: () => import('@/views/profile/ProfileView.vue'),
          meta: { requiresAuth: true }
        },
        {
          path: 'api-test',
          name: 'api-test',
          component: () => import('@/views/test/ApiTestView.vue'),
          meta: { requiresAuth: false }
        },
        {
          path: 'business-test',
          name: 'business-test',
          component: () => import('@/views/test/BusinessTestView.vue'),
          meta: { requiresAuth: false }
        },
        {
          path: 'login-diagnostic',
          name: 'login-diagnostic',
          component: () => import('@/views/test/LoginDiagnosticView.vue'),
          meta: { requiresAuth: false }
        },
        {
          path: 'api-path-diagnostic',
          name: 'api-path-diagnostic',
          component: () => import('@/views/test/ApiPathDiagnosticView.vue'),
          meta: { requiresAuth: false }
        },
        {
          path: 'api-integration-test',
          name: 'api-integration-test',
          component: () => import('@/views/test/ApiIntegrationTestView.vue'),
          meta: { requiresAuth: false }
        },
        {
          path: 'api-status-check',
          name: 'api-status-check',
          component: () => import('@/views/test/ApiStatusCheckView.vue'),
          meta: { requiresAuth: false }
        },
        {
          path: 'data-format-check',
          name: 'data-format-check',
          component: () => import('@/views/test/DataFormatCheckView.vue'),
          meta: { requiresAuth: false }
        },
        {
          path: 'accident-api-test',
          name: 'accident-api-test',
          component: () => import('@/views/test/AccidentApiTestView.vue'),
          meta: { requiresAuth: false }
        },
        {
          path: 'data-adapter-test',
          name: 'data-adapter-test',
          component: () => import('@/views/test/DataAdapterTestView.vue'),
          meta: { requiresAuth: false }
        }
      ]
    }
  ]
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // 如果有token但没有用户信息，尝试获取用户信息
  if (authStore.token && !authStore.user) {
    await authStore.initAuth()
  }

  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next('/login')
  } else if (to.meta.requiresAdmin && authStore.user?.grade !== 'admin') {
    next('/dashboard')
  } else if (to.path === '/login' && authStore.isAuthenticated) {
    next('/dashboard')
  } else {
    next()
  }
})

export default router
