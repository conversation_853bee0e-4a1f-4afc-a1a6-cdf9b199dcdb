@echo off
echo ================================================================================
echo 基于Yolov8与ByteTrack的高速公路智慧监控平台 - Conda环境配置
echo Python 3.9.16 + CUDA 11.8 + ByteTrack环境
echo ================================================================================

echo.
echo 检查Conda是否可用...
conda --version >nul 2>&1
if errorlevel 1 (
    echo ✗ Conda未安装或不在PATH中
    echo 请先安装Anaconda或Miniconda:
    echo https://www.anaconda.com/products/distribution
    pause
    exit /b 1
)
echo ✓ Conda已安装

echo.
echo 检查ByteTrack环境是否存在...
conda env list | findstr "ByteTrack" >nul 2>&1
if not errorlevel 1 (
    echo ⚠ ByteTrack环境已存在
    set /p choice="是否删除现有环境并重新创建? (y/n): "
    if /i "%choice%"=="y" (
        echo 删除现有ByteTrack环境...
        conda env remove -n ByteTrack -y
    ) else (
        echo 将在现有环境中更新依赖...
        goto :update_env
    )
)

echo.
echo 创建ByteTrack Conda环境...
echo 这可能需要几分钟时间，请耐心等待...

REM 方法1: 使用environment.yml文件
if exist "environment_bytetrack.yml" (
    echo 使用environment.yml文件创建环境...
    conda env create -f environment_bytetrack.yml
    if errorlevel 1 (
        echo ⚠ 使用yml文件创建失败，尝试手动创建...
        goto :manual_create
    )
    goto :verify_install
)

:manual_create
echo 手动创建环境...

REM 创建基础环境
echo 步骤1: 创建Python 3.9.16环境...
conda create -n ByteTrack python=3.9.16 -y
if errorlevel 1 (
    echo ✗ 环境创建失败
    pause
    exit /b 1
)

REM 激活环境并安装PyTorch
echo 步骤2: 安装PyTorch CUDA版本...
call conda activate ByteTrack
conda install pytorch==2.1.2 torchvision==0.16.2 torchaudio==2.1.2 pytorch-cuda=11.8 -c pytorch -c nvidia -y
if errorlevel 1 (
    echo ✗ PyTorch安装失败
    pause
    exit /b 1
)

REM 安装其他依赖
echo 步骤3: 安装其他依赖包...
pip install numpy==1.24.3 scipy==1.10.1 matplotlib==3.7.2
pip install opencv-python==******** pillow==9.5.0
pip install ultralytics>=8.0.0 supervision>=0.16.0
pip install flask==2.3.3 flask-cors==4.0.0 pymysql==1.1.0
pip install python-dotenv==1.0.0 pandas==2.0.3 psutil==5.9.6
pip install tqdm requests python-dateutil

goto :verify_install

:update_env
echo 更新现有环境...
call conda activate ByteTrack
pip install --upgrade ultralytics supervision flask flask-cors pymysql python-dotenv

:verify_install
echo.
echo ================================================================================
echo 验证安装结果
echo ================================================================================

call conda activate ByteTrack

echo 测试Python版本...
python -c "import sys; print(f'Python版本: {sys.version}')"

echo 测试PyTorch...
python -c "import torch; print(f'PyTorch版本: {torch.__version__}'); print(f'CUDA可用: {torch.cuda.is_available()}')"

echo 测试其他关键包...
python -c "import cv2; print(f'OpenCV版本: {cv2.__version__}')"
python -c "import numpy as np; print(f'NumPy版本: {np.__version__}')"
python -c "from ultralytics import YOLO; print('Ultralytics YOLO导入成功')"
python -c "import flask; print(f'Flask版本: {flask.__version__}')"

echo.
echo ================================================================================
echo 安装完成！
echo ================================================================================

echo.
echo 环境信息:
echo   环境名称: ByteTrack
echo   Python版本: 3.9.16
echo   PyTorch: CUDA 11.8版本
echo.
echo 使用方法:
echo   1. 激活环境: conda activate ByteTrack
echo   2. 测试CUDA: python test_cuda.py
echo   3. 测试数据库: python test_db.py
echo   4. 启动系统: python start_server.py
echo.
echo 快捷激活脚本已创建:
echo   - activate_bytetrack.bat (双击运行)

REM 创建快捷激活脚本
echo @echo off > activate_bytetrack.bat
echo echo 激活ByteTrack Conda环境... >> activate_bytetrack.bat
echo call conda activate ByteTrack >> activate_bytetrack.bat
echo echo 环境已激活！ >> activate_bytetrack.bat
echo echo. >> activate_bytetrack.bat
echo echo 可用命令: >> activate_bytetrack.bat
echo echo   python start_server.py    - 启动服务器 >> activate_bytetrack.bat
echo echo   python test_cuda.py       - 测试CUDA >> activate_bytetrack.bat
echo echo   python test_db.py         - 测试数据库 >> activate_bytetrack.bat
echo echo. >> activate_bytetrack.bat
echo cmd /k >> activate_bytetrack.bat

echo ✓ activate_bytetrack.bat 已创建

echo.
echo 按任意键退出...
pause >nul
