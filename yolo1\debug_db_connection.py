#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试数据库连接问题
"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(override=True, dotenv_path='config/end-back.env')

# 添加backend路径
sys.path.append('backend')

def debug_db_connection():
    """调试数据库连接"""
    print("🔍 调试数据库连接问题")
    print("=" * 50)
    
    try:
        from utils.database import get_db_connection, DatabaseManager
        
        print("✅ 数据库模块导入成功")
        
        # 测试数据库管理器
        print("\n1. 测试数据库管理器...")
        db_manager = DatabaseManager()
        print(f"   配置: {db_manager.config}")
        
        try:
            connection = db_manager.get_connection()
            print("   ✅ 数据库管理器连接成功")
            connection.close()
        except Exception as e:
            print(f"   ❌ 数据库管理器连接失败: {e}")
            return False
        
        # 测试上下文管理器
        print("\n2. 测试上下文管理器...")
        try:
            with get_db_connection() as db:
                print(f"   数据库连接对象类型: {type(db)}")
                print(f"   数据库连接对象: {db}")
                
                if db is None:
                    print("   ❌ 数据库连接对象为None")
                    return False
                
                if not hasattr(db, 'get_one'):
                    print("   ❌ 数据库连接对象没有get_one方法")
                    print(f"   可用方法: {[method for method in dir(db) if not method.startswith('_')]}")
                    return False
                
                print("   ✅ 数据库连接对象正常")
                
                # 测试查询
                print("\n3. 测试查询...")
                result = db.get_one("SELECT COUNT(*) as count FROM user")
                print(f"   查询结果: {result}")
                
                if result:
                    print("   ✅ 查询成功")
                    
                    # 测试用户查询
                    print("\n4. 测试用户查询...")
                    user = db.get_one("SELECT * FROM user WHERE username=%s", ("admin",))
                    print(f"   用户查询结果: {user}")
                    
                    if user:
                        print("   ✅ 用户查询成功")
                        
                        # 测试登录查询
                        print("\n5. 测试登录查询...")
                        login_user = db.get_one(
                            "SELECT * FROM user WHERE username=%s AND password=%s", 
                            ("admin", "123456")
                        )
                        print(f"   登录查询结果: {login_user}")
                        
                        if login_user:
                            print("   ✅ 登录查询成功")
                            return True
                        else:
                            print("   ❌ 登录查询失败")
                            return False
                    else:
                        print("   ❌ 用户查询失败")
                        return False
                else:
                    print("   ❌ 查询失败")
                    return False
                    
        except Exception as e:
            print(f"   ❌ 上下文管理器测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    except Exception as e:
        print(f"❌ 数据库模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 数据库连接问题调试")
    print("=" * 60)
    
    success = debug_db_connection()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 数据库连接调试成功！")
        print("✅ 登录功能应该可以正常工作")
    else:
        print("⚠️ 数据库连接调试失败")
        print("💡 需要修复数据库连接问题")
    print("=" * 60)

if __name__ == "__main__":
    main()
