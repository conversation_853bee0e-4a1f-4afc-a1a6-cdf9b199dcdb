#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GPU检测问题的脚本
比较使用兼容性函数和直接使用supervision库的差异
"""

import torch
import cv2
import numpy as np
from ultralytics import YOLO
import supervision as sv
import time

# 兼容性函数
def create_detections_from_result(result):
    """兼容不同版本supervision库的检测结果创建函数"""
    try:
        return sv.Detections.from_ultralytics(result)
    except AttributeError:
        try:
            return sv.Detections.from_yolov8(result)
        except AttributeError:
            if result.boxes is not None:
                boxes = result.boxes.xyxy.cpu().numpy()
                confidences = result.boxes.conf.cpu().numpy()
                class_ids = result.boxes.cls.cpu().numpy().astype(int)
                track_ids = result.boxes.id.cpu().numpy().astype(int) if result.boxes.id is not None else None

                return sv.Detections(
                    xyxy=boxes,
                    confidence=confidences,
                    class_id=class_ids,
                    tracker_id=track_ids
                )
            else:
                return sv.Detections.empty()

def test_gpu_detection():
    print("=== GPU检测测试 ===")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA版本: {torch.version.cuda}")
        print(f"GPU设备: {torch.cuda.get_device_name(0)}")
    
    # 检查设备选择
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"选择的设备: {device}")
    
    # 加载模型
    print("\n加载YOLO模型...")
    model = YOLO('yolov8n.pt')
    
    # 创建测试图像
    test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
    
    print("\n=== 测试1: 使用兼容性函数 ===")
    try:
        start_time = time.time()
        results = model.track(test_image, device=device, verbose=False)
        result = results[0]
        
        if result.boxes is not None and len(result.boxes) > 0:
            detections = create_detections_from_result(result)
            print(f"检测到 {len(detections)} 个目标")
            print(f"检测结果设备: {result.boxes.xyxy.device}")
        else:
            print("未检测到目标")
        
        end_time = time.time()
        print(f"处理时间: {end_time - start_time:.4f}秒")
        print("兼容性函数测试成功")
    except Exception as e:
        print(f"兼容性函数测试失败: {e}")
    
    print("\n=== 测试2: 直接使用supervision ===")
    try:
        start_time = time.time()
        results = model.track(test_image, device=device, verbose=False)
        result = results[0]
        
        if result.boxes is not None and len(result.boxes) > 0:
            detections = sv.Detections.from_yolov8(result)
            print(f"检测到 {len(detections)} 个目标")
            print(f"检测结果设备: {result.boxes.xyxy.device}")
        else:
            print("未检测到目标")
        
        end_time = time.time()
        print(f"处理时间: {end_time - start_time:.4f}秒")
        print("直接supervision测试成功")
    except Exception as e:
        print(f"直接supervision测试失败: {e}")
    
    print("\n=== 测试3: 检查模型设备 ===")
    try:
        # 检查模型是否在GPU上
        model_device = next(model.model.parameters()).device
        print(f"模型设备: {model_device}")
        
        # 强制将模型移到GPU
        if torch.cuda.is_available():
            model.model = model.model.to('cuda')
            model_device_after = next(model.model.parameters()).device
            print(f"移动后模型设备: {model_device_after}")
    except Exception as e:
        print(f"模型设备检查失败: {e}")

if __name__ == "__main__":
    test_gpu_detection()