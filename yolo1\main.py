# -*- coding: utf-8 -*-
# <AUTHOR> pan
# @Description : python main.py
# @Date : 2023年7月27日10:46:25

# 系统模块
import threading
import os
import sys
import time
import datetime
import uuid
import json
import signal
import subprocess

# 第三方库
import numpy as np
import cv2
import matplotlib.pyplot as plt
from PIL import Image
import supervision as sv



# PySide6 组件
from PySide6.QtCore import Qt, QThread, Signal, QTimer, QUrl
from PySide6.QtGui import QImage, QPixmap, QDesktopServices, QColor, QAction, QCursor
from PySide6.QtWidgets import QMainWindow, QFileDialog, QApplication, QVBoxLayout, QMenu, QLabel
from PySide6.QtUiTools import QUiLoader

# UI 和工具类
from ui.main_window import Ui_MainWindow
from ui.ui_function import UIFuncitons
from ui.toast.toast import DialogOver
from ui.pop.pop_box import MessageBox
from utils.main_utils import check_path
from utils.convert import plot_one_box, ResizePicture
from utils.AtestCamera import Camera

# 定义check_url函数，检查URL中是否包含中文字符
def check_url(url):
    """
    检查URL中是否包含中文字符
    返回True表示包含中文，False表示不包含中文
    """
    for char in url:
        if '\u4e00' <= char <= '\u9fff':
            return True
    return False

# 应用类
from classes.yolo import YoloPredictor
from classes.video_writer import VideoWriter
from classes.target_tracker import TargetTracker
from classes.violation_detector import ViolationDetector
from classes.car_chart import WorkerThread
from classes.fps import FPS
from classes.camera_loader import CameraLoader
from classes.main_config import MainConfig
from classes.tech_tracker import TargetTracker

# 对话框
from ui.dialog.rtsp_win import RTSPDialog
from ui.dialog.tech_tracking_win import TechTrackingDialog
from ui.dialog.id_win import id_Window
from ui.dialog.violation_detection_dialog import ViolationDetectionWindow
from ui.dialog.pedestrian_detection_dialog import PedestrianDetectionWindow

# 样式应用
import apply_style

class MainWindow(QMainWindow, Ui_MainWindow):

    # 主窗口向yolo实例发送执行信号
    main2yolo_begin_sgl = Signal()

    def __init__(self, parent=None):
        super(MainWindow, self).__init__()

        self.setupUi(self)
        # 背景设置为半透明 & 无边框窗口
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setWindowFlags(Qt.FramelessWindowHint)

        # 立即移除导航栏按钮上的阴影 - 在UI动作之前应用
        apply_style.remove_shadow_from_leftmenu(self)

        # UI动作（动效）
        UIFuncitons.uiDefinitions(self)

        # 变量设置
        self.car_threshold = 0 # 车辆阈值
        self.image_id = 0 # 图片 id
        self.txt_id = 0 # 标签 id

        # 热力图设置
        self.show_heatmap = False # 是否显示热力图
        self.show_accumulated_heatmap = False # 是否显示累积热力图

        # 流量图设置
        self.traffic_graph_mode = "both"  # 默认显示双模式图表

        # 添加累计流量显示标签
        self.total_traffic_label = QLabel(self.below)
        self.total_traffic_label.setObjectName("total_traffic_label")
        self.total_traffic_label.setStyleSheet("color: blue; font-weight: bold;")
        self.total_traffic_label.setText("累计车流量: 0")
        # 将标签添加到底部状态栏布局中
        self.horizontalLayout_13.addWidget(self.total_traffic_label)

        # 设置阴影
        UIFuncitons.shadow_style(self, self.Class_QF, QColor(149, 117, 205))
        UIFuncitons.shadow_style(self, self.Target_QF, QColor(255, 145, 124))
        UIFuncitons.shadow_style(self, self.Fps_QF, QColor(147, 129, 255))
        UIFuncitons.shadow_style(self, self.Model_QF, QColor(79, 195, 189))

        # 设置 为 -- （好看）
        self.Class_num.setText('--')
        self.Target_num.setText('--')
        self.fps_label.setText('--')

        # 计时器：每2秒监视模型文件更改一次
        self.Qtimer_ModelBox = QTimer(self)
        self.Qtimer_ModelBox.timeout.connect(self.ModelBoxRefre)
        self.Qtimer_ModelBox.start(2000)


        self.yolo_init()  # 实例化YOLO
        self.model_bind() # 预测参数-数值变动绑定 主页面
        # 主要功能绑定       @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
        self.main_function_bind()

        self.load_config() # 配置加载
        self.model_load()  # 模型加载

        # 画图线程
        self.is_draw_thread = False
        self.draw_thread = WorkerThread()

        self.show_status('欢迎使用基于Yolov8与ByteTrack的高速公路智慧监控平台!')

        # 再次移除导航栏按钮上的阴影，确保它们没有阴影
        apply_style.remove_shadow_from_leftmenu(self)

        # 设置窗口标题
        apply_style.update_title(self)

    # 实例化YOLO
    def yolo_init(self):
        # Yolo-v8 thread
        self.yolo_predict = YoloPredictor()                           #实例化yolo检测
        self.select_model = self.model_box.currentText()
        self.yolo_thread = QThread()

        # 将主窗口引用传递给YOLO预测器
        self.yolo_predict.set_main_window(self)

        # 显示预测视频（左，右）
        self.yolo_predict.yolo2main_trail_img.connect(lambda x: self.show_image(x, self.pre_video))
        self.yolo_predict.yolo2main_box_img.connect(lambda x: self.show_image(x, self.res_video))

        # 输出信息、FPS、类数、总数
        self.yolo_predict.yolo2main_status_msg.connect(lambda x: self.show_status(x))
        self.yolo_predict.yolo2main_fps.connect(lambda x: self.fps_label.setText(x))
        self.yolo_predict.yolo2main_class_num.connect(lambda x:self.Class_num.setText(str(x)))
        self.yolo_predict.yolo2main_progress.connect(lambda x: self.progress_bar.setValue(x))

        # 移动线程里面去（通过main2yolo_begin_sgl信号控制-先要开启yolo_thread线程，才能启动yolo_predict的run方法）
        self.yolo_predict.moveToThread(self.yolo_thread)
        self.main2yolo_begin_sgl.connect(self.yolo_predict.run)

        # 显示总车流量
        self.yolo_predict.yolo2main_target_num.connect(lambda x:self.Target_setText(x))

    # 模型加载
    def model_load(self):
        # 创建模型文件夹
        check_path(self.config.models_path)
        self.model_box.clear()
        self.pt_list = os.listdir(f'./{self.config.models_path}')
        self.pt_list = [file for file in self.pt_list if file.endswith('.pt') or file.endswith('.engine')]
        self.pt_list.sort(key=lambda x: os.path.getsize(f'./{self.config.models_path}/' + x))   #按文件大小排序
        self.model_box.clear()
        self.model_box.addItems(self.pt_list)

        # 这些按钮的样式将在main_function_bind()中统一设置

    # 主页面各功能绑定
    def main_function_bind(self):
        # 添加左侧菜单切换连接
        self.ToggleBotton.clicked.connect(lambda: UIFuncitons.toggleMenu(self, True))
        self.settings_button.clicked.connect(lambda: UIFuncitons.settingBox(self, True))

        # 设置导航栏按钮样式 - 使用规则的阴影
        nav_button_base_style = """
            QPushButton {
                background-repeat: no-repeat;
                background-position: left center;
                border: none;
                text-align: center;
                padding-left: 0px;
                color: rgba(255, 255, 255, 199);
                font: 700 12pt "Nirmala UI";
                border-radius: 5px;
                margin: 3px 8px;
                background-color: rgba(%s, 100);
                box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            }

            QPushButton:hover {
                background-color: rgba(%s, 150);
                box-shadow: 3px 3px 6px rgba(0, 0, 0, 0.4);
            }
        """

        # 本地文件按钮
        self.src_file_button.clicked.connect(self.open_src_file)
        self.src_file_button.setText("本地文件")
        self.src_file_button.setStyleSheet(
            nav_button_base_style % ("41, 128, 185", "41, 128, 185")
        )

        # 移除摄像头按钮的连接和文本设置

        # RTSP按钮
        self.src_rtsp_button.clicked.connect(self.rtsp_seletction)
        self.src_rtsp_button.setText("调用RTSP监控")
        self.src_rtsp_button.setStyleSheet(
            nav_button_base_style % ("52, 152, 219", "52, 152, 219")
        )

        # 流量图按钮
        self.src_graph_button.setText("实时流量分析")
        self.src_graph_button.setStyleSheet(
            nav_button_base_style % ("60, 160, 230", "60, 160, 230")
        )
        self.src_graph_button.clicked.connect(self.show_traffic_graph)

        # 单目标追踪按钮
        self.tracking_mode = "single"  # 默认为单目标追踪模式
        self.src_lock_button.clicked.connect(self.lock_id_selection)
        self.src_lock_button.setText("单目标跟踪")
        self.src_lock_button.setStyleSheet(
            nav_button_base_style % ("155, 89, 182", "155, 89, 182")
        )

        # 热力图按钮（改为拥塞情况）
        self.src_web_button.setText("实时拥塞情况")
        self.src_web_button.setStyleSheet(
            nav_button_base_style % ("243, 156, 18", "243, 156, 18")
        )
        self.src_web_button.clicked.connect(self.toggle_heatmap)

        # 违规检测按钮
        self.src_violation_button.clicked.connect(self.show_violation_detection)
        self.src_violation_button.setText("违规行为检测")
        self.src_violation_button.setStyleSheet(
            nav_button_base_style % ("231, 76, 60", "231, 76, 60")
        )

        # 行人检测按钮
        self.src_pedestrian_button.clicked.connect(self.show_pedestrian_detection)
        self.src_pedestrian_button.setText("行人闯入检测")
        self.src_pedestrian_button.setStyleSheet(
            nav_button_base_style % ("41, 128, 255", "41, 128, 255")
        )

        self.run_button.clicked.connect(self.run_or_continue) # 开始
        self.stop_button.clicked.connect(self.stop) # 终止

        # 连接多目标追踪按钮
        self.src_multi_tracking_button.clicked.connect(self.enable_multi_tracking)
        self.src_multi_tracking_button.setText("多目标智能跟踪")
        self.src_multi_tracking_button.setStyleSheet(
            nav_button_base_style % ("52, 152, 219", "52, 152, 219")
        )

        # 修改为道路事故监测按钮
        self.src_collision_detection_button.clicked.connect(self.enable_collision_detection)
        self.src_collision_detection_button.setText("道路事故监测")
        self.src_collision_detection_button.setStyleSheet(
            nav_button_base_style % ("231, 76, 60", "231, 76, 60")
        )

        self.save_res_button.toggled.connect(self.is_save_res) # 是否 保存 视频
        self.save_txt_button.toggled.connect(self.is_save_txt) # 是否 保存 标签
        self.show_labels_checkbox.toggled.connect(self.is_show_labels)  # 是否 显示标签
        self.show_trace_checkbox.toggled.connect(self.is_show_trace)  # 是否 显示轨迹

        # 新增热力图相关参数绑定
        self.yolo_predict.show_heatmap = self.show_heatmap
        self.yolo_predict.show_accumulated_heatmap = self.show_accumulated_heatmap

    # 模型参数绑定
    def model_bind(self):
        self.model_box.currentTextChanged.connect(self.change_model)
        self.iou_spinbox.valueChanged.connect(lambda x:self.change_val(x, 'iou_spinbox'))    # iou box
        self.iou_slider.valueChanged.connect(lambda x:self.change_val(x, 'iou_slider'))      # iou scroll bar

        self.conf_spinbox.valueChanged.connect(lambda x:self.change_val(x, 'conf_spinbox'))  # conf box
        self.conf_slider.valueChanged.connect(lambda x:self.change_val(x, 'conf_slider'))    # conf scroll bar

        self.speed_spinbox.valueChanged.connect(lambda x:self.change_val(x, 'speed_spinbox'))# speed box
        self.speed_slider.valueChanged.connect(lambda x:self.change_val(x, 'speed_slider'))  # speed scroll bar

        self.speed_sss.valueChanged.connect(lambda x: self.change_val(x, 'speed_sss'))  # speed box
        self.speed_nnn.valueChanged.connect(lambda x:self.change_val(x, 'speed_nnn'))  # speed scroll bar

    # JSON配置文件初始化
    def load_config(self):
        self.config = MainConfig("./config/config.json")

        self.save_res_button.setChecked(self.config.save_res)
        self.save_txt_button.setChecked(self.config.save_txt)

        self.iou_slider.setValue(self.config.iou * 100)
        self.conf_slider.setValue(self.config.conf * 100)
        self.speed_slider.setValue(self.config.rate)
        self.speed_sss.setValue(self.config.car_threshold)

        self.yolo_predict.save_txt = self.config.save_txt # 保存标签
        self.yolo_predict.save_res = self.config.save_res # 保存结果
        self.yolo_predict.save_txt_path = self.config.save_txt_path
        self.yolo_predict.save_res_path = self.config.save_res_path
        self.yolo_predict.new_model_name = f"./{self.config.models_path}/%s" % self.select_model

        self.yolo_predict.show_trace = self.config.show_trace  # 轨迹
        self.show_trace_checkbox.setChecked(self.config.show_trace) # 轨迹
        self.yolo_predict.show_labels = self.config.show_labels  # 标签
        self.show_labels_checkbox.setChecked(self.config.show_labels) # 标签

        # 载入热力图配置
        self.show_heatmap = False  # 强制设置为关闭状态
        self.show_accumulated_heatmap = False  # 强制设置为关闭状态
        self.yolo_predict.show_heatmap = False
        self.yolo_predict.show_accumulated_heatmap = False
        # 确保拥塞情况按钮显示正确文本
        self.src_web_button.setText("实时拥塞情况")

        self.open_fold = self.config.open_fold
        self.rtsp_ip = self.config.rtsp_ip
        self.car_id = self.config.car_id
        self.car_threshold = self.config.car_threshold

        self.run_button.setChecked(False)

    # 退出时退出线程，保存设置
    def closeEvent(self, event):
        try:
            self.stop()
            # 检测画图线程
            self.draw_thread.close_exec()
            # self.draw_thread.deleteLater()

            # config.json
            self.config.save_res = self.yolo_predict.save_res
            self.config.save_txt = self.yolo_predict.save_txt

            self.config.show_labels = self.yolo_predict.show_labels
            self.config.show_trace = self.yolo_predict.show_trace

            self.config.iou = self.yolo_predict.iou_thres
            self.config.conf = self.yolo_predict.conf_thres
            self.config.rate = self.yolo_predict.speed_thres

            # 保存热力图设置
            self.config.show_heatmap = self.show_heatmap
            self.config.show_accumulated_heatmap = self.show_accumulated_heatmap

            self.config.car_threshold = self.car_threshold   # 车辆数预测警报
            self.config.rtsp_ip = self.rtsp_ip
            self.config.car_id = self.car_id
            self.config.open_fold = self.open_fold

            self.config.save_config()  # 保存配置

            # 退出时延迟 2s
            MessageBox(
                self.close_button, title='Note', text='退出中，请等待...', time=2000, auto=True).exec()

            sys.exit(0)
        except Exception as e:
            print(e)
            sys.exit(0)

    # 如果超出了车流阈值，那么就变红！！
    def Target_setText(self, num):
        num = str(num)
        self.Target_num.setText(num)
        self.char_label.setText(f"当前车流量: {num}")
        if (int(num) > int(self.car_threshold)):
            self.char_label.setStyleSheet("color: red;")
        else:
            self.char_label.setStyleSheet("color: green;")


    #主窗口显示轨迹图像和检测图像 （缩放在这里）
    @staticmethod
    def show_image(img_src, label):
        try:
            # 检查图像的通道数，确定图像是否为彩色图像
            if len(img_src.shape) == 3:
                ih, iw, _ = img_src.shape
            if len(img_src.shape) == 2:
                ih, iw = img_src.shape

            # 根据标签窗口的大小调整图像的大小
            w = label.geometry().width()
            h = label.geometry().height()

            # 根据图像宽高比例进行缩放
            if iw / w > ih / h:
                scal = w / iw
                nw = w
                nh = int(scal * ih)
                img_src_ = cv2.resize(img_src, (nw, nh))

            else:
                scal = h / ih
                nw = int(scal * iw)
                nh = h
                img_src_ = cv2.resize(img_src, (nw, nh))

            # 将OpenCV图像从BGR格式转换为RGB格式，并创建QImage对象
            frame = cv2.cvtColor(img_src_, cv2.COLOR_BGR2RGB)
            img = QImage(frame.data, frame.shape[1], frame.shape[0], frame.shape[2] * frame.shape[1],
                         QImage.Format_RGB888)

            # 在标签窗口中显示图像
            label.setPixmap(QPixmap.fromImage(img))

        except Exception as e:
            print(repr(e))

    #控制开始|暂停
    def run_or_continue(self):
        # 检测是否有模型
        if self.yolo_predict.new_model_name == '' or self.yolo_predict.new_model_name == None:
            DialogOver(parent=self, text="请检测模型", title="运行失败", flags="danger")
            self.run_button.setChecked(False)
            return
        # 检测输入源
        if self.yolo_predict.source == '' or self.yolo_predict.source == None:
            self.show_status('请在检测前选择输入源...')
            self.run_button.setChecked(False)
            DialogOver(parent=self, text="请检测输入源", title="运行失败", flags="danger")
            return

        self.yolo_predict.stop_dtc = False # 线程开始

        # 开始
        if self.run_button.isChecked():

            # 图片预测
            file_extension = self.yolo_predict.source[-3:].lower()
            if file_extension == 'png' or file_extension == 'jpg':
                self.img_predict()
                return

            # 视频预测
            DialogOver(parent=self, text="开始检测...", title="运行成功", flags="success")
            self.run_button.setChecked(True)

            self.draw_thread.run_continue()  # 折线图开始

            # 不可再改变设置（config动态调整 关闭）
            self.save_txt_button.setEnabled(False)
            self.save_res_button.setEnabled(False)
            self.conf_slider.setEnabled(False)
            self.iou_slider.setEnabled(False)
            self.speed_slider.setEnabled(False)

            self.show_status('检测中...')
            if '0' in self.yolo_predict.source or 'rtsp' in self.yolo_predict.source:
                self.progress_bar.setFormat('实时视频流检测中...')
            if 'avi' in self.yolo_predict.source or 'mp4' in self.yolo_predict.source:
                self.progress_bar.setFormat("当前检测进度:%p%")
            self.yolo_predict.continue_dtc = True
            # 开始检测
            if not self.yolo_thread.isRunning():
                self.yolo_thread.start()
                self.main2yolo_begin_sgl.emit()
        # 暂停
        else:
            self.draw_thread.pause()  # 折线图暂停
            self.yolo_predict.continue_dtc = False
            self.show_status("暂停...")
            DialogOver(parent=self, text="已暂停检测", title="运行暂停", flags="warning")
            self.run_button.setChecked(False)

    # 显示状态 （底部栏
    def show_status(self, msg):
        self.status_bar.setText(msg)  # 显示输出
        if msg == '检测完成':
            self.save_res_button.setEnabled(True)
            self.save_txt_button.setEnabled(True)
            self.run_button.setChecked(False)
            self.progress_bar.setValue(0)
            # 终止yolo线程
            if self.yolo_thread.isRunning():
                self.yolo_thread.quit()
            # 修改画图线程状态
            self.draw_thread.stop()
            self.is_draw_thread = False


        elif msg == '检测终止':
            self.save_res_button.setEnabled(True)
            self.save_txt_button.setEnabled(True)
            self.run_button.setChecked(False)
            self.progress_bar.setValue(0)
            # 终止yolo线程
            if self.yolo_thread.isRunning():
                self.yolo_thread.quit()
            # 修改画图线程状态
            self.draw_thread.stop()
            self.is_draw_thread = False

            self.pre_video.clear()
            self.res_video.clear()
            self.Class_num.setText('--')
            self.Target_num.setText('--')
            self.fps_label.setText('--')

    # 打开文件
    def open_src_file(self):
        # 支持视频格式
        file_str = "All Files (*);;Videos (*.mp4 *.mkv *.avi *.flv);;Images (*.jpg *.jpeg *.png)"
        # 清华镜像该种方式可能会有问题：https://blog.csdn.net/qq_41206517/article/details/122697485
        name, _ = QFileDialog.getOpenFileName(self, "选择文件", self.open_fold, file_str)
        if name:
            self.open_fold = os.path.dirname(name)
            # 终止事件
            self.stop() # 关闭YOLO线程

            DialogOver(parent=self, text="文件路径: {}".format(name), title="加载成功", flags="success")

            # 直接执行运行
            self.yolo_predict.source = name
            self.yolo_predict.stop_dtc = False
            # 开启YOLO时会发信号，主线程接收到信号执行界面更新
            self.run_or_continue()

            return True

    # camera选择
    def camera_select(self):
        #try:
            # 关闭YOLO线程
            self.stop()
            #获取本地摄像头数量
            _, cams = Camera().get_cam_num()
            popMenu = QMenu()
            popMenu.setFixedWidth(self.src_cam_button.width())
            popMenu.setStyleSheet('''
                                            QMenu {
                                            font-size: 20px;
                                            font-family: "Microsoft YaHei UI";
                                            font-weight: light;
                                            color:white;
                                            padding-left: 5px;
                                            padding-right: 5px;
                                            padding-top: 4px;
                                            padding-bottom: 4px;
                                            border-style: solid;
                                            border-width: 0px;
                                            border-color: rgba(255, 212, 255, 255);
                                            border-radius: 3px;
                                            background-color: rgba(16,155,226,50);
                                            }
                                            ''')

            for cam in cams:
                exec("action_%s = QAction('%s 号摄像头')" % (cam, cam))
                exec("popMenu.addAction(action_%s)" % cam)
            pos = QCursor.pos()
            action = popMenu.exec(pos)

            # 设置摄像头来源
            if action:
                str_temp = ''
                selected_stream_source = str_temp.join(filter(str.isdigit, action.text())) #获取摄像头号，去除非数字字符
                self.yolo_predict.source = selected_stream_source
                self.show_status(f'摄像头设备:{action.text()}')
                DialogOver(parent=self, text=f"当前摄像头为: {action.text()}", title="摄像头选择成功", flags="success")

    # 1、选择rtsp
    def rtsp_seletction(self):
        self.rtsp_window = RTSPDialog()
        self.rtsp_window.rtspEdit.setText(self.rtsp_ip)
        self.rtsp_window.show()
        # 如果点击则加载RTSP
        self.rtsp_window.rtspButton.clicked.connect(lambda: self.load_rtsp(self.rtsp_window.rtspEdit.text()))
        # 连接id_confirmed信号，确保使用用户通过确认按钮提交的完整RTSP URL
        self.rtsp_window.id_confirmed.connect(self.load_confirmed_rtsp)

    # 加载用户确认的完整RTSP URL
    def load_confirmed_rtsp(self, rtsp_url):
        """加载用户从对话框确认的完整RTSP URL"""
        print(f"加载用户确认的RTSP URL: {rtsp_url}")
        try:
            MessageBox(self.close_button, title='提示', text='加载 rtsp...', time=1000, auto=True).exec()
            self.stop() # 关闭YOLO线程

            # 保存RTSP地址
            self.yolo_predict.source = rtsp_url
            self.rtsp_ip = rtsp_url # 保存完整URL

            # 状态显示
            self.show_status(f'摄像头连接成功: {rtsp_url}')
            DialogOver(parent=self, text="摄像头连接成功，开始进行检测", title="连接成功", flags="success")

            # 立即启动预测
            self.run_or_continue()

            return True
        except Exception as e:
            DialogOver(parent=self, text=f"RTSP加载失败: {str(e)}", title="错误", flags="danger")
            print(f"RTSP加载错误: {str(e)}")

    # 2、加载RTSP
    def load_rtsp(self, ip=None):
        try:
            # 显示提示信息
            MessageBox(self.close_button, title='提示', text='正在连接摄像头...', time=1000, auto=True).exec()

            # 停止当前线程
            self.stop()

            # 直接使用预定义的完整RTSP URL，包含认证信息和路径
            # 尝试使用最常见的组合
            # 如果用户提供了IP，则使用用户的IP，否则使用默认值
            user_ip = ip.split(':')[0] if ip and ':' in ip else ip  # 提取用户指定的IP部分
            user_port = "8080"  # 默认端口

            if ip and ':' in ip:
                parts = ip.split(':')
                user_ip = parts[0]
                if len(parts) > 1:
                    user_port = parts[1]

            # 如果用户提供了完整的URL，则直接使用
            if ip and ip.startswith(('rtsp://', 'http://', 'https://')):
                rtsp_urls = [ip]  # 直接使用用户提供的完整URL
                print(f"最新RTSP URL: {ip}")
            else:
                # 否则根据用户提供的IP构建URL列表
                rtsp_urls = [
                    f"rtsp://admin:admin@{user_ip}:{user_port}/h264",
                    f"rtsp://admin:admin@{user_ip}:{user_port}/h264_ulaw.sdp",
                    f"rtsp://admin:admin@{user_ip}:{user_port}/h264_pcm.sdp",
                    f"rtsp://admin:123456@{user_ip}:{user_port}/h264",
                    f"rtsp://{user_ip}:{user_port}/h264",
                ]

            # 尝试所有可能的URL
            success = False
            working_url = ""

            import cv2
            for url in rtsp_urls:
                try:
                    print(f"尝试连接: {url}")
                    cap = cv2.VideoCapture(url)
                    ret, frame = cap.read()
                    cap.release()

                    if ret:
                        working_url = url
                        success = True
                        print(f"连接成功: {url}")
                        break
                except Exception as e:
                    print(f"连接失败 {url}: {str(e)}")
                    continue

            # 如果找到了有效的URL
            if success:
                # 记录RTSP地址
                self.yolo_predict.source = working_url
                self.rtsp_ip = working_url

                # 关闭对话框
                if hasattr(self, 'rtsp_window') and self.rtsp_window:
                    self.rtsp_window.close()

                # 状态显示
                self.show_status(f'摄像头连接成功: {working_url}')
                DialogOver(parent=self, text="摄像头连接成功，开始进行检测", title="连接成功", flags="success")

                # 立即启动预测
                self.run_or_continue()

                return True
            else:
                # 所有尝试都失败
                DialogOver(parent=self, text="无法连接到摄像头，请确认IP地址、用户名和密码是否正确", title="连接失败", flags="danger")
                return False

        except Exception as e:
            DialogOver(parent=self, text=f"RTSP连接失败: {str(e)}", title="错误", flags="danger")
            print(f"RTSP连接错误: {str(e)}")
            return False

    # 切换单/多目标跟踪模式
    def toggle_tracking_mode(self):
        # 没有开始检测
        if not self.run_button.isChecked():
            DialogOver(parent=self, text="请先开始目标检测！", title="开启失败", flags="danger")
            return

        if self.tracking_mode == "single":
            # 切换到多目标跟踪模式
            self.tracking_mode = "multi"
            self.src_lock_button.setText("多目标跟踪")
            # 更新按钮样式以反映多目标模式
            self.src_lock_button.setStyleSheet(u"QPushButton{\n"
                                              "background-image: url(./ui/img/track.png);\n"
                                              "background-repeat: no-repeat;\n"
                                              "background-position: left center;\n"
                                              "border: none;\n"
                                              "border-left: 23px solid rgba(155, 89, 182, 199);\n"
                                              "text-align: center;\n"
                                              "padding-left: 0px;\n"
                                              "color: rgba(255, 255, 255, 199);\n"
                                              "font: 700 12pt \"Nirmala UI\";\n"
                                              "background-color: rgba(155, 89, 182, 100);\n"
                                              "}\n"
                                              "\n"
                                              "QPushButton:hover{\n"
                                              "background-color: rgba(155, 89, 182, 150);\n"
                                              "}")

            # 启用多目标跟踪功能
            self.yolo_predict.lock_id = None  # 清除单一ID锁定
            self.yolo_predict.multi_tracking = False  # 启用多目标跟踪

            # 显示成功消息
            DialogOver(parent=self, text="已切换至多目标跟踪模式", title="跟踪模式", flags="success")
            self.show_status('已启用多目标跟踪')

        else:
            # 切换到单目标跟踪模式
            self.tracking_mode = "single"
            # 打开单目标ID选择对话框
            self.lock_id_selection()

            # 禁用多目标跟踪
            self.yolo_predict.multi_tracking = False

            # 显示消息
            DialogOver(parent=self, text="已切换至单目标跟踪模式", title="跟踪模式", flags="success")

    # 多目标追踪按钮点击事件 - 直接打开对话框
    def enable_multi_tracking(self):
        # 没有开始检测时提示需要先开始检测
        if not self.run_button.isChecked():
            DialogOver(parent=self, text="请先开始目标检测！", title="开启失败", flags="danger")
            return

        # 如果多目标跟踪对话框已经存在，就显示它而不是创建新的
        if hasattr(self, 'multi_tracking_dialog') and self.multi_tracking_dialog:
            self.multi_tracking_dialog.show()
            return

        # 创建优化版多目标跟踪对话框
        from ui.dialog.optimized_multi_tracking_dialog import OptimizedMultiTrackingDialog
        self.multi_tracking_dialog = OptimizedMultiTrackingDialog(self)

        # 连接信号
        self.multi_tracking_dialog.tracking_started.connect(self.on_multi_tracking_started)
        self.multi_tracking_dialog.tracking_stopped.connect(self.on_multi_tracking_stopped)
        self.multi_tracking_dialog.target_added.connect(self.on_target_added)
        self.multi_tracking_dialog.target_removed.connect(self.on_target_removed)

        # 加载检测到的所有目标到对话框的可用目标列表
        if hasattr(self, 'yolo_predict') and self.yolo_predict:
            detected_targets = []
            # 获取当前检测到的所有车辆目标
            if hasattr(self.yolo_predict, 'detected_objects'):
                for obj_id, obj_info in self.yolo_predict.detected_objects.items():
                    if 'class' in obj_info and '车' in obj_info['class']:
                        detected_targets.append({
                            'id': obj_id,
                            'class': obj_info['class']
                        })

            # 更新对话框中的可用目标列表
            self.multi_tracking_dialog.update_available_targets(detected_targets)

        # 显示对话框
        self.multi_tracking_dialog.show()

    # 当开始多目标跟踪时的回调函数
    def on_multi_tracking_started(self, target_ids):
        # 设置YOLO预测器的多目标跟踪模式
        self.yolo_predict.multi_tracking = False
        self.yolo_predict.multi_tracking_ids = target_ids

        # 更新UI状态
        self.tracking_mode = "multi"

    def update_multi_tracking_preview(self, target_id, target_image):
        """更新多目标追踪预览图像"""
        try:
            if (hasattr(self, 'multi_tracking_dialog') and
                self.multi_tracking_dialog and
                self.multi_tracking_dialog.isVisible()):

                # 调用对话框的图像更新方法
                self.multi_tracking_dialog.update_target_image(target_id, target_image)

        except Exception as e:
            print(f"更新多目标追踪预览时出错: {str(e)}")

    def get_available_targets(self):
        """获取当前可用的目标列表"""
        try:
            if hasattr(self, 'yolo_predict') and hasattr(self.yolo_predict, 'detected_objects'):
                targets = []
                for obj_id, obj_info in self.yolo_predict.detected_objects.items():
                    targets.append({
                        'id': obj_id,
                        'class': obj_info.get('class_zh', obj_info.get('class', '车辆')),
                        'confidence': int(obj_info.get('confidence', 0.0) * 100)
                    })
                return targets
            return []
        except Exception as e:
            print(f"获取可用目标时出错: {str(e)}")
            return []

    # 当停止多目标跟踪时的回调函数
    def on_multi_tracking_stopped(self):
        # 重置YOLO预测器的多目标跟踪设置
        self.yolo_predict.multi_tracking = False
        self.yolo_predict.multi_tracking_ids = []

        # 显示状态信息
        self.show_status('已停止多目标跟踪')
        print("停止多目标跟踪")

    # 当添加跟踪目标时的回调函数
    def on_target_added(self, target_id):
        # 如果正在跟踪中，将新目标添加到跟踪列表
        if hasattr(self, 'yolo_predict') and self.yolo_predict.multi_tracking:
            if not hasattr(self.yolo_predict, 'multi_tracking_ids'):
                self.yolo_predict.multi_tracking_ids = []

            if target_id not in self.yolo_predict.multi_tracking_ids:
                self.yolo_predict.multi_tracking_ids.append(target_id)
                print(f"添加跟踪目标ID: {target_id}")

    # 当移除跟踪目标时的回调函数
    def on_target_removed(self, target_id):
        # 如果正在跟踪中，从跟踪列表中移除目标
        if hasattr(self, 'yolo_predict') and hasattr(self.yolo_predict, 'multi_tracking_ids'):
            if target_id in self.yolo_predict.multi_tracking_ids:
                self.yolo_predict.multi_tracking_ids.remove(target_id)
                print(f"移除跟踪目标ID: {target_id}")

    # 注意: update_multi_tracking_preview方法已在上面定义

    # 注意: 多目标跟踪的实际实现在前面已定义的 on_multi_tracking_started(self, target_ids) 和 on_multi_tracking_stopped(self) 方法中

    # 车辆碰撞检测功能
    def enable_collision_detection(self):
        """启用车辆碰撞检测"""
        print("🔍 碰撞检测按钮被点击")
        
        # 检查是否已经开始检测
        if not self.run_button.isChecked():
            print("⚠️ 目标检测未开始，显示警告对话框")
            DialogOver(parent=self, text="请先开始目标检测！", title="开启失败", flags="danger")
            return

        # 如果碰撞检测对话框已经存在，就显示它而不是创建新的
        if hasattr(self, 'collision_detection_dialog') and self.collision_detection_dialog:
            print("📱 碰撞检测对话框已存在，直接显示")
            self.collision_detection_dialog.show()
            return

        try:
            print("🚀 开始创建碰撞检测对话框")
            # 创建碰撞检测对话框
            from ui.dialog.collision_detection_dialog import CollisionDetectionDialog
            print("✅ 碰撞检测对话框类导入成功")
            
            self.collision_detection_dialog = CollisionDetectionDialog(self)
            print("✅ 碰撞检测对话框实例创建成功")

            # 连接信号
            self.collision_detection_dialog.detection_started.connect(self.on_collision_detection_started)
            self.collision_detection_dialog.detection_stopped.connect(self.on_collision_detection_stopped)
            self.collision_detection_dialog.collision_detected.connect(self.on_collision_detected)
            print("✅ 信号连接成功")

            # 显示对话框
            self.collision_detection_dialog.show()
            print("✅ 碰撞检测对话框显示成功")
            
        except Exception as e:
            print(f"❌ 创建碰撞检测对话框失败: {e}")
            import traceback
            traceback.print_exc()
            DialogOver(parent=self, text=f"创建对话框失败: {str(e)}", title="错误", flags="danger")

    def on_collision_detection_started(self):
        """碰撞检测开始时的回调"""
        self.show_status('已启用车辆碰撞检测')
        print("开始车辆碰撞检测")

    def on_collision_detection_stopped(self):
        """碰撞检测停止时的回调"""
        self.show_status('已停止车辆碰撞检测')
        print("停止车辆碰撞检测")

    def on_collision_detected(self, collision_info):
        """检测到碰撞时的回调"""
        # 显示碰撞警报
        collision_msg = f"检测到车辆碰撞！\n车辆 {collision_info['id1']} 与 {collision_info['id2']}\n严重程度: {collision_info['severity']}"
        DialogOver(parent=self, text=collision_msg, title="碰撞警报", flags="danger")

        # 更新状态栏
        self.show_status(f'碰撞警报: 车辆{collision_info["id1"]}与{collision_info["id2"]}发生碰撞')
        print(f"碰撞检测: {collision_msg}")

    def update_collision_detection(self, tracked_objects):
        """更新碰撞检测数据"""
        try:
            if (hasattr(self, 'collision_detection_dialog') and
                self.collision_detection_dialog and
                self.collision_detection_dialog.isVisible() and
                self.collision_detection_dialog.is_detecting):

                # 将追踪对象数据传递给碰撞检测算法
                self.collision_detection_dialog.process_frame_data(tracked_objects)

        except Exception as e:
            print(f"更新碰撞检测时出错: {str(e)}")

    # 1、设置 单目标
    def lock_id_selection(self):
        # 初始化单目标追踪器（如果尚未初始化）
        if not hasattr(self, 'target_tracker'):
            self.target_tracker = TargetTracker()
            # 连接信号
            self.target_tracker.target_image_signal.connect(self.update_tracking_preview)
            self.target_tracker.status_signal.connect(self.update_tracking_status)
            self.target_tracker.tracking_lost_signal.connect(self.on_tracking_lost)

        # 清空之前的ID
        self.yolo_predict.lock_id = None

        # 设置回调函数，将YOLO检测到的目标帧传递给TargetTracker
        if hasattr(self.target_tracker, 'process_frame'):
            self.yolo_predict.target_tracker_callback = self.target_tracker.process_frame

        # 显示高级追踪窗口
        self.tech_tracking_window = TechTrackingDialog()
        self.tech_tracking_window.idEdit.setText(str(self.car_id))
        self.tech_tracking_window.show()
        self.tech_tracking_window.id_confirmed.connect(self.set_lock_id)

    # 2、设置 单目标 ID
    def set_lock_id(self, lock_id):
        if lock_id:
            try:
                # 确保lock_id是整数
                lock_id = int(lock_id)

                # 设置 YOLO 的锁定 ID
                self.yolo_predict.lock_id = lock_id
                self.car_id = lock_id  # 写回lock_id

                # 设置高级追踪器的目标 ID
                self.target_tracker.set_target_id(lock_id)

                # 显示状态信息
                self.show_status('开始追踪 ID:{}'.format(lock_id))
            except Exception as e:
                print(f"Error setting lock ID: {e}")
                self.show_status(f'设置 ID 失败: {str(e)}')

    # 更新追踪预览
    def update_tracking_preview(self, pixmap):
        if hasattr(self, 'tech_tracking_window') and self.tech_tracking_window.isVisible():
            self.tech_tracking_window.update_target_preview(pixmap)

    # 更新追踪状态
    def update_tracking_status(self, message, color="green"):
        if hasattr(self, 'tech_tracking_window') and self.tech_tracking_window.isVisible():
            self.tech_tracking_window.update_status(message, color)

    # 处理追踪丢失
    def on_tracking_lost(self):
        # 显示追踪丢失提示
        self.show_status('目标追踪丢失: 目标不再在视野中')
        # 清空ID
        self.yolo_predict.lock_id = None

    # 完善流量图功能
    def show_traffic_graph(self):
        # 没有开始检测
        if not self.run_button.isChecked():
            DialogOver(parent=self, text="请先开始目标检测！", title="开启失败", flags="danger")
            return

        # 初始化流量图状态
        if not hasattr(self, 'traffic_graph_mode'):
            self.traffic_graph_mode = "both"  # 可选值: "both", "realtime", "accumulated"

        # 根据当前状态切换模式
        if self.traffic_graph_mode == "both":
            self.traffic_graph_mode = "realtime"
            self.src_graph_button.setText("实时流量分析")
            DialogOver(parent=self, text="切换到实时流量分析模式", title="流量图模式", flags="success")
        elif self.traffic_graph_mode == "realtime":
            self.traffic_graph_mode = "accumulated"
            self.src_graph_button.setText("累计流量分析")
            DialogOver(parent=self, text="切换到累计流量分析模式", title="流量图模式", flags="success")
        elif self.traffic_graph_mode == "accumulated":
            self.traffic_graph_mode = "both"
            self.src_graph_button.setText("双模式流量分析")
            DialogOver(parent=self, text="切换到双模式流量分析", title="流量图模式", flags="success")

        # 避免重复开启
        if self.is_draw_thread:
            # 已开启，只需改变模式
            if hasattr(self.draw_thread, 'set_chart_mode'):
                self.draw_thread.set_chart_mode(self.traffic_graph_mode)
        else:
            # 首次开启
            self.draw_thread.start()
            self.is_draw_thread = True

            # 连接数据更新信号
            if hasattr(self.draw_thread, 'data_updated'):
                self.draw_thread.data_updated.connect(self.update_traffic_stats)

    # 更新流量统计信息
    def update_traffic_stats(self, data):
        """更新流量统计信息到界面上"""
        if 'realtime' in data:
            realtime = data['realtime']
            self.char_label.setText(f"当前车流量: {realtime}")

            # 如果超过阈值，显示为红色
            if int(realtime) > int(self.car_threshold):
                self.char_label.setStyleSheet("color: red; font-weight: bold;")
            else:
                self.char_label.setStyleSheet("color: green;")

        if 'accumulated' in data:
            accumulated = data['accumulated']
            # 可以在界面的适当位置显示累计流量
            if hasattr(self, 'total_traffic_label'):
                self.total_traffic_label.setText(f"累计车流量: {accumulated}")

    # 切换热力图显示状态
    def toggle_heatmap(self):
        # 终止事件
        if not self.run_button.isChecked():
            DialogOver(parent=self, text="请先开始目标检测！", title="开启失败", flags="danger")
            return

        # 循环切换热力图显示模式
        if not self.show_heatmap and not self.show_accumulated_heatmap:
            # 显示实时拥塞情况
            self.show_heatmap = True
            self.show_accumulated_heatmap = False
            self.src_web_button.setText("实时拥塞情况")
            DialogOver(parent=self, text="实时拥塞情况已开启", title="拥塞状态", flags="success")
        elif self.show_heatmap and not self.show_accumulated_heatmap:
            # 显示累积拥塞情况
            self.show_heatmap = False
            self.show_accumulated_heatmap = True
            self.src_web_button.setText("累积拥塞情况")
            DialogOver(parent=self, text="累积拥塞情况已开启", title="拥塞状态", flags="success")
        else:
            # 关闭拥塞显示
            self.show_heatmap = False
            self.show_accumulated_heatmap = False
            self.src_web_button.setText("实时拥塞情况")

            # 清空热力图数据
            if 'reset_heatmap_data' in dir(self.yolo_predict):
                self.yolo_predict.reset_heatmap_data()

            DialogOver(parent=self, text="拥塞显示已关闭", title="拥塞状态", flags="warning")

        # 更新YOLO预测器的热力图设置
        self.yolo_predict.show_heatmap = self.show_heatmap
        self.yolo_predict.show_accumulated_heatmap = self.show_accumulated_heatmap

    #保存提示（MP4）
    def is_save_res(self):
        if self.save_res_button.checkState() == Qt.CheckState.Unchecked:
            self.show_status('提示: 监测结果不会被保存')
            self.yolo_predict.save_res = False
        elif self.save_res_button.checkState() == Qt.CheckState.Checked:
            self.show_status('提示: 监测结果将会被保存')
            self.yolo_predict.save_res = True

    #保存提示（txt）
    def is_save_txt(self):
        if self.save_txt_button.checkState() == Qt.CheckState.Unchecked:
            self.show_status('提示: 标签信息不会被保存')
            self.yolo_predict.save_txt = False
        elif self.save_txt_button.checkState() == Qt.CheckState.Checked:
            self.show_status('提示: 标签信息将会被保存')
            self.yolo_predict.save_txt = True

    # 是否显示 标签
    def is_show_labels(self):
        if self.show_labels_checkbox.checkState() == Qt.CheckState.Unchecked:
            self.yolo_predict.show_labels = False
            self.show_status('提示: 不再显示标签')
        elif self.show_labels_checkbox.checkState() == Qt.CheckState.Checked:
            self.yolo_predict.show_labels = True
            self.show_status('提示: 显示标签')

    # 是否显示 轨迹
    def is_show_trace(self):
        if self.show_trace_checkbox.checkState() == Qt.CheckState.Unchecked:
            self.yolo_predict.show_trace = False
            self.show_status('提示: 不再显示轨迹')
        elif self.show_trace_checkbox.checkState() == Qt.CheckState.Checked:
            self.yolo_predict.show_trace = True
            self.show_status('提示: 显示轨迹')

    #终止事件（按下终止按钮 or 输入源更换的时候）
    def stop(self):
        try:
            # 摄像头释放
            self.yolo_predict.release_capture()  # 这里是为了终止使用摄像头检测函数的线程，改了yolo源码
            # 结束线程
            self.yolo_thread.quit()

        except:
            pass
        self.yolo_predict.stop_dtc = True
        self.run_button.setChecked(False)    #恢复按钮状态

        # 终止后才可以修改设置
        self.save_res_button.setEnabled(True)   #把保存按钮设置为可用
        self.save_txt_button.setEnabled(True)   #把保存按钮设置为可用
        self.iou_slider.setEnabled(True)        #把滑块设置为可用
        self.conf_slider.setEnabled(True)       #把滑块设置为可用
        self.speed_slider.setEnabled(True)      #把滑块设置为可用
        self.pre_video.clear()           #清空视频显示
        self.res_video.clear()           #清空视频显示
        self.progress_bar.setValue(0)   #进度条清零
        self.Class_num.setText('--')
        self.Target_num.setText('--')
        self.fps_label.setText('--')

    #检测参数设置
    def change_val(self, x, flag):
        # 交互比
        if flag == 'iou_spinbox':
            self.iou_slider.setValue(int(x*100))
        elif flag == 'iou_slider':
            self.iou_spinbox.setValue(x/100)
            self.show_status('IOU Threshold: %s' % str(x/100))
            self.yolo_predict.iou_thres = x/100
        # 置信度
        elif flag == 'conf_spinbox':
            self.conf_slider.setValue(int(x*100))
        elif flag == 'conf_slider':
            self.conf_spinbox.setValue(x/100)
            self.show_status('Conf Threshold: %s' % str(x/100))
            self.yolo_predict.conf_thres = x/100
        # 延时
        elif flag == 'speed_spinbox':
            self.speed_slider.setValue(x)
        elif flag == 'speed_slider':
            self.speed_spinbox.setValue(x)
            self.show_status('Delay: %s ms' % str(x))
            self.yolo_predict.speed_thres = x  # ms

        # 车辆数预测警报
        elif flag == 'speed_nnn':
            self.speed_sss.setValue(x)
        elif flag == 'speed_sss':
            self.speed_nnn.setValue(x)
            self.show_status('流量阈值设置: %s 辆' % str(x))
            self.car_threshold = x  # ms

    #模型更换
    def change_model(self,x):
        self.select_model = self.model_box.currentText()
        self.yolo_predict.new_model_name = f"./{self.config.models_path}/%s" % self.select_model
        self.show_status('更改模型：%s' % self.select_model)
        self.Model_name.setText(self.select_model)

    #循环监测文件夹的文件变化
    def ModelBoxRefre(self):
        pt_list = os.listdir(f'./{self.config.models_path}')
        pt_list = [file for file in pt_list if file.endswith('.pt') or file.endswith('.engine')]
        pt_list.sort(key=lambda x: os.path.getsize(f'./{self.config.models_path}/' + x))
        #必须排序后再比较，否则列表会一直刷新
        if pt_list != self.pt_list:
            self.pt_list = pt_list
            self.model_box.clear()
            self.model_box.addItems(self.pt_list)

    #获取鼠标位置（用于按住标题栏拖动窗口）
    def mousePressEvent(self, event):
        p = event.globalPosition()
        globalPos = p.toPoint()
        self.dragPos = globalPos

    #拖动窗口大小时优化调整
    def resizeEvent(self, event):
        # Update Size Grips
        UIFuncitons.resize_grips(self)

    # 预测图片
    def img_predict(self):

        if check_url(self.yolo_predict.source):
            DialogOver(parent=self, text="目标路径含有中文！", title="程序取消", flags="danger")
            return
        self.run_button.setChecked(False)  # 按钮
        # 读取照片
        image = cv2.imread(self.yolo_predict.source)
        org_img = image.copy()
        # 加载模型
        model = self.yolo_predict.load_yolo_model()
        # 获取数据源
        iter_model = iter(model.track(source=image, show=False))
        result = next(iter_model)  # 这里是检测的核心，
        # 如果没有目标
        if result.boxes.id is None:
            DialogOver(parent=self, text="该图片中没有要检测的目标哟！", title="运行完成", flags="warning")
            self.show_image(image, self.pre_video)
            self.show_image(image, self.res_video)
            self.yolo_predict.source = ''
            return

        # 如果有目标
        # 直接使用supervision库创建检测结果
        detections = sv.Detections.from_yolov8(result)
        detections.tracker_id = result.boxes.id.cpu().numpy().astype(int)
        # 画标签
        labels_write, img_box = self.yolo_predict.creat_labels(detections, image, model)

        # 显示信息 —— 类别数 & 总数
        self.Class_num.setText(str(self.yolo_predict.get_class_number(detections)))
        self.Target_num.setText(str(len(detections.tracker_id)))
        # 显示图片
        self.show_image(org_img, self.pre_video)  # left
        self.show_image(img_box, self.res_video)  # right
        self.yolo_predict.source = ''
        DialogOver(parent=self, text="图片检测完成", title="运行成功", flags="success")

        # 保存图片
        if self.yolo_predict.save_res:
            check_path(self.config.save_res_path) # 检查保存路径
            # 存在同名文件，自增 self.image_id 直至文件不存在
            while os.path.exists(f"{self.config.save_res_path}/image_result_{self.image_id}.jpg"):
                self.image_id += 1
            # 将 BGR 格式的 frame 转换为 RGB 格式
            rgb_frame = cv2.cvtColor(img_box, cv2.COLOR_BGR2RGB)
            # 把 rgb_frame 转换为 numpy格式 就行了
            numpy_frame = np.array(rgb_frame)
            Image.fromarray(numpy_frame).save(f"./{self.config.save_res_path}/image_result_{self.image_id}.jpg")

        # 存储labels里的信息
        if self.yolo_predict.save_txt:
            check_path(self.config.save_txt_path) # 检查保存路径
            # 存在同名文件，自增 self.txt_id 直至文件不存在
            while os.path.exists(f"{self.config.save_txt_path}/result_{self.txt_id}.jpg"):
                self.txt_id += 1

            with open(f'{self.config.save_txt_path}/result_{self.txt_id}.txt', 'a') as f:
                f.write('当前时刻屏幕信息:' +
                        str(labels_write) +
                        f'检测时间: {datetime.datetime.now().strftime("%Y-%m-%d-%H:%M:%S")}' +
                        f' 路段通过的目标总数: {len(detections.tracker_id)}')
                f.write('\n')
        return

    # 违规事件检测功能
    def show_violation_detection(self):
        # 显示调试信息
        self.show_status('正在打开违规检测对话框...')

        try:
            # 创建违规检测对话框
            self.violation_dialog = ViolationDetectionWindow()
            self.violation_dialog.configConfirmed.connect(self.apply_violation_config)

            # 如果有现有的违规检测数据，则更新统计信息
            if hasattr(self.yolo_predict, 'violation_detector') and self.yolo_predict.violation_detector is not None:
                try:
                    stats = self.yolo_predict.violation_detector.violation_count
                    total = sum(stats.values())
                    stats_dict = {
                        "超速": stats["超速"],
                        "逆行": stats["逆行"],
                        "违规变道": stats["违规变道"],
                        "总数": total
                    }
                    self.violation_dialog.updateStats(stats_dict)
                except Exception as e:
                    print(f"Error updating stats: {str(e)}")

            # 显示违规检测对话框
            self.violation_dialog.show()

            # 显示成功提示
            DialogOver(parent=self, text="违规检测对话框已打开", title="成功", flags="success")
        except Exception as e:
            self.show_status(f'打开违规检测对话框失败: {str(e)}')
            print(f"Error showing violation dialog: {str(e)}")

    # 应用违规检测配置
    def apply_violation_config(self, config):
        # 应用违规检测配置
        if not hasattr(self.yolo_predict, 'violation_detection_enabled'):
            self.yolo_predict.violation_detection_enabled = False

        # 启用违规检测
        self.yolo_predict.violation_detection_enabled = False

        # 更新违规检测器的配置
        if hasattr(self.yolo_predict, 'violation_detector') and self.yolo_predict.violation_detector is not None:
            # 更新现有的违规检测器的配置
            self.yolo_predict.violation_detector.speed_limit = config['speed_limit']
        else:
            # 创建新的违规检测器
            try:
                self.yolo_predict.violation_detector = ViolationDetector(speed_limit=config['speed_limit'])
            except Exception as e:
                print(f"Error creating ViolationDetector: {str(e)}")
                self.show_status('创建违规检测器失败')
                return

        # 显示成功提示
        self.show_status('违规检测配置已应用')

    def show_pedestrian_detection(self):
        """显示简化版行人检测对话框"""
        # 显示调试信息
        self.show_status('正在打开行人检测对话框...')

        try:
            # 导入简化版行人检测对话框
            from ui.dialog.simple_pedestrian_dialog import SimplePedestrianDialog

            # 创建简化版行人检测对话框
            self.pedestrian_dialog = SimplePedestrianDialog(self, self.yolo_predict)

            # 连接信号
            self.pedestrian_dialog.detectionStarted.connect(self.on_pedestrian_detection_started)
            self.pedestrian_dialog.detectionStopped.connect(self.on_pedestrian_detection_stopped)
            self.pedestrian_dialog.pedestrianDetected.connect(self.on_pedestrian_detected)

            # 显示行人检测对话框
            self.pedestrian_dialog.show()

            # 显示成功提示
            DialogOver(parent=self, text="行人检测系统已打开", title="成功", flags="success")
        except Exception as e:
            self.show_status(f'打开行人检测对话框失败: {str(e)}')
            print(f"Error showing pedestrian dialog: {str(e)}")
            DialogOver(parent=self, text=f"打开失败: {str(e)}", title="错误", flags="danger")

    # 应用行人检测配置
    def apply_pedestrian_config(self, config):
        """应用行人检测配置"""
        # 初始化行人检测设置
        if not hasattr(self.yolo_predict, 'pedestrian_detection_enabled'):
            self.yolo_predict.pedestrian_detection_enabled = False

        # 启用行人检测
        self.yolo_predict.pedestrian_detection_enabled = True
        
        # 禁用其他检测功能以确保只检测行人
        self.yolo_predict.violation_detection_enabled = False
        self.yolo_predict.multi_tracking = False
        self.yolo_predict.show_speed = False

        # 保存配置到 YOLO 预测器中
        self.yolo_predict.pedestrian_config = config

        # 初始化行人统计数据
        if not hasattr(self.yolo_predict, 'pedestrian_stats'):
            self.yolo_predict.pedestrian_stats = {
                "普通行人": 0,
                "特殊行人": 0,
                "密度警告": 0,
                "总数": 0
            }

        # 显示成功提示
        self.show_status('行人闯入检测已启用，已自动切换为行人专检模式')
        DialogOver(parent=self, text="行人检测已启用\n系统将只检测行人，忽略车辆", title="行人检测模式", flags="success")

    # 更新行人统计信息
    def update_pedestrian_stats(self, stats):
        """更新行人检测统计信息"""
        if hasattr(self.yolo_predict, 'pedestrian_stats'):
            self.yolo_predict.pedestrian_stats = stats
            self.show_status(f'行人检测统计已更新: 总计 {stats["总数"]} 人')

    def on_pedestrian_detection_started(self):
        """行人检测启动时的处理"""
        self.show_status('行人检测已启动，正在监控...')

    def on_pedestrian_detection_stopped(self):
        """行人检测停止时的处理"""
        # 恢复正常检测模式
        if hasattr(self.yolo_predict, 'pedestrian_detection_enabled'):
            self.yolo_predict.pedestrian_detection_enabled = False
        
        # 重新启用其他检测功能
        self.yolo_predict.show_speed = False
        
        self.show_status('行人检测已停止，已恢复正常检测模式')
        DialogOver(parent=self, text="已退出行人检测模式\n系统恢复检测所有目标", title="检测模式", flags="info")

    def on_pedestrian_detected(self, count):
        """检测到行人时的处理"""
        if count > 0:
            self.show_status(f'🚶 检测到 {count} 名行人')

            # 如果检测到多人，显示警告
            if count >= 3:
                DialogOver(parent=self, text=f"检测到 {count} 名行人，请注意安全", title="行人警告", flags="warning")

if __name__ == "__main__":
    app = QApplication(sys.argv)

    # 应用美化样式
    from apply_style import apply_style_to_app
    apply_style_to_app(app)

    print("System started successfully")

    Home = MainWindow()

    # 更新标题
    from apply_style import update_title
    update_title(Home)

    Home.show()
    sys.exit(app.exec())

