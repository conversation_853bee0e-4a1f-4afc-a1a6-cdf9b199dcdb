# -*- coding: utf-8 -*-
"""
用户数据模型
"""

import hashlib
from datetime import datetime

class User:
    """用户模型类"""
    
    def __init__(self, user_id=None, username=None, password_hash=None, 
                 email=None, role='user', created_at=None, updated_at=None):
        self.user_id = user_id
        self.username = username
        self.password_hash = password_hash
        self.email = email
        self.role = role
        self.created_at = created_at or datetime.now()
        self.updated_at = updated_at or datetime.now()
    
    @staticmethod
    def hash_password(password):
        """密码哈希"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def check_password(self, password):
        """验证密码"""
        return self.password_hash == self.hash_password(password)
    
    @classmethod
    def find_by_username(cls, username, db_connection=None):
        """根据用户名查找用户"""
        if not db_connection:
            from utils.database import get_db_connection
            db_connection = get_db_connection()
        
        try:
            cursor = db_connection.cursor()
            cursor.execute(
                "SELECT user_id, username, password_hash, email, role, created_at, updated_at "
                "FROM users WHERE username = ?", 
                (username,)
            )
            row = cursor.fetchone()
            
            if row:
                return cls(
                    user_id=row[0],
                    username=row[1],
                    password_hash=row[2],
                    email=row[3],
                    role=row[4],
                    created_at=row[5],
                    updated_at=row[6]
                )
            return None
        except Exception as e:
            print(f"查找用户时出错: {e}")
            return None
        finally:
            cursor.close()
    
    def save(self, db_connection=None):
        """保存用户到数据库"""
        if not db_connection:
            from utils.database import get_db_connection
            db_connection = get_db_connection()
        
        try:
            cursor = db_connection.cursor()
            if self.user_id:
                # 更新现有用户
                cursor.execute(
                    "UPDATE users SET username=?, password_hash=?, email=?, role=?, updated_at=? "
                    "WHERE user_id=?",
                    (self.username, self.password_hash, self.email, self.role, 
                     datetime.now(), self.user_id)
                )
            else:
                # 创建新用户
                cursor.execute(
                    "INSERT INTO users (username, password_hash, email, role, created_at, updated_at) "
                    "VALUES (?, ?, ?, ?, ?, ?)",
                    (self.username, self.password_hash, self.email, self.role, 
                     self.created_at, self.updated_at)
                )
                self.user_id = cursor.lastrowid
            
            db_connection.commit()
            return True
        except Exception as e:
            print(f"保存用户时出错: {e}")
            db_connection.rollback()
            return False
        finally:
            cursor.close()
    
    def to_dict(self):
        """转换为字典"""
        return {
            'user_id': self.user_id,
            'username': self.username,
            'email': self.email,
            'role': self.role,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }