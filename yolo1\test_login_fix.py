#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试登录修复
"""

import requests
import json

def test_login_fix():
    """测试登录修复"""
    base_url = "http://127.0.0.1:5500"
    
    print("🔍 测试登录修复")
    print("=" * 50)
    
    # 测试不同的用户名和密码组合
    test_cases = [
        ("admin", "123456", "管理员用户"),
        ("operator", "123456", "操作员用户"),
        ("admin", "wrong_password", "错误密码测试"),
        ("nonexistent", "123456", "不存在用户测试")
    ]
    
    for username, password, description in test_cases:
        print(f"\n📝 {description}")
        print(f"   用户名: {username}, 密码: {password}")
        
        try:
            login_data = {"username": username, "password": password}
            response = requests.post(f"{base_url}/api/v1/auth/login", json=login_data, timeout=5)
            
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"   响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
                
                if data.get('success'):
                    print("   ✅ 登录成功")
                    token = data.get('data', {}).get('token')
                    if token:
                        print(f"   🔑 Token: {token[:30]}...")
                        
                        # 测试使用token访问受保护的接口
                        test_protected_api(base_url, token)
                    else:
                        print("   ⚠️ 未获取到Token")
                else:
                    print(f"   ❌ 登录失败: {data.get('message')}")
            else:
                print(f"   ❌ HTTP错误: {response.status_code}")
                print(f"   响应: {response.text}")
                
        except Exception as e:
            print(f"   ❌ 异常: {e}")

def test_protected_api(base_url, token):
    """测试受保护的API"""
    print("   🔒 测试受保护的API...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        # 测试获取用户信息
        response = requests.get(f"{base_url}/api/v1/auth/profile", headers=headers, timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                user = data.get('data', {})
                print(f"   ✅ 用户信息: {user.get('username')} ({user.get('grade')})")
            else:
                print(f"   ❌ 获取用户信息失败: {data.get('message')}")
        else:
            print(f"   ❌ 用户信息接口错误: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 用户信息接口异常: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 登录修复测试")
    print("=" * 60)
    
    test_login_fix()
    
    print("\n" + "=" * 60)
    print("📋 测试完成")
    print("💡 如果所有测试通过，说明登录问题已修复")
    print("=" * 60)

if __name__ == "__main__":
    main()
