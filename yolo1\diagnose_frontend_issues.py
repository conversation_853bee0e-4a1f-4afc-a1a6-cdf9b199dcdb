#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面诊断前端问题
"""

import requests
import json
import time
from datetime import datetime

def test_all_apis():
    """测试所有API的响应时间和格式"""
    base_url = "http://127.0.0.1:5500"
    
    print("🔐 获取token...")
    
    # 登录
    login_data = {"username": "admin", "password": "123456"}
    try:
        response = requests.post(f"{base_url}/api/v1/auth/login", json=login_data, timeout=5)
        if response.status_code != 200:
            print(f"❌ 登录失败: {response.status_code}")
            return False
        
        login_result = response.json()
        if not login_result.get('success'):
            print(f"❌ 登录失败: {login_result.get('message')}")
            return False
        
        token = login_result['data']['token']
        headers = {"Authorization": f"Bearer {token}"}
        print("✅ 登录成功")
        
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return False
    
    # 测试所有前端需要的API
    critical_apis = [
        # 基础API
        ("/api/v1/auth/profile", "用户资料", "对象"),
        ("/api/v1/analysis/statistics/overview", "概览统计", "对象"),
        ("/api/v1/monitor/list?page=1&size=100", "监控点列表", "对象"),
        
        # 实时警报相关
        ("/api/v1/accident/alerts/realtime?limit=20", "实时警报", "对象"),
        ("/api/v1/accident/records?page=1&page_size=10", "事故记录", "对象"),
        
        # 数据分析
        ("/api/v1/analysis/alarms", "警报统计", "对象"),
        ("/api/v1/analysis/traffic-flow", "交通流量", "数组"),
        
        # 检测任务
        ("/api/v1/detection/tasks?page=1&size=10", "检测任务", "对象"),
        
        # 系统管理
        ("/api/v1/system/users?page=1&size=10", "系统用户", "对象"),
        ("/api/v1/system/config", "系统配置", "对象"),
    ]
    
    results = []
    
    for endpoint, name, expected_type in critical_apis:
        print(f"\n🧪 测试 {name}...")
        
        try:
            start_time = time.time()
            response = requests.get(f"{base_url}{endpoint}", headers=headers, timeout=10)
            end_time = time.time()
            
            duration = end_time - start_time
            
            result = {
                'name': name,
                'endpoint': endpoint,
                'status_code': response.status_code,
                'duration': round(duration, 3),
                'success': False,
                'data_type': None,
                'data_size': 0,
                'error': None
            }
            
            if response.status_code == 200:
                try:
                    api_result = response.json()
                    if api_result.get('success'):
                        data = api_result.get('data')
                        result['success'] = True
                        result['data_type'] = type(data).__name__
                        
                        if isinstance(data, list):
                            result['data_size'] = len(data)
                        elif isinstance(data, dict):
                            result['data_size'] = len(data.keys())
                        
                        print(f"   ✅ 成功 - {duration:.3f}秒 - {result['data_type']} - 大小:{result['data_size']}")
                        
                        # 特别检查实时警报
                        if name == "实时警报":
                            alerts = data.get('alerts', [])
                            print(f"   📊 警报数据: {len(alerts)}条警报")
                            if alerts:
                                alert = alerts[0]
                                print(f"   📋 警报字段: {list(alert.keys())}")
                    else:
                        result['error'] = api_result.get('message', '未知错误')
                        print(f"   ❌ API失败: {result['error']}")
                        
                except json.JSONDecodeError as e:
                    result['error'] = f"JSON解析错误: {e}"
                    print(f"   ❌ JSON错误: {e}")
            else:
                result['error'] = f"HTTP {response.status_code}"
                print(f"   ❌ HTTP错误: {response.status_code}")
                
        except requests.exceptions.Timeout:
            result['error'] = "请求超时"
            print(f"   ❌ 超时")
        except Exception as e:
            result['error'] = str(e)
            print(f"   ❌ 异常: {e}")
        
        results.append(result)
    
    return results

def analyze_results(results):
    """分析测试结果"""
    print("\n" + "=" * 80)
    print("📊 API测试结果分析")
    print("=" * 80)
    
    total_apis = len(results)
    successful_apis = len([r for r in results if r['success']])
    failed_apis = [r for r in results if not r['success']]
    slow_apis = [r for r in results if r['duration'] > 2.0]
    
    print(f"总API数量: {total_apis}")
    print(f"成功API数量: {successful_apis}")
    print(f"失败API数量: {len(failed_apis)}")
    print(f"慢API数量 (>2秒): {len(slow_apis)}")
    
    if failed_apis:
        print(f"\n❌ 失败的API:")
        for api in failed_apis:
            print(f"   {api['name']}: {api['error']}")
    
    if slow_apis:
        print(f"\n⚠️ 慢响应API:")
        for api in slow_apis:
            print(f"   {api['name']}: {api['duration']}秒")
    
    # 特别关注实时警报
    realtime_alert = next((r for r in results if r['name'] == '实时警报'), None)
    if realtime_alert:
        print(f"\n🚨 实时警报API详情:")
        print(f"   状态: {'✅ 成功' if realtime_alert['success'] else '❌ 失败'}")
        print(f"   响应时间: {realtime_alert['duration']}秒")
        print(f"   数据类型: {realtime_alert['data_type']}")
        print(f"   数据大小: {realtime_alert['data_size']}")
        if realtime_alert['error']:
            print(f"   错误: {realtime_alert['error']}")

def check_frontend_expectations():
    """检查前端期望的数据格式"""
    print("\n" + "=" * 80)
    print("🔍 检查前端期望格式")
    print("=" * 80)
    
    base_url = "http://127.0.0.1:5500"
    
    # 登录
    login_data = {"username": "admin", "password": "123456"}
    response = requests.post(f"{base_url}/api/v1/auth/login", json=login_data)
    token = response.json()['data']['token']
    headers = {"Authorization": f"Bearer {token}"}
    
    # 检查实时警报的具体格式
    print("🚨 检查实时警报API格式...")
    response = requests.get(f"{base_url}/api/v1/accident/alerts/realtime?limit=3", headers=headers)
    
    if response.status_code == 200:
        result = response.json()
        print(f"完整响应结构:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
    else:
        print(f"❌ 请求失败: {response.status_code}")

def main():
    """主函数"""
    print("=" * 80)
    print("🚀 前端问题全面诊断")
    print("=" * 80)
    
    # 1. 测试所有API
    results = test_all_apis()
    
    if results:
        # 2. 分析结果
        analyze_results(results)
        
        # 3. 检查前端期望格式
        check_frontend_expectations()
    
    print("\n" + "=" * 80)
    print("💡 问题诊断建议:")
    print("1. 如果实时警报API成功但前端一直转圈:")
    print("   - 检查前端JavaScript控制台错误")
    print("   - 检查前端是否正确解析API响应")
    print("   - 清除浏览器缓存和localStorage")
    print("2. 如果导航不响应:")
    print("   - 检查前端路由配置")
    print("   - 检查是否有JavaScript错误阻塞")
    print("   - 重启前端开发服务器")
    print("3. 如果API响应慢:")
    print("   - 检查数据库连接")
    print("   - 优化SQL查询")
    print("   - 减少返回数据量")
    print("=" * 80)

if __name__ == "__main__":
    main()
