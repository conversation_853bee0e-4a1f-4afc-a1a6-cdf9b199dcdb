# 1. 创建Python 3.9.16环境
conda create -n ByteTrack python=3.9.16 -y

# 2. 激活环境
conda activate ByteTrack

# 3. 安装PyTorch CUDA版本 (推荐方式)
conda install pytorch==2.1.2 torchvision==0.16.2 torchaudio==2.1.2 pytorch-cuda=11.8 -c pytorch -c nvidia -y

# 4. 安装科学计算包
conda install numpy=1.24.3 scipy=1.10.1 matplotlib=3.7.2 pandas=2.0.3 -y

# 5. 安装其他依赖
pip install opencv-python==********
pip install pillow==9.5.0
pip install ultralytics>=8.0.0
pip install supervision>=0.16.0
pip install flask==2.3.3 flask-cors==4.0.0
pip install pymysql==1.1.0 python-dotenv==1.0.0
pip install psutil==5.9.6 tqdm requests

# 6. 验证安装
python -c "import torch; print(f'PyTorch: {torch.__version__}'); print(f'CUDA: {torch.cuda.is_available()}')"
python -c "from ultralytics import YOLO; print('YOLO导入成功')"