#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
道路预警对话框 - 浅色透明风格
"""

from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

class RoadWarningDialog(QDialog):
    """道路预警对话框 - 精美的浅色透明设计"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        self.setup_animations()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("道路预警系统")
        self.setFixedSize(480, 320)
        self.setModal(True)
        
        # 设置窗口无边框和透明背景
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.Dialog)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # 主容器
        main_container = QFrame(self)
        main_container.setGeometry(0, 0, 480, 320)
        main_container.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.5 rgba(248, 250, 252, 0.98),
                    stop:1 rgba(241, 245, 249, 0.95));
                border-radius: 20px;
                border: 1px solid rgba(203, 213, 225, 0.6);
                /* Qt不支持box-shadow属性 */
            }
        """)
        
        # 添加毛玻璃效果
        blur_effect = QGraphicsBlurEffect()
        blur_effect.setBlurRadius(1)
        main_container.setGraphicsEffect(blur_effect)
        
        # 主布局
        layout = QVBoxLayout(main_container)
        layout.setContentsMargins(25, 20, 25, 20)
        layout.setSpacing(15)
        
        # 创建标题区域
        self.create_header(layout)
        
        # 创建内容区域
        self.create_content(layout)
        
        # 创建底部按钮
        self.create_footer(layout)
        
        # 添加阴影效果
        self.add_shadow_effect(main_container)
        
    def create_header(self, parent_layout):
        """创建标题区域"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: transparent;
                border: none;
            }
        """)
        
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(0, 0, 0, 0)
        
        # 警告图标
        icon_label = QLabel()
        icon_label.setFixedSize(32, 32)
        icon_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgb(251, 191, 36),
                    stop:1 rgb(245, 158, 11));
                border-radius: 16px;
                color: white;
                font-size: 18px;
                font-weight: bold;
            }
        """)
        icon_label.setText("⚠")
        icon_label.setAlignment(Qt.AlignCenter)
        
        # 标题文本
        title_label = QLabel("道路预警提醒")
        title_label.setStyleSheet("""
            QLabel {
                color: rgb(51, 65, 85);
                font-family: 'Microsoft YaHei UI', 'PingFang SC', sans-serif;
                font-size: 20px;
                font-weight: 600;
                background: transparent;
                border: none;
                margin-left: 10px;
            }
        """)
        
        # 关闭按钮
        self.close_btn = QPushButton("×")
        self.close_btn.setFixedSize(28, 28)
        self.close_btn.setStyleSheet("""
            QPushButton {
                background: rgba(148, 163, 184, 0.1);
                border: none;
                border-radius: 14px;
                color: rgb(100, 116, 139);
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: rgba(239, 68, 68, 0.1);
                color: rgb(239, 68, 68);
            }
            QPushButton:pressed {
                background: rgba(239, 68, 68, 0.2);
            }
        """)
        self.close_btn.clicked.connect(self.close)
        
        header_layout.addWidget(icon_label)
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addWidget(self.close_btn)
        
        parent_layout.addWidget(header_frame)
        
    def create_content(self, parent_layout):
        """创建内容区域"""
        content_frame = QFrame()
        content_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.4);
                border-radius: 12px;
                border: 1px solid rgba(226, 232, 240, 0.8);
                padding: 15px;
            }
        """)
        
        content_layout = QVBoxLayout(content_frame)
        content_layout.setSpacing(12)
        
        # 预警信息
        warning_text = QLabel("检测到前方路段存在以下情况：")
        warning_text.setStyleSheet("""
            QLabel {
                color: rgb(71, 85, 105);
                font-size: 14px;
                font-weight: 500;
                background: transparent;
                border: none;
            }
        """)
        content_layout.addWidget(warning_text)
        
        # 预警项目列表
        warnings = [
            ("🚗", "车流量异常", "当前车流量超过正常水平 25%", "rgb(239, 68, 68)"),
            ("🐌", "行驶缓慢", "平均车速低于 30 km/h", "rgb(245, 158, 11)"),
            ("⚡", "拥堵预警", "预计 15 分钟后形成拥堵", "rgb(59, 130, 246)")
        ]
        
        for icon, title, desc, color in warnings:
            warning_item = self.create_warning_item(icon, title, desc, color)
            content_layout.addWidget(warning_item)
        
        parent_layout.addWidget(content_frame)
        
    def create_warning_item(self, icon, title, description, color):
        """创建预警项目"""
        item_frame = QFrame()
        item_frame.setStyleSheet(f"""
            QFrame {{
                background: rgba(255, 255, 255, 0.6);
                border-radius: 8px;
                border-left: 3px solid {color};
                padding: 8px;
            }}
        """)
        
        item_layout = QHBoxLayout(item_frame)
        item_layout.setContentsMargins(12, 8, 12, 8)
        
        # 图标
        icon_label = QLabel(icon)
        icon_label.setFixedSize(24, 24)
        icon_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                background: transparent;
                border: none;
            }
        """)
        icon_label.setAlignment(Qt.AlignCenter)
        
        # 文本信息
        text_layout = QVBoxLayout()
        text_layout.setSpacing(2)
        
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {color};
                font-size: 13px;
                font-weight: 600;
                background: transparent;
                border: none;
            }}
        """)
        
        desc_label = QLabel(description)
        desc_label.setStyleSheet("""
            QLabel {
                color: rgb(100, 116, 139);
                font-size: 11px;
                background: transparent;
                border: none;
            }
        """)
        
        text_layout.addWidget(title_label)
        text_layout.addWidget(desc_label)
        
        item_layout.addWidget(icon_label)
        item_layout.addLayout(text_layout)
        item_layout.addStretch()
        
        return item_frame
        
    def create_footer(self, parent_layout):
        """创建底部按钮"""
        footer_layout = QHBoxLayout()
        footer_layout.setSpacing(10)
        
        # 查看详情按钮
        detail_btn = QPushButton("查看详情")
        detail_btn.setFixedHeight(36)
        detail_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(59, 130, 246, 0.1),
                    stop:1 rgba(37, 99, 235, 0.1));
                border: 1px solid rgba(59, 130, 246, 0.3);
                border-radius: 8px;
                color: rgb(59, 130, 246);
                font-size: 13px;
                font-weight: 500;
                padding: 0 20px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(59, 130, 246, 0.15),
                    stop:1 rgba(37, 99, 235, 0.15));
                border-color: rgba(59, 130, 246, 0.5);
            }
            QPushButton:pressed {
                background: rgba(59, 130, 246, 0.2);
            }
        """)
        
        # 确认按钮
        confirm_btn = QPushButton("我知道了")
        confirm_btn.setFixedHeight(36)
        confirm_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgb(59, 130, 246),
                    stop:1 rgb(37, 99, 235));
                border: none;
                border-radius: 8px;
                color: white;
                font-size: 13px;
                font-weight: 500;
                padding: 0 24px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgb(37, 99, 235),
                    stop:1 rgb(29, 78, 216));
            }
            QPushButton:pressed {
                background: rgb(29, 78, 216);
            }
        """)
        confirm_btn.clicked.connect(self.accept)
        
        footer_layout.addStretch()
        footer_layout.addWidget(detail_btn)
        footer_layout.addWidget(confirm_btn)
        
        parent_layout.addLayout(footer_layout)
        
    def add_shadow_effect(self, widget):
        """添加阴影效果"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(30)
        shadow.setColor(QColor(0, 0, 0, 40))
        shadow.setOffset(0, 10)
        widget.setGraphicsEffect(shadow)
        
    def setup_animations(self):
        """设置动画效果"""
        # 淡入动画
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(300)
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(1.0)
        self.fade_animation.setEasingCurve(QEasingCurve.OutCubic)
        
        # 缩放动画
        self.scale_animation = QPropertyAnimation(self, b"geometry")
        self.scale_animation.setDuration(300)
        self.scale_animation.setEasingCurve(QEasingCurve.OutBack)
        
    def showEvent(self, event):
        """显示事件 - 播放动画"""
        super().showEvent(event)
        
        # 设置初始状态
        self.setWindowOpacity(0.0)
        
        # 计算居中位置
        if self.parent():
            parent_rect = self.parent().geometry()
            x = parent_rect.x() + (parent_rect.width() - self.width()) // 2
            y = parent_rect.y() + (parent_rect.height() - self.height()) // 2
        else:
            screen = QApplication.primaryScreen().geometry()
            x = (screen.width() - self.width()) // 2
            y = (screen.height() - self.height()) // 2
        
        # 设置动画起始和结束位置
        start_rect = QRect(x, y + 20, self.width(), self.height())
        end_rect = QRect(x, y, self.width(), self.height())
        
        self.scale_animation.setStartValue(start_rect)
        self.scale_animation.setEndValue(end_rect)
        
        # 播放动画
        self.fade_animation.start()
        self.scale_animation.start()


# 使用示例
if __name__ == "__main__":
    import sys
    app = QApplication(sys.argv)
    
    dialog = RoadWarningDialog()
    dialog.show()
    
    sys.exit(app.exec())
