# -*- coding: utf-8 -*-
# @Description : 测试用户信息接口
# @Date : 2025年6月21日

import requests
import json

def test_profile():
    """测试用户信息接口"""
    base_url = "http://127.0.0.1:5500"
    
    print("🔐 测试用户信息接口")
    print("="*50)
    
    # 1. 先登录获取Token
    print("1. 登录获取Token...")
    login_response = requests.post(f"{base_url}/api/v1/auth/login", 
        json={
            'username': 'admin',
            'password': '123456'
        },
        timeout=10
    )
    
    print(f"登录状态码: {login_response.status_code}")
    
    if login_response.status_code == 200:
        login_data = login_response.json()
        print(f"登录响应: {json.dumps(login_data, ensure_ascii=False, indent=2)}")
        
        if login_data.get('code') == 200:
            token = login_data['data']['token']
            print(f"✅ 获取Token成功: {token}")
            
            # 2. 使用Token获取用户信息
            print("\n2. 使用Token获取用户信息...")
            headers = {'Authorization': f'Bearer {token}'}
            
            profile_response = requests.get(f"{base_url}/api/v1/auth/profile", 
                headers=headers,
                timeout=10
            )
            
            print(f"用户信息状态码: {profile_response.status_code}")
            
            try:
                profile_data = profile_response.json()
                print(f"用户信息响应: {json.dumps(profile_data, ensure_ascii=False, indent=2)}")
                
                if profile_data.get('code') == 200:
                    print("✅ 获取用户信息成功!")
                    user = profile_data['data']
                    print(f"👤 用户: {user['username']} ({user['grade']})")
                    print(f"📧 邮箱: {user['email']}")
                else:
                    print(f"❌ 获取用户信息失败: {profile_data.get('message')}")
                    
            except json.JSONDecodeError:
                print(f"📄 非JSON响应: {profile_response.text}")
                
        else:
            print(f"❌ 登录失败: {login_data.get('message')}")
    else:
        print(f"❌ 登录请求失败: {login_response.status_code}")
    
    # 3. 测试无Token访问
    print("\n3. 测试无Token访问...")
    no_token_response = requests.get(f"{base_url}/api/v1/auth/profile", timeout=10)
    print(f"无Token状态码: {no_token_response.status_code}")
    
    try:
        no_token_data = no_token_response.json()
        print(f"无Token响应: {json.dumps(no_token_data, ensure_ascii=False, indent=2)}")
    except:
        print(f"无Token响应文本: {no_token_response.text}")
    
    # 4. 测试错误Token
    print("\n4. 测试错误Token...")
    wrong_headers = {'Authorization': 'Bearer wrong_token'}
    wrong_token_response = requests.get(f"{base_url}/api/v1/auth/profile", 
        headers=wrong_headers,
        timeout=10
    )
    print(f"错误Token状态码: {wrong_token_response.status_code}")
    
    try:
        wrong_token_data = wrong_token_response.json()
        print(f"错误Token响应: {json.dumps(wrong_token_data, ensure_ascii=False, indent=2)}")
    except:
        print(f"错误Token响应文本: {wrong_token_response.text}")
    
    print(f"\n{'='*50}")
    print("🎯 用户信息接口测试完成!")

if __name__ == "__main__":
    test_profile()
