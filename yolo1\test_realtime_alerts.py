#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试实时警报API
"""

import requests
import json
import time

def test_realtime_alerts():
    """测试实时警报API"""
    base_url = "http://127.0.0.1:5500"
    
    print("🔐 获取token...")
    
    # 1. 登录获取token
    login_data = {"username": "admin", "password": "123456"}
    try:
        response = requests.post(f"{base_url}/api/v1/auth/login", json=login_data, timeout=5)
        if response.status_code != 200:
            print(f"❌ 登录失败: {response.status_code}")
            return
        
        login_result = response.json()
        if not login_result.get('success'):
            print(f"❌ 登录失败: {login_result.get('message')}")
            return
        
        token = login_result['data']['token']
        headers = {"Authorization": f"Bearer {token}"}
        print("✅ 登录成功")
        
        # 2. 测试实时警报API
        print("\n🧪 测试实时警报API...")
        
        start_time = time.time()
        try:
            response = requests.get(
                f"{base_url}/api/v1/accident/alerts/realtime?limit=20", 
                headers=headers, 
                timeout=10
            )
            end_time = time.time()
            
            print(f"   响应时间: {end_time - start_time:.2f}秒")
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"   响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                if result.get('success'):
                    data = result.get('data', {})
                    alerts = data.get('alerts', [])
                    print(f"   ✅ API成功，返回{len(alerts)}条警报")
                    
                    # 检查数据格式
                    if alerts:
                        alert = alerts[0]
                        print(f"   示例警报字段: {list(alert.keys())}")
                    else:
                        print("   ⚠️ 没有警报数据")
                else:
                    print(f"   ❌ API失败: {result.get('message')}")
            else:
                print(f"   ❌ HTTP错误: {response.status_code}")
                print(f"   响应: {response.text}")
                
        except requests.exceptions.Timeout:
            print("   ❌ 请求超时")
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")
        
        # 3. 测试其他相关API
        other_apis = [
            ("/api/v1/accident/records?page=1&page_size=10", "事故记录"),
            ("/api/v1/analysis/statistics/overview", "概览统计"),
            ("/api/v1/monitor/list", "监控点列表")
        ]
        
        for endpoint, name in other_apis:
            print(f"\n🧪 测试 {name} API...")
            try:
                start_time = time.time()
                response = requests.get(f"{base_url}{endpoint}", headers=headers, timeout=5)
                end_time = time.time()
                
                print(f"   响应时间: {end_time - start_time:.2f}秒")
                print(f"   状态码: {response.status_code}")
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        print(f"   ✅ {name} API 成功")
                    else:
                        print(f"   ❌ {name} API 失败: {result.get('message')}")
                else:
                    print(f"   ❌ {name} HTTP错误: {response.status_code}")
                    
            except Exception as e:
                print(f"   ❌ {name} 请求异常: {e}")
        
    except Exception as e:
        print(f"❌ 登录异常: {e}")

def check_database_alarms():
    """检查数据库中的警报数据"""
    print("\n🔍 检查数据库警报数据...")
    
    import pymysql
    from datetime import datetime
    
    config = {
        'host': '127.0.0.1',
        'port': 3306,
        'user': 'root',
        'password': '123456',
        'database': 'yolo',
        'charset': 'utf8mb4',
        'autocommit': True,
        'cursorclass': pymysql.cursors.DictCursor
    }
    
    try:
        connection = pymysql.connect(**config)
        with connection.cursor() as cursor:
            # 检查最近1小时的警报
            cursor.execute("""
                SELECT COUNT(*) as count FROM alarm 
                WHERE create_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
            """)
            recent_count = cursor.fetchone()['count']
            print(f"   最近1小时警报数: {recent_count}")
            
            # 检查今日警报
            cursor.execute("""
                SELECT COUNT(*) as count FROM alarm 
                WHERE DATE(create_time) = CURDATE()
            """)
            today_count = cursor.fetchone()['today']
            print(f"   今日警报数: {today_count}")
            
            # 检查总警报数
            cursor.execute("SELECT COUNT(*) as count FROM alarm")
            total_count = cursor.fetchone()['count']
            print(f"   总警报数: {total_count}")
            
            if total_count == 0:
                print("   ⚠️ 数据库中没有警报数据，需要添加测试数据")
            
        connection.close()
        
    except Exception as e:
        print(f"   ❌ 检查数据库失败: {e}")

def main():
    """主函数"""
    print("=" * 80)
    print("🚀 实时警报API测试")
    print("=" * 80)
    
    # 1. 检查数据库数据
    check_database_alarms()
    
    # 2. 测试API
    test_realtime_alerts()
    
    print("\n" + "=" * 80)
    print("💡 如果实时警报API响应慢或失败:")
    print("   1. 检查数据库连接是否正常")
    print("   2. 检查是否有足够的测试数据")
    print("   3. 检查SQL查询是否有性能问题")
    print("   4. 检查前端是否在等待这个API响应")
    print("=" * 80)

if __name__ == "__main__":
    main()
