import numpy as np
from collections import defaultdict
from PySide6.QtCore import QObject, Signal
import cv2
import time

class TargetTracker(QObject):
    # 定义信号
    target_image_signal = Signal(object)  # 发送目标图像
    status_signal = Signal(str, str)      # 发送状态信息 (message, color)
    tracking_lost_signal = Signal()       # 追踪丢失信号

    def __init__(self, max_age=10, min_hits=3, iou_threshold=0.3):
        """初始化目标跟踪器

        Args:
            max_age: 目标消失后的最大存活帧数
            min_hits: 确认目标所需的最小连续检测次数
            iou_threshold: IOU匹配阈值
        """
        super().__init__()
        self.max_age = max_age
        self.min_hits = min_hits
        self.iou_threshold = iou_threshold

        self.tracks = []
        self.frame_count = 0
        self.track_id = 0

        # 单目标追踪相关属性
        self.target_id = None
        self.is_tracking = False
        self.last_target_frame = None

        # 多目标追踪相关属性
        self.multi_tracking = False
        self.multi_tracking_ids = []
        self.tracked_targets = {}  # 存储多个追踪目标的信息

    def _calculate_iou(self, box1, box2):
        """计算两个边界框的IOU

        Args:
            box1: [x1, y1, x2, y2]
            box2: [x1, y1, x2, y2]
        """
        x1 = max(box1[0], box2[0])
        y1 = max(box1[1], box2[1])
        x2 = min(box1[2], box2[2])
        y2 = min(box1[3], box2[3])

        if x2 < x1 or y2 < y1:
            return 0.0

        intersection = (x2 - x1) * (y2 - y1)
        box1_area = (box1[2] - box1[0]) * (box1[3] - box1[1])
        box2_area = (box2[2] - box2[0]) * (box2[3] - box2[1])
        union = box1_area + box2_area - intersection

        return intersection / union if union > 0 else 0

    def update(self, detections):
        """更新跟踪状态

        Args:
            detections: 当前帧的检测结果列表，每个元素为[x1, y1, x2, y2, conf, cls]

        Returns:
            tracks: 更新后的跟踪结果列表，每个元素为[x1, y1, x2, y2, track_id, cls]
        """
        self.frame_count += 1

        # 没有检测结果，更新所有轨迹状态
        if len(detections) == 0:
            for track in self.tracks:
                track['time_since_update'] += 1
            return self._get_tracks()

        # 没有轨迹，初始化新轨迹
        if len(self.tracks) == 0:
            for det in detections:
                self._init_track(det)
            return self._get_tracks()

        # 计算检测结果和现有轨迹的IOU矩阵
        iou_matrix = np.zeros((len(self.tracks), len(detections)))
        for t, track in enumerate(self.tracks):
            for d, det in enumerate(detections):
                iou_matrix[t, d] = self._calculate_iou(track['bbox'], det[:4])

        # 匹配检测结果和轨迹
        matched_indices = []
        for t, track in enumerate(self.tracks):
            if track['time_since_update'] > self.max_age:
                continue

            if len(matched_indices) == len(detections):
                break

            max_iou = np.max(iou_matrix[t])
            if max_iou >= self.iou_threshold:
                d = np.argmax(iou_matrix[t])
                if d not in [m[1] for m in matched_indices]:
                    matched_indices.append([t, d])

        # 更新匹配的轨迹
        for t, d in matched_indices:
            self._update_track(self.tracks[t], detections[d])

        # 处理未匹配的检测结果
        unmatched_dets = [d for d in range(len(detections))
                         if d not in [m[1] for m in matched_indices]]
        for d in unmatched_dets:
            self._init_track(detections[d])

        # 更新未匹配的轨迹
        unmatched_tracks = [t for t in range(len(self.tracks))
                           if t not in [m[0] for m in matched_indices]]
        for t in unmatched_tracks:
            self.tracks[t]['time_since_update'] += 1

        # 删除过期轨迹
        self.tracks = [t for t in self.tracks if t['time_since_update'] <= self.max_age]

        return self._get_tracks()

    def _init_track(self, detection):
        """初始化新轨迹

        Args:
            detection: [x1, y1, x2, y2, conf, cls]
        """
        self.tracks.append({
            'track_id': self.track_id,
            'bbox': detection[:4],
            'hits': 1,
            'time_since_update': 0,
            'cls': detection[5],
            'history': [detection[:4]]
        })
        self.track_id += 1

    def _update_track(self, track, detection):
        """更新轨迹信息

        Args:
            track: 轨迹字典
            detection: [x1, y1, x2, y2, conf, cls]
        """
        track['bbox'] = detection[:4]
        track['hits'] += 1
        track['time_since_update'] = 0
        track['history'].append(detection[:4])

    def _get_tracks(self):
        """获取当前有效的跟踪结果

        Returns:
            tracks: [[x1, y1, x2, y2, track_id, cls], ...]
        """
        results = []
        for track in self.tracks:
            if track['hits'] >= self.min_hits and track['time_since_update'] == 0:
                bbox = track['bbox']
                results.append([*bbox, track['track_id'], track['cls']])
        return results

    def set_target_id(self, target_id):
        """设置要追踪的目标ID"""
        self.target_id = target_id
        self.is_tracking = True
        self.status_signal.emit(f"开始追踪目标 ID: {target_id}", "green")

    def process_frame(self, frame, bbox, object_id):
        """处理帧数据，用于单目标追踪"""
        try:
            if self.target_id is None or object_id != self.target_id:
                return

            # 提取目标区域
            x1, y1, x2, y2 = bbox
            target_region = frame[y1:y2, x1:x2]

            if target_region.size > 0:
                # 调整大小以便显示
                target_region = cv2.resize(target_region, (200, 200))
                self.last_target_frame = target_region

                # 转换为QPixmap并发送信号
                from PySide6.QtGui import QImage, QPixmap
                h, w, ch = target_region.shape
                bytes_per_line = ch * w
                qt_image = QImage(target_region.data, w, h, bytes_per_line, QImage.Format_RGB888)
                pixmap = QPixmap.fromImage(qt_image.rgbSwapped())

                self.target_image_signal.emit(pixmap)
                self.status_signal.emit(f"正在追踪目标 ID: {self.target_id}", "green")

        except Exception as e:
            self.status_signal.emit(f"追踪错误: {str(e)}", "red")

    def update_frame(self, detections, frame):
        """更新帧并返回增强的帧"""
        try:
            if not self.is_tracking or self.target_id is None:
                return frame

            # 检查目标是否在当前检测中
            target_found = False
            enhanced_frame = frame.copy()

            for i, (_, _, _, _, tracker_id) in enumerate(detections):
                if tracker_id == self.target_id:
                    target_found = True
                    # 在目标周围绘制特殊标记
                    bbox = detections.xyxy[i]
                    x1, y1, x2, y2 = map(int, bbox)

                    # 绘制高亮边框
                    cv2.rectangle(enhanced_frame, (x1-3, y1-3), (x2+3, y2+3), (0, 255, 0), 3)
                    cv2.putText(enhanced_frame, f"TRACKING: {self.target_id}",
                              (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

                    # 处理目标区域
                    self.process_frame(frame, (x1, y1, x2, y2), tracker_id)
                    break

            if not target_found:
                self.status_signal.emit(f"目标 ID: {self.target_id} 丢失", "red")
                # 可以选择是否自动停止追踪
                # self.stop_tracking()

            return enhanced_frame

        except Exception as e:
            self.status_signal.emit(f"帧更新错误: {str(e)}", "red")
            return frame

    def stop_tracking(self):
        """停止追踪"""
        self.is_tracking = False
        self.target_id = None
        self.tracking_lost_signal.emit()
        self.status_signal.emit("追踪已停止", "gray")

    def start_multi_tracking(self, target_ids):
        """开始多目标追踪"""
        self.multi_tracking = True
        self.multi_tracking_ids = target_ids
        self.tracked_targets = {}
        self.status_signal.emit(f"开始多目标追踪，目标数量: {len(target_ids)}", "green")

    def stop_multi_tracking(self):
        """停止多目标追踪"""
        self.multi_tracking = False
        self.multi_tracking_ids = []
        self.tracked_targets = {}
        self.status_signal.emit("多目标追踪已停止", "gray")

    def update_multi_tracking(self, detections, frame):
        """更新多目标追踪"""
        if not self.multi_tracking or not self.multi_tracking_ids:
            return frame

        enhanced_frame = frame.copy()

        try:
            for target_id in self.multi_tracking_ids:
                # 检查目标是否在当前检测中
                if hasattr(detections, 'tracker_id') and target_id in detections.tracker_id:
                    # 找到目标索引
                    target_index = list(detections.tracker_id).index(target_id)
                    bbox = detections.xyxy[target_index]
                    x1, y1, x2, y2 = map(int, bbox)

                    # 绘制特殊标记
                    cv2.rectangle(enhanced_frame, (x1-3, y1-3), (x2+3, y2+3), (0, 255, 0), 3)
                    cv2.putText(enhanced_frame, f"MULTI-TRACK: {target_id}",
                              (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

                    # 提取目标区域并发送信号
                    target_region = frame[y1:y2, x1:x2]
                    if target_region.size > 0:
                        # 调整大小
                        target_region = cv2.resize(target_region, (200, 200))

                        # 转换为QPixmap并发送信号
                        from PySide6.QtGui import QImage, QPixmap
                        h, w, ch = target_region.shape
                        bytes_per_line = ch * w
                        qt_image = QImage(target_region.data, w, h, bytes_per_line, QImage.Format_RGB888)
                        pixmap = QPixmap.fromImage(qt_image.rgbSwapped())

                        self.target_image_signal.emit(pixmap)

                        # 更新追踪信息
                        self.tracked_targets[target_id] = {
                            'bbox': bbox,
                            'last_seen': time.time()
                        }

        except Exception as e:
            self.status_signal.emit(f"多目标追踪错误: {str(e)}", "red")

        return enhanced_frame