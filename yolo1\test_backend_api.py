# -*- coding: utf-8 -*-
# @Description : 后端API测试脚本
# @Date : 2025年6月20日

import requests
import json
import time

def test_api(url, method='GET', data=None, headers=None):
    """测试API接口"""
    try:
        if method == 'GET':
            response = requests.get(url, headers=headers, timeout=10)
        elif method == 'POST':
            response = requests.post(url, json=data, headers=headers, timeout=10)
        
        print(f"\n{'='*60}")
        print(f"测试: {method} {url}")
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        try:
            response_data = response.json()
            print(f"响应数据: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
        except:
            print(f"响应文本: {response.text}")
        
        return response.status_code == 200, response
    except Exception as e:
        print(f"\n{'='*60}")
        print(f"测试: {method} {url}")
        print(f"错误: {e}")
        return False, None

def main():
    """主测试函数"""
    base_url = "http://127.0.0.1:5500"
    
    print("基于Yolov8与ByteTrack的高速公路智慧监控平台 - 后端API测试")
    print("="*80)
    
    # 测试基础接口
    tests = [
        # 基础接口
        (f"{base_url}/", "GET", None),
        (f"{base_url}/health", "GET", None),
        (f"{base_url}/api/docs", "GET", None),
        (f"{base_url}/api/test", "GET", None),
        
        # 登录接口测试
        (f"{base_url}/api/login", "POST", {"username": "admin", "password": "123456"}),
        (f"{base_url}/api/login", "POST", {"username": "admin", "password": "wrong"}),
        
        # V1 API接口测试
        (f"{base_url}/api/v1/auth/login", "POST", {"username": "admin", "password": "123456"}),
        (f"{base_url}/api/v1/system/health-check", "GET", None),
        (f"{base_url}/api/v1/docs", "GET", None),
    ]
    
    success_count = 0
    total_count = len(tests)
    
    for url, method, data in tests:
        success, response = test_api(url, method, data)
        if success:
            success_count += 1
    
    print(f"\n{'='*80}")
    print(f"测试完成: {success_count}/{total_count} 个接口正常")
    print("="*80)
    
    # 如果基础登录成功，测试更多接口
    login_success, login_response = test_api(f"{base_url}/api/login", "POST", {"username": "admin", "password": "123456"})
    
    if login_success:
        try:
            token = login_response.json()['data']['token']
            headers = {'Authorization': f'Bearer {token}'}
            
            print(f"\n使用Token测试更多接口...")
            
            # 测试需要认证的接口
            auth_tests = [
                (f"{base_url}/api/monitors", "GET", None),
            ]
            
            for url, method, data in auth_tests:
                test_api(url, method, data, headers)
                
        except Exception as e:
            print(f"Token测试失败: {e}")

if __name__ == "__main__":
    main()
