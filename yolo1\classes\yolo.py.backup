# -*- coding: utf-8 -*-
# <AUTHOR> pan
# @Description : 封装了一些函数
# @Date : 2023年8月8日09:45:23

import supervision as sv
from ultralytics import YOLO
from ultralytics.data.loaders import LoadStreams

from ultralytics.engine.predictor import BasePredictor
from ultralytics.utils import DEFAULT_CFG, SETTINGS
from ultralytics.utils.torch_utils import smart_inference_mode
from ultralytics.utils.files import increment_path
from ultralytics.cfg import get_cfg
from ultralytics.utils.checks import check_imshow

from PySide6.QtCore import Signal, QObject



from pathlib import Path
import datetime
import numpy as np
import time
import cv2
import math
import torch
import threading
import random

# 导入科技轨迹效果模块
from classes.tech_trail import TechTrail

from classes.paint_trail import draw_trail
from classes.lane_heatmap import init_heatmap, update_heatmap, generate_heatmap_visualization, get_accumulation_heatmap, reset_heatmap
from utils.main_utils import check_path
from classes.speed_estimator import SpeedEstimator
from classes.violation_detector import ViolationDetector
import threading
import time
from queue import Queue

x_axis_time_graph = []
y_axis_count_graph = []
video_id_count = 0


class YoloPredictor(BasePredictor, QObject):
    yolo2main_trail_img = Signal(np.ndarray)  # 轨迹图像信号
    yolo2main_box_img = Signal(np.ndarray)  # 绘制了标签与锚框的图像的信号
    yolo2main_status_msg = Signal(str)  # 检测/暂停/停止/测试完成等信号
    yolo2main_fps = Signal(str)  # fps

    yolo2main_labels = Signal(dict)  # 检测到的目标结果（每个类别的数量）
    yolo2main_progress = Signal(int)  # 进度条
    yolo2main_class_num = Signal(int)  # 当前帧类别数
    yolo2main_target_num = Signal(int)  # 当前帧目标数


    def __init__(self, cfg=DEFAULT_CFG, overrides=None):
        super(YoloPredictor, self).__init__()
        QObject.__init__(self)

        try:
            self.args = get_cfg(cfg, overrides)
        except:
            pass
        project = self.args.project or Path(SETTINGS['runs_dir']) / self.args.task
        name = f'{self.args.mode}'
        self.save_dir = increment_path(Path(project) / name, exist_ok=self.args.exist_ok)
        self.done_warmup = False
        if self.args.show:
            self.args.show = check_imshow(warn=True)

        # GUI args
        self.used_model_name = None  # 使用过的检测模型名称
        self.new_model_name = None  # 新更改的模型

        self.source = ''  # 输入源str
        self.progress_value = 0  # 进度条的值

        self.stop_dtc = False  # 终止bool
        self.continue_dtc = True  # 暂停bool


        # config
        self.iou_thres = 0.45  # iou
        self.conf_thres = 0.25  # conf
        self.speed_thres = 0.01  # delay, ms （缓冲）

        self.save_res = False  # 保存MP4
        self.save_txt = False  # 保存txt
        self.save_res_path = "pre_result"
        self.save_txt_path = "pre_labels"

        self.show_labels = True  # 显示图像标签bool
        self.show_trace = True  # 显示图像轨迹bool

        # 热力图相关设置
        self.show_heatmap = False  # 显示热力图
        self.show_accumulated_heatmap = False  # 显示累积热力图
        self.heatmap_initialized = False  # 热力图是否已初始化

        # 速度估计相关设置
        self.show_speed = True  # 是否显示速度估计
        self.speed_estimator = None  # 速度估计器

        # 违规行为检测相关设置
        self.violation_detection_enabled = False  # 是否启用违规行为检测
        self.violation_detector = None  # 违规行为检测器实例

        # 运行时候的参数放这里
        self.start_time = None  # 拿来算FPS的计数变量
        self.count = None
        self.sum_of_count = None
        self.class_num = None
        self.total_frames = None
        self.lock_id = None

        # 初始化科技轨迹效果
        self.tech_trail = TechTrail()
        # 是否启用科技轨迹效果
        self.enable_tech_trail = True

        # 设置线条样式    厚度 & 缩放大小
        self.box_annotator = sv.BoxAnnotator(
            thickness=2
        )

        # 主窗口对象
        self.main_window = None

        # 多目标追踪相关属性
        self.multi_tracking = False
        self.multi_tracking_ids = []
        self.detected_objects = {}  # 存储检测到的对象信息
        self.tracked_objects = {}   # 存储正在追踪的对象信息
        
        # 行人检测模式标志
        self.pedestrian_detection_mode = False  # 是否启用行人检测模式
        self.pedestrian_callback = None  # 行人检测回调函数

    # 点击开始检测按钮后的检测事件
    @smart_inference_mode()  # 一个修饰器，用来开启检测模式：如果torch>=1.9.0，则执行torch.inference_mode()，否则执行torch.no_grad()
    def run(self):
        self.yolo2main_status_msg.emit('正在加载模型...')
        LoadStreams.capture = ''
        self.count = 0                 # 拿来参与算FPS的计数变量
        self.start_time = time.time()  # 拿来算FPS的计数变量
        global video_id_count

        # 重置热力图数据
        self.reset_heatmap_data()

        # 检查保存路径
        if self.save_txt:
            check_path(self.save_txt_path)
        if self.save_res:
            check_path(self.save_res_path)

        model = self.load_yolo_model()

        # 获取数据源 （不同的类型获取不同的数据源）
        # 设置设备参数以利用CUDA
        import torch
        device = 'cuda' if torch.cuda.is_available() else 'cpu'

        # 使用指定设备进行推理
        iter_model = iter(
            model.track(source=self.source, show=False, stream=True, iou=self.iou_thres, conf=self.conf_thres, device=device))

        # 折线图数据初始化
        global x_axis_time_graph, y_axis_count_graph
        x_axis_time_graph = []
        y_axis_count_graph = []

        self.yolo2main_status_msg.emit('检测中...')

        # 使用OpenCV读取视频——获取进度条
        if 'mp4' in self.source or 'avi' in self.source or 'mkv' in self.source or 'flv' in self.source or 'mov' in self.source:
            cap = cv2.VideoCapture(self.source)
            self.total_frames = cap.get(cv2.CAP_PROP_FRAME_COUNT)
            cap.release()

        # 如果保存，则创建写入对象
        img_res, result, height, width = self.recognize_res(iter_model)
        fourcc = cv2.VideoWriter_fourcc(*'XVID')
        out = None  # 视频写出变量
        if self.save_res:
            out = cv2.VideoWriter(f'{self.save_res_path}/video_result_{video_id_count}.mp4', fourcc, 25,
                                  (width, height), True)  # 保存检测视频的路径

        # 开始死循环检测
        while True:
            try:
                # 暂停与开始
                if self.continue_dtc:
                    img_res, result, height, width = self.recognize_res(iter_model)
                    self.res_address(img_res, result, height, width, model, out)

                # 终止
                if self.stop_dtc:
                    if self.save_res:
                        if out:
                            out.release()
                            video_id_count += 1
                    self.source = None
                    self.yolo2main_status_msg.emit('检测终止')
                    self.release_capture()  # 这里是为了终止使用摄像头检测函数的线程，改了yolo源码
                    break


            # 检测截止（本地文件检测）
            except StopIteration:
                if self.save_res:
                    out.release()
                    video_id_count += 1
                    print('writing complete')
                self.yolo2main_status_msg.emit('检测完成')
                self.yolo2main_progress.emit(1000)
                cv2.destroyAllWindows()  # 单目标追踪停止！
                self.source = None

                break
        try:
            out.release()
        except:
            pass

    # 进行识别——并返回所有结果
    def res_address(self, img_res, result, height, width, model, out):
            # 复制一份
            img_box = np.copy(img_res)   # 右边的图（会绘制标签！） img_res是原图-不会受影响
            img_trail = np.copy(img_res) # 左边的图

            # 如果没有识别的：
            if result.boxes.id is None:
                # 目标都是0
                self.sum_of_count = 0
                self.class_num = 0
                labels_write = "暂未识别到目标！"
                # 即使没有目标也要确保右侧显示原图
                # img_box不做处理保持为原图
            # 如果有识别的
            else:
                # 直接使用supervision库创建检测结果
                detections = sv.Detections.from_yolov8(result)
                detections.tracker_id = result.boxes.id.cpu().numpy().astype(int)
                
                # 行人检测模式：只保留行人类别（class_id = 0）
                if hasattr(self, 'pedestrian_detection_enabled') and self.pedestrian_detection_enabled:
                    person_mask = detections.class_id == 0  # 只保留person类别
                    detections.xyxy = detections.xyxy[person_mask]
                    detections.confidence = detections.confidence[person_mask]
                    detections.class_id = detections.class_id[person_mask]
                    detections.tracker_id = detections.tracker_id[person_mask]

                # id 、位置、目标总数
                self.class_num = self.get_class_number(detections)  # 类别数
                id = detections.tracker_id  # id
                xyxy = detections.xyxy  # 位置
                self.sum_of_count = len(id)  # 目标总数

                # 初始化热力图（如果尚未初始化）
                if not self.heatmap_initialized:
                    init_heatmap(width, height)
                    self.heatmap_initialized = True

                # 更新热力图数据
                update_heatmap(xyxy, id)

                # 更新速度估计数据（如果启用）
                if self.show_speed and self.speed_estimator is not None:
                    for box, object_id in zip(xyxy, id):
                        x1, y1, x2, y2 = box
                        center_x = (x1 + x2) / 2
                        center_y = (y1 + y2) / 2
                        self.speed_estimator.update_position(object_id, (center_x, center_y))
                    # 计算所有目标的速度
                    self.speed_estimator.calculate_speeds()

                # 更新检测到的对象信息（用于多目标追踪）
                self.update_detected_objects(detections, model)

                # 处理多目标追踪
                if self.multi_tracking and self.multi_tracking_ids:
                    self.process_multi_tracking(detections, img_res)

                # 画标签到图像上（并返回要写下的信息
                labels_write, img_box = self.creat_labels(detections, img_box, model)

            # 左侧图像处理逻辑（轨迹或热力图）
            if self.show_trace:
                img_trail = np.zeros((height, width, 3), dtype='uint8')  # 黑布
                grid_color = (255, 255, 255)
                line_width = 1
                grid_size = 100
                for y in range(0, height, grid_size):
                    cv2.line(img_trail, (0, y), (width, y), grid_color, line_width)
                for x in range(0, width, grid_size):
                    cv2.line(img_trail, (x, 0), (x, height), grid_color, line_width)
                # 只有在有检测结果时才绘制轨迹
                if result.boxes.id is not None:
                    identities = id
                    draw_trail(img_trail, xyxy, model.model.names, id, identities)
            elif self.show_heatmap:
                # 生成实时热力图
                img_trail = generate_heatmap_visualization(show_title=True)
            elif self.show_accumulated_heatmap:
                # 生成累积热力图
                img_trail = get_accumulation_heatmap(show_title=True)
            else:
                img_trail = img_res  # 显示原图

            # 写入txt——存储labels里的信息
            if self.save_txt:
                with open(f'{self.save_txt_path}/result.txt', 'a') as f:
                    f.write('当前时刻屏幕信息:' +
                            str(labels_write) +
                            f'检测时间: {datetime.datetime.now().strftime("%Y-%m-%d-%H:%M:%S")}' +
                            f' 路段通过的目标总数: {self.sum_of_count}')
                    f.write('\n')

            # 预测视频写入本地
            if self.save_res:
                out.write(img_box)

            # 添加 折线图数据
            now = datetime.datetime.now()
            new_time = now.strftime("%Y-%m-%d %H:%M:%S")
            if new_time not in x_axis_time_graph:  # 防止同一秒写入
                x_axis_time_graph.append(new_time)
                y_axis_count_graph.append(self.sum_of_count)


            # 抠锚框里的图  （单目标追踪）
            if self.lock_id is not None:
                self.lock_id = int(self.lock_id)
                # 使用高级目标追踪器
                try:
                    if hasattr(self, 'main_window') and hasattr(self.main_window, 'target_tracker'):
                        # 使用高级目标追踪器
                        enhanced_frame = self.main_window.target_tracker.update_frame(detections, img_res)
                        # 如果正在追踪并且增强帧不为空，则使用增强帧
                        if self.main_window.target_tracker.is_tracking and enhanced_frame is not None:
                            img_box = enhanced_frame
                except Exception as e:
                    print(f"Error in target tracking: {e}")
                    pass

            # 传递信号给主窗口
            self.emit_res(img_trail, img_box)

            # 更新碰撞检测（如果启用）
            if hasattr(self, 'main_window') and self.main_window:
                try:
                    # 准备追踪对象数据用于碰撞检测
                    tracked_objects = []
                    if hasattr(result, 'boxes') and result.boxes is not None and result.boxes.id is not None:
                        boxes = result.boxes.xyxy.cpu().numpy()
                        track_ids = result.boxes.id.cpu().numpy()
                        confidences = result.boxes.conf.cpu().numpy()

                        for i, (box, track_id, conf) in enumerate(zip(boxes, track_ids, confidences)):
                            tracked_obj = {
                                'track_id': int(track_id),
                                'bbox': box.tolist(),
                                'confidence': float(conf)
                            }
                            tracked_objects.append(tracked_obj)

                    # 传递给主窗口的碰撞检测更新方法
                    self.main_window.update_collision_detection(tracked_objects)

                except Exception as e:
                    print(f"碰撞检测更新错误: {str(e)}")

            # 处理行人检测（如果启用）
            if hasattr(self, 'pedestrian_detection_mode') and self.pedestrian_detection_mode:
                try:
                    self.process_pedestrian_detection(result, img_box)
                except Exception as e:
                    print(f"行人检测处理错误: {str(e)}")

    # 识别结果处理
    def recognize_res(self, iter_model):
            # 检测 ---然后获取有用的数据
            result = next(iter_model)  # 这里是检测的核心，每次循环都会检测一帧图像,可以自行打印result看看里面有哪些key可以用
            img_res = result.orig_img  # 原图
            height, width, _ = img_res.shape

            return img_res, result, height, width

    # 单目标检测窗口开启
    def open_target_tracking(self, detections, img_res):
        try:
            # 使用高级目标追踪器
            if hasattr(self, 'main_window') and hasattr(self.main_window, 'target_tracker'):
                # 使用高级目标追踪器
                return
            else:
                # 使用旧版目标追踪器
                result_cropped = self.single_object_tracking(detections, img_res)
                cv2.imshow(f'OBJECT-ID:{self.lock_id}', result_cropped)
                cv2.moveWindow(f'OBJECT-ID:{self.lock_id}', 0, 0)
                # press esc to quit
                if cv2.waitKey(5) & 0xFF == 27:
                    self.lock_id = None
                    cv2.destroyAllWindows()
        except Exception as e:
            print(f"Error in legacy target tracking: {e}")
            cv2.destroyAllWindows()
            pass

    # 单目标跟踪
    def single_object_tracking(self, detections, img_box):
        store_xyxy_for_id = {}
        for xyxy, id in zip(detections.xyxy, detections.tracker_id):
            store_xyxy_for_id[id] = xyxy
            mask = np.zeros_like(img_box)
        try:
            if self.lock_id not in detections.tracker_id:
                cv2.destroyAllWindows()
                self.lock_id = None
            x1, y1, x2, y2 = int(store_xyxy_for_id[self.lock_id][0]), int(store_xyxy_for_id[self.lock_id][1]), int(
                store_xyxy_for_id[self.lock_id][2]), int(store_xyxy_for_id[self.lock_id][3])
            cv2.rectangle(mask, (x1, y1), (x2, y2), (255, 255, 255), -1)
            result_mask = cv2.bitwise_and(img_box, mask)
            result_cropped = result_mask[y1:y2, x1:x2]
            result_cropped = cv2.resize(result_cropped, (256, 256))
            return result_cropped

        except:
            cv2.destroyAllWindows()
            pass

    # 信号发送区（性能优化版本）
    def emit_res(self, img_trail, img_box):
        # 性能优化：减少不必要的延迟
        if self.speed_thres > 0:
            time.sleep(self.speed_thres/1000)  # 缓冲

        # 性能优化：批量发送信号，减少GUI更新频率
        # 轨迹图像（左边）
        self.yolo2main_trail_img.emit(img_trail)
        # 标签图（右边）
        self.yolo2main_box_img.emit(img_box)

        # 性能优化：减少数值更新频率，每5帧更新一次
        if self.count % 5 == 0:
            # 总类别数量 、 总目标数
            self.yolo2main_class_num.emit(self.class_num)
            self.yolo2main_target_num.emit(self.sum_of_count)
        # 进度条
        if '0' in self.source or 'rtsp' in self.source:
            self.yolo2main_progress.emit(0)
        else:
            self.progress_value = int(self.count / self.total_frames * 1000)
            self.yolo2main_progress.emit(self.progress_value)
        # 性能优化：减少FPS计算频率，避免GUI卡顿
        self.count += 1
        if self.count % 10 == 0 and self.count >= 10:  # 每10帧计算一次FPS，减少GUI更新频率
            fps_value = int(10 / (time.time() - self.start_time))
            self.yolo2main_fps.emit(f"{fps_value} FPS")
            self.start_time = time.time()

    # 加载模型
    def load_yolo_model(self):
        if self.used_model_name != self.new_model_name:
            self.setup_model(self.new_model_name)
            self.used_model_name = self.new_model_name

        # 创建YOLO模型
        model = YOLO(self.new_model_name)

        # 检查CUDA是否可用并设置设备
        import torch
        if torch.cuda.is_available():
            model.to('cuda')  # 将模型移至CUDA设备
            print(f"CUDA available: {torch.cuda.get_device_name(0)}")
            self.yolo2main_status_msg.emit(f"使用CUDA加速: {torch.cuda.get_device_name(0)}")
        else:
            print("CUDA不可用，使用CPU推理")
            self.yolo2main_status_msg.emit("CUDA不可用，使用CPU推理")

        return model

    # 画标签到图像上
    def creat_labels(self, detections, img_box, model):
        # 性能优化：延迟初始化，只在需要时创建
        if self.show_speed and self.speed_estimator is None:
            self.speed_estimator = SpeedEstimator(fps=30, pixels_per_meter=10, min_speed=80)

        if self.violation_detection_enabled and self.violation_detector is None:
            try:
                self.violation_detector = ViolationDetector(speed_limit=120)
            except Exception as e:
                print(f"Error creating ViolationDetector: {str(e)}")
                self.violation_detection_enabled = False

        # 性能优化：直接使用numpy数组，避免重复转换
        if hasattr(detections, 'xyxy'):
            boxes = detections.xyxy
            confidences = detections.confidence if detections.confidence is not None else None
            class_ids = detections.class_id if detections.class_id is not None else None
            tracker_ids = detections.tracker_id if detections.tracker_id is not None else None
            num_detections = len(boxes)
        else:
            # 如果是列表格式，提取数据
            if detections:
                boxes = [det[1] for det in detections]
                confidences = [det[2] for det in detections]
                class_ids = [det[3] for det in detections]
                tracker_ids = [det[4] for det in detections]
                num_detections = len(detections)
            else:
                return [], img_box

        # 构建当前对象列表（性能优化版本）
        current_objects = {}

        # 性能优化：批量处理检测结果
        for i in range(num_detections):
            box = boxes[i]
            confidence = confidences[i] if confidences is not None else 0.0
            class_id = class_ids[i] if class_ids is not None else 0
            tracker_id = tracker_ids[i] if tracker_ids is not None else i

            if box is None:
                continue
            x1, y1, x2, y2 = box
            position = ((x1 + x2) / 2, (y1 + y2) / 2)

            # 更新速度估计器
            if self.show_speed and self.speed_estimator is not None:
                self.speed_estimator.update_position(tracker_id, position)

            # 如果启用了违规检测，更新轨迹信息
            if self.violation_detection_enabled and self.violation_detector is not None:
                speed = None
                if self.show_speed and self.speed_estimator is not None and tracker_id in self.speed_estimator.speeds:
                    speed = self.speed_estimator.speeds[tracker_id]

                # 更新对象信息
                current_objects[tracker_id] = {
                    "position": position,
                    "class_id": class_id,
                    "box": box,
                    "speed": speed
                }

                # 更新违规检测器中的轨迹
                self.violation_detector.update_trajectory(
                    tracker_id, position, time.time(), class_id, speed)

        # 检测违规行为并绘制标记
        if self.violation_detection_enabled and self.violation_detector is not None:
            try:
                speeds = {id: obj["speed"] for id, obj in current_objects.items()
                        if "speed" in obj and obj["speed"] is not None}
                self.violation_detector.detect_violations(img_box, current_objects, speeds)
                img_box = self.violation_detector.draw_violations(img_box, detections, self.speed_estimator)
            except Exception as e:
                print(f"Error in violation detection: {str(e)}")

        # 要画出来的信息（性能优化版本）
        labels_draw = []
        for i in range(num_detections):
            confidence = confidences[i] if confidences is not None else 0.0
            class_id = class_ids[i] if class_ids is not None else 0
            tracker_id = tracker_ids[i] if tracker_ids is not None else i
            # 行人检测模式：只显示行人相关信息
            if hasattr(self, 'pedestrian_detection_enabled') and self.pedestrian_detection_enabled:
                if class_id == 0:  # 只处理行人
                    label = f"行人 ID: {tracker_id} 置信度: {confidence:.2f}"
                    labels_draw.append(label)
            else:
                # 原有的车辆检测逻辑
                # 检查是否为被追踪的目标
                if self.multi_tracking and tracker_id in self.multi_tracking_ids:
                    # 被追踪的车辆只显示ID
                    label = f"ID: {tracker_id}"
                else:
                    # 普通车辆显示完整信息
                    label = f"ID: {tracker_id} {model.model.names[class_id]}"
                    # 如果显示速度估计，并且速度估计器不为空，并且有目标
                    if self.show_speed and self.speed_estimator is not None and tracker_id in self.speed_estimator.speeds:
                        # 只对车辆显示速度
                        if class_id in [2, 3, 5, 7]:  # 汽车、卡车等
                            speed = self.speed_estimator.speeds[tracker_id]
                            # 根据速度设置颜色标记，超过110红色警告
                            if speed > 110:
                                label = f"ID: {tracker_id} {model.model.names[class_id]} ⚠️{speed:.1f}km/h"
                            else:
                                label = f"ID: {tracker_id} {model.model.names[class_id]} {speed:.1f}km/h"
                labels_draw.append(label)

        # 存储labels里的信息（用于保存到文件）（性能优化版本）
        labels_write = []
        for i in range(num_detections):
            confidence = confidences[i] if confidences is not None else 0.0
            class_id = class_ids[i] if class_ids is not None else 0
            tracker_id = tracker_ids[i] if tracker_ids is not None else i
            # 行人检测模式：只记录行人信息
            if hasattr(self, 'pedestrian_detection_enabled') and self.pedestrian_detection_enabled:
                if class_id == 0:  # 只处理行人
                    label = f"行人ID: {tracker_id} 置信度: {confidence:0.2f}"
                    labels_write.append(label)
            else:
                # 原有的车辆检测逻辑
                label = f"目标ID: {tracker_id} 目标类别: {class_id} 置信度: {confidence:0.2f}"
                # 添加速度信息
                if self.show_speed and self.speed_estimator is not None and tracker_id in self.speed_estimator.speeds:
                    if class_id in [2, 3, 5, 7]:  # 汽车、卡车等
                        speed = self.speed_estimator.speeds[tracker_id]
                        label += f" 速度: {speed:.1f}km/h"
                        # 添加违规信息（如果有）
                        if self.violation_detection_enabled and self.violation_detector is not None:
                            if tracker_id in self.violation_detector.violation_records:
                                violations = [vtype for _, vtype, _ in self.violation_detector.violation_records[tracker_id]]
                                if violations:
                                    label += f" 违规: {','.join(violations)}"
                labels_write.append(label)

        # 性能优化：直接使用OpenCV绘制，避免supervision的开销
        # 绘制检测框
        for i in range(num_detections):
            box = boxes[i]
            x1, y1, x2, y2 = map(int, box)
            # 绘制边界框
            cv2.rectangle(img_box, (x1, y1), (x2, y2), (0, 255, 0), 2)

            # 绘制标签（如果有的话）
            if i < len(labels_draw):
                label = labels_draw[i]
                # 绘制标签背景
                (text_width, text_height), _ = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)
                cv2.rectangle(img_box, (x1, y1-text_height-10), (x1+text_width, y1), (0, 255, 0), -1)
                # 绘制标签文本
                cv2.putText(img_box, label, (x1, y1-5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)

        # 如果启用了多目标追踪，为追踪的目标添加特殊标记
        if self.multi_tracking and self.multi_tracking_ids:
            self.highlight_tracked_targets(img_box, detections)

        # 如果启用科技轨迹效果（性能优化版本）
        if self.enable_tech_trail:
            # 批量处理轨迹更新
            for i in range(num_detections):
                class_id = class_ids[i] if class_ids is not None else 0
                tracker_id = tracker_ids[i] if tracker_ids is not None else i
                # 只为车辆类型添加轨迹(例如:汽车、卡车、公交车、摩托车)
                if class_id in [2, 3, 5, 7]:
                    # 更新轨迹点
                    self.tech_trail.update_track(tracker_id, boxes[i])

            # 绘制科技感轨迹效果
            img_box = self.tech_trail.draw_trails(img_box)

        return labels_write, img_box

    # 获取类别数
    def get_class_number(self, detections):
        class_num_arr = []
        for each in detections.class_id:
            if each not in class_num_arr:
                class_num_arr.append(each)
        return len(class_num_arr)

    # 释放摄像头
    def release_capture(self):
        LoadStreams.capture = 'release'  # 这里是为了终止使用摄像头检测函数的线程，改了yolo源码

    # 重置热力图数据
    def reset_heatmap_data(self):
        from classes.lane_heatmap import reset_heatmap
        reset_heatmap()
        # 确保热力图状态被正确设置
        self.show_heatmap = False
        self.show_accumulated_heatmap = False

    # 设置主窗口对象
    def set_main_window(self, main_window):
        self.main_window = main_window
    
    # 设置行人检测模式
    def set_pedestrian_detection_mode(self, enabled, callback=None):
        """设置行人检测模式
        Args:
            enabled (bool): 是否启用行人检测模式
            callback (function): 行人检测结果回调函数
        """
        self.pedestrian_detection_mode = enabled
        self.pedestrian_callback = callback
        if enabled:
            print("已启用行人检测模式")
        else:
            print("已关闭行人检测模式")

    # 更新检测到的对象信息
    def update_detected_objects(self, detections, model):
        """更新检测到的对象信息，用于多目标追踪"""
        try:
            # 清空之前的检测对象
            self.detected_objects.clear()

            # 优化：直接使用numpy数组操作，避免重复转换
            if hasattr(detections, 'xyxy'):
                class_ids = detections.class_id if detections.class_id is not None else [0] * len(detections.xyxy)
                tracker_ids = detections.tracker_id if detections.tracker_id is not None else list(range(len(detections.xyxy)))
                detection_pairs = list(zip(class_ids, tracker_ids))
            else:
                detection_pairs = [(class_id, tracker_id) for _, _, _, class_id, tracker_id in detections]

            # 遍历当前检测结果
            for class_id, tracker_id in detection_pairs:
                class_name = model.model.names[class_id]

                # 根据检测模式过滤对象类型
                should_record = False
                if self.pedestrian_detection_mode:
                    # 行人检测模式：只记录行人
                    should_record = class_name == 'person'
                else:
                    # 车辆检测模式：只记录车辆类型的对象
                    should_record = class_name in ['car', 'truck', 'bus', 'motorcycle']
                
                if should_record:
                    self.detected_objects[tracker_id] = {
                        'id': tracker_id,
                        'class': class_name,
                        'class_zh': self._get_chinese_class_name(class_name),
                        'bbox': detections.xyxy[i],
                        'confidence': detections.confidence[i] if hasattr(detections, 'confidence') else 0.0
                    }

        except Exception as e:
            print(f"更新检测对象信息时出错: {str(e)}")

    def process_pedestrian_detection(self, result, frame):
        """处理行人检测结果"""
        if not hasattr(self, 'pedestrian_callback') or not self.pedestrian_callback:
            return

        try:
            # 提取检测结果
            detections = []

            if hasattr(result, 'boxes') and result.boxes is not None:
                boxes = result.boxes.xyxy.cpu().numpy()
                confidences = result.boxes.conf.cpu().numpy()
                class_ids = result.boxes.cls.cpu().numpy().astype(int)

                # 过滤出行人检测结果 (class_id = 0 表示person)
                for i, (box, conf, cls_id) in enumerate(zip(boxes, confidences, class_ids)):
                    if cls_id == 0:  # person类别
                        detection = {
                            'bbox': box.tolist(),
                            'confidence': float(conf),
                            'class_id': int(cls_id),
                            'class_name': 'person'
                        }
                        detections.append(detection)

            # 调用回调函数处理行人检测结果
            if detections:
                self.pedestrian_callback(detections, frame)

        except Exception as e:
            print(f"行人检测处理错误: {str(e)}")

    def process_multi_tracking(self, detections, img_res):
        """处理多目标追踪"""
        try:
            # 遍历所有要追踪的目标ID
            for target_id in self.multi_tracking_ids:
                # 检查目标是否在当前检测结果中
                if target_id in detections.tracker_id:
                    # 找到目标在检测结果中的索引
                    target_index = list(detections.tracker_id).index(target_id)

                    # 获取目标的边界框
                    bbox = detections.xyxy[target_index]
                    x1, y1, x2, y2 = map(int, bbox)

                    # 提取目标区域
                    target_region = img_res[y1:y2, x1:x2]

                    if target_region.size > 0:
                        # 更新追踪对象信息
                        self.tracked_objects[target_id] = {
                            'bbox': bbox,
                            'region': target_region,
                            'last_seen': time.time()
                        }

                        # 如果主窗口存在多目标追踪对话框，更新预览
                        if (hasattr(self, 'main_window') and
                            hasattr(self.main_window, 'multi_tracking_dialog') and
                            self.main_window.multi_tracking_dialog and
                            self.main_window.multi_tracking_dialog.isVisible()):

                            # 调用主窗口的更新预览方法
                            self.main_window.update_multi_tracking_preview(target_id, target_region)

        except Exception as e:
            print(f"处理多目标追踪时出错: {str(e)}")

    def highlight_tracked_targets(self, img_box, detections):
        """为正在追踪的目标添加特殊高亮标记"""
        try:
            # 优化：直接处理数据，避免重复转换
            if hasattr(detections, 'xyxy'):
                boxes = detections.xyxy
                tracker_ids = detections.tracker_id if detections.tracker_id is not None else list(range(len(boxes)))

                for i, (bbox, tracker_id) in enumerate(zip(boxes, tracker_ids)):
                    if tracker_id in self.multi_tracking_ids:
                        # 获取目标的边界框
                        x1, y1, x2, y2 = map(int, bbox)
            else:
                # detections已经是列表格式
                for i, (_, bbox, _, _, tracker_id) in enumerate(detections):
                    if tracker_id in self.multi_tracking_ids:
                        # 获取目标的边界框
                        x1, y1, x2, y2 = map(int, bbox)

                    # 绘制特殊的追踪边框（更粗、更亮的颜色）
                    # 使用渐变色边框效果
                    cv2.rectangle(img_box, (x1-4, y1-4), (x2+4, y2+4), (0, 255, 255), 3)  # 青色外框
                    cv2.rectangle(img_box, (x1-2, y1-2), (x2+2, y2+2), (0, 255, 0), 2)    # 绿色内框

                    # 在四个角落添加特殊标记
                    corner_size = 15
                    corner_thickness = 3
                    corner_color = (0, 255, 255)

                    # 左上角
                    cv2.line(img_box, (x1-4, y1-4), (x1-4+corner_size, y1-4), corner_color, corner_thickness)
                    cv2.line(img_box, (x1-4, y1-4), (x1-4, y1-4+corner_size), corner_color, corner_thickness)

                    # 右上角
                    cv2.line(img_box, (x2+4, y1-4), (x2+4-corner_size, y1-4), corner_color, corner_thickness)
                    cv2.line(img_box, (x2+4, y1-4), (x2+4, y1-4+corner_size), corner_color, corner_thickness)

                    # 左下角
                    cv2.line(img_box, (x1-4, y2+4), (x1-4+corner_size, y2+4), corner_color, corner_thickness)
                    cv2.line(img_box, (x1-4, y2+4), (x1-4, y2+4-corner_size), corner_color, corner_thickness)

                    # 右下角
                    cv2.line(img_box, (x2+4, y2+4), (x2+4-corner_size, y2+4), corner_color, corner_thickness)
                    cv2.line(img_box, (x2+4, y2+4), (x2+4, y2+4-corner_size), corner_color, corner_thickness)

                    # 添加脉冲效果（可选）
                    pulse_intensity = int(abs(np.sin(time.time() * 3)) * 100)  # 0-100的脉冲强度
                    if pulse_intensity > 50:
                        cv2.rectangle(img_box, (x1-6, y1-6), (x2+6, y2+6), (255, 255, 0), 1)

        except Exception as e:
            print(f"高亮追踪目标时出错: {str(e)}")

    def _get_chinese_class_name(self, class_name):
        """获取中文类别名称"""
        class_mapping = {
            'car': '小汽车',
            'truck': '货车',
            'bus': '客车',
            'motorcycle': '摩托车',
            'person': '行人'
        }
        return class_mapping.get(class_name, class_name)

    # 设置模型路径
    def setup_model(self, model_path):
        """设置模型路径并处理相关配置"""
        self.new_model_name = model_path
        # 判断是否为TensorRT引擎文件
        if model_path.endswith('.engine'):
            print(f"Loading TensorRT engine: {model_path}")
            self.yolo2main_status_msg.emit(f"加载TensorRT引擎: {model_path}")
        else:
            print(f"Loading model: {model_path}")
            self.yolo2main_status_msg.emit(f"加载模型: {model_path}")

    # YOLO处理函数，将当前帧传递给TargetTracker进行处理
    def predict(self):
        # 获取模型
        if self.custom:
            self.model = YOLO(self.weights)
            self.model.fuse()
        else:
            self.model = YOLO(self.weights)

        # 更新模型配置
        self.model.conf = self.conf
        self.model.iou = self.iou

        # 设置数据源
        source = str(self.source)

        # 创建文件保存路径
        save_dir = check_path(Path(self.project) / self.name)
        self.txt_path = str(save_dir / 'labels')

        # 是否有运行的权限
        is_impossible = not check_imshow(warn=True)

        # 初始化锁定的目标ID
        self.lock_id = None

        # 初始化检测变量
        if source.isnumeric() or source.startswith(('rtsp://', 'rtmp://', 'http://', 'https://')):
            is_file = False
            webcam = True
        else:
            is_file = True
            webcam = False

        screen_size = (self.img_size, self.img_size)
        stride = self.model.stride
        self.vid_path, self.vid_writer = None, None

        # 读取摄像头帧
        if webcam:
            self.source = eval(source) if source.isnumeric() else source
            self.cap = cv2.VideoCapture(self.source)
            self.cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc(*'MJPG'))
            self.fps = self.cap.get(cv2.CAP_PROP_FPS)

        # 初始化检测对象
        self.dataset = LoadStreams(source, img_size=self.img_size, stride=stride, auto=True)[0]

        # 轨迹可视化
        if not hasattr(self, 'old_gray'):
            self.old_gray = None
            self.old_points = None
            self.mask = None
            self.trail_pointsets = []

        # 热力图初始化
        self.heatmap, self.accumulated_heatmap = init_heatmap()

        # 首帧信息
        self.thread_main_start.emit()

        # 准备进行推理
        prev_time = 0
        curr_time = 0
        # 记录处理的帧数
        curr_frame = 0
        self.seen = 0

        # 检测开始
        self.yolo2main_status_msg.emit('检测中...')

        # 默认检测为启动状态
        self.stop_dtc = False
        is_dtc_fin = False

        # 生成字节流（视频）
        gen = enumerate(self.dataset)

        # 开始检测帧
        while True:
            try:
                # 如果按下【终止】按钮
                if self.stop_dtc:
                    # 告知主线程，并终止帧检测循环
                    self.yolo2main_status_msg.emit('检测终止')
                    break

                # 主动暂停
                if self.pause:
                    time.sleep(0.5)
                    continue

                # 获取当前帧
                detect_frame = next(gen)
                path, im, im0s, vid_cap, s = detect_frame[1]

                # 获取当前帧
                curr_frame = detect_frame[0]

                # 如果该帧为视频的最后一帧
                if is_file and curr_frame == len(self.dataset) - 1:
                    is_dtc_fin = True

                # 视频进度条
                if is_file:
                    video_percent = curr_frame / (len(self.dataset) - 1) * 100
                    # 此处向main主线程发送进度条信号
                    self.yolo2main_progress.emit(video_percent)

                # 原始图像帧
                im0 = im0s.copy()

                # 检测帧
                results = self.model(im, self.size)

                # 创建标签
                det = results[0].boxes.data

                # 处理所有检测结果
                pred_classes = det[:, 5].cpu().numpy().astype(int) if len(det) else []  # 所有预测的类别
                pred_bboxes = det[:, :4].cpu().numpy() if len(det) else []  # 所有预测的边界框
                pred_confs = det[:, 4].cpu().numpy() if len(det) else []  # 所有预测的置信度

                # 创建标签
                labels = []
                for i, (box, cls, conf) in enumerate(zip(pred_bboxes, pred_classes, pred_confs)):
                    x1, y1, x2, y2 = box
                    cls_id = int(cls)
                    cls_name = self.model.names[cls_id]
                    label = {'class': cls_name, 'cx': (x1 + x2) / 2, 'cy': (y1 + y2) / 2,
                            'width': (x2 - x1), 'height': (y2 - y1), 'conf': float(conf)}
                    labels.append(label)

                # 创建检测用图像 - 用于前端显示检测结果与标签
                annotator = sv.BoxAnnotator(
                    thickness=1
                )

                # 检测到物体
                if len(det):
                    # 目标类别ID与数量统计
                    c_num = 0                   # 检测到的类别数量
                    self.current_labels = {}    # 检测到的目标字典

                    # 统计所有类别数量（根据检测模式过滤）
                    for c in pred_classes:
                        c_name = self.model.names[int(c)]
                        
                        # 根据检测模式过滤计数的类别
                        should_count = False
                        if self.pedestrian_detection_mode:
                            # 行人检测模式：只计数行人
                            should_count = c_name == 'person'
                        else:
                            # 车辆检测模式：只计数车辆类型
                            should_count = c_name in ['car', 'truck', 'bus', 'motorcycle']
                        
                        if should_count:
                            if c_name not in self.current_labels.keys():
                                self.current_labels[c_name] = 1
                                c_num = c_num + 1
                            else:
                                self.current_labels[c_name] += 1

                    # 发送当前的检测目标字典到主线程
                    self.yolo2main_labels.emit(self.current_labels)
                    # 发送当前的检测目标类别数到主线程
                    self.yolo2main_class_num.emit(c_num)
                    # 发送当前的检测目标数(总数)到主线程
                    self.yolo2main_target_num.emit(len(det))

                    # 标注图像
                    if self.show_labels or self.lock_id is not None:
                        # 创建标签
                        result_frame = im0.copy()

                        # 绘制所有检测框
                        for i, (box, cls, conf) in enumerate(zip(pred_bboxes, pred_classes, pred_confs)):
                            # 获取类别名称
                            cls_id = int(cls)
                            cls_name = self.model.names[cls_id]

                            # 根据检测模式过滤显示的类别
                            should_display = False
                            if self.pedestrian_detection_mode:
                                # 行人检测模式：只显示行人
                                should_display = cls_name == 'person'
                            else:
                                # 车辆检测模式：只显示车辆类别
                                should_display = cls_name in ['car', 'truck', 'bus', 'motorcycle']
                            
                            if not should_display:
                                continue

                            # 获取边界框与置信度
                            x1, y1, x2, y2 = box

                            # 设置随机颜色 - 根据类别ID
                            box_color = [(cls_id * 50) % 255, (cls_id * 100) % 255, (cls_id * 150) % 255]

                            # 绘制边界框
                            cv2.rectangle(result_frame, (int(x1), int(y1)), (int(x2), int(y2)), box_color, 2)

                            # 是否锁定特定ID进行追踪
                            if self.lock_id is not None and self.lock_id == i:
                                # 特殊标记锁定的目标
                                cv2.rectangle(result_frame, (int(x1)-5, int(y1)-5), (int(x2)+5, int(y2)+5), [0, 255, 0], 3)
                                # 将当前帧传递给TargetTracker进行处理
                                if hasattr(self, 'target_tracker_callback'):
                                    self.target_tracker_callback(im0, [(int(x1), int(y1)), (int(x2), int(y2))], i)

                            # 显示ID和类别
                            label_text = f"ID:{i} {cls_name}:{conf:.2f}"
                            cv2.putText(result_frame, label_text, (int(x1), int(y1) - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, box_color, 2)

                        # 性能优化：图像发送已在emit_res中处理，避免重复发送
                        pass
                    else:
                        pass
                else:
                    # 性能优化：图像发送已在emit_res中处理，避免重复发送
                    pass

                # 更新轨迹
                if self.show_trace:
                    # 绘制轨迹
                    trail_img, self.old_gray, self.old_points, self.mask, self.trail_pointsets = draw_trail(
                        im0,
                        self.old_gray,
                        self.old_points,
                        self.mask,
                        self.trail_pointsets
                    )
                    # 性能优化：避免重复发送，图像发送统一在emit_res中处理
                    # self.yolo2main_trail_img.emit(trail_img)

                # 更新热力图
                if self.show_heatmap or self.show_accumulated_heatmap:
                    if len(det):
                        # 更新热力图数据
                        update_heatmap(pred_bboxes, self.heatmap, self.accumulated_heatmap)

                        # 根据热力图显示设置生成不同的可视化效果
                        if self.show_heatmap:
                            # 生成实时热力图
                            heatmap_vis = generate_heatmap_visualization(self.heatmap, im0)
                            # 性能优化：避免重复发送
                            # self.yolo2main_trail_img.emit(heatmap_vis)
                        elif self.show_accumulated_heatmap:
                            # 生成累积热力图
                            acc_heatmap_vis = get_accumulation_heatmap(self.accumulated_heatmap, im0)
                            # 性能优化：避免重复发送
                            # self.yolo2main_trail_img.emit(acc_heatmap_vis)

                # 不显示轨迹和热力图时，发送空帧
                if not self.show_trace and not self.show_heatmap and not self.show_accumulated_heatmap:
                    # 性能优化：避免重复发送，统一在emit_res中处理
                    pass

                # 性能优化：移除重复的FPS计算，避免GUI卡顿
                # FPS计算已在res_address函数中处理
                self.seen += 1

                # 检测完成
                if is_dtc_fin:
                    self.yolo2main_status_msg.emit('检测完成')
                    self.stop_dtc = True

            except StopIteration:  # 遍历完所有图片或者视频帧
                self.yolo2main_status_msg.emit('检测完成')
                self.stop_dtc = True

            except Exception as e:
                print(f'检测过程中出现错误: {e}')
                self.yolo2main_status_msg.emit(f'检测错误: {e}')
                self.stop_dtc = True

        # 检测完毕，释放占用的资源
        if webcam:
            self.release_capture()
        cv2.destroyAllWindows()