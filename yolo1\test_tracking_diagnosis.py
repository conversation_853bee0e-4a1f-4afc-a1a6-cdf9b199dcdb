#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO多目标追踪系统问题诊断测试
"""

import sys
import numpy as np
import cv2
from PySide6.QtWidgets import QApplication, QMainWindow
from PySide6.QtCore import QTimer

# 模拟主窗口类
class MockMainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.yolo_predict = MockYOLOPredict()
        self.multi_tracking_dialog = None

    def get_available_targets(self):
        """获取当前可用的目标列表"""
        try:
            if hasattr(self, 'yolo_predict') and hasattr(self.yolo_predict, 'detected_objects'):
                targets = []
                for obj_id, obj_info in self.yolo_predict.detected_objects.items():
                    targets.append({
                        'id': obj_id,
                        'class': obj_info.get('class_zh', obj_info.get('class', '车辆')),
                        'confidence': int(obj_info.get('confidence', 0.0) * 100)
                    })
                return targets
            return []
        except Exception as e:
            print(f"获取可用目标时出错: {str(e)}")
            return []

    def update_multi_tracking_preview(self, target_id, target_image):
        """更新多目标追踪预览图像"""
        try:
            if (hasattr(self, 'multi_tracking_dialog') and
                self.multi_tracking_dialog and
                self.multi_tracking_dialog.isVisible()):

                # 调用对话框的图像更新方法
                self.multi_tracking_dialog.update_target_image(target_id, target_image)
                print(f"更新预览图像: 目标ID {target_id}")

        except Exception as e:
            print(f"更新多目标追踪预览时出错: {str(e)}")

# 模拟YOLO检测器类
class MockYOLOPredict:
    def __init__(self):
        self.detected_objects = {}
        self.multi_tracking = False
        self.multi_tracking_ids = []
        self.tracked_objects = {}

        # 模拟一些检测到的车辆
        self.detected_objects = {
            101: {
                'id': 101,
                'class': 'car',
                'class_zh': '小汽车',
                'confidence': 0.95
            },
            102: {
                'id': 102,
                'class': 'truck',
                'class_zh': '货车',
                'confidence': 0.88
            },
            103: {
                'id': 103,
                'class': 'car',
                'class_zh': '小汽车',
                'confidence': 0.92
            },
            104: {
                'id': 104,
                'class': 'bus',
                'class_zh': '客车',
                'confidence': 0.89
            },
            105: {
                'id': 105,
                'class': 'car',
                'class_zh': '小汽车',
                'confidence': 0.91
            }
        }
        print(f"模拟YOLO检测器初始化，检测到 {len(self.detected_objects)} 个目标")

def test_dialog_integration():
    """测试对话框与主窗口的集成"""
    app = QApplication(sys.argv)

    print("=== YOLO多目标追踪系统问题诊断 ===")

    # 创建模拟主窗口
    main_window = MockMainWindow()
    print(f"1. 主窗口创建完成，检测到 {len(main_window.yolo_predict.detected_objects)} 个目标")

    # 测试get_available_targets方法
    available_targets = main_window.get_available_targets()
    print(f"2. 可用目标获取测试: {len(available_targets)} 个目标")
    for target in available_targets:
        print(f"   - ID: {target['id']}, 类型: {target['class']}, 置信度: {target['confidence']}%")

    # 创建对话框
    from ui.dialog.optimized_multi_tracking_dialog import OptimizedMultiTrackingDialog
    dialog = OptimizedMultiTrackingDialog(parent=main_window)
    main_window.multi_tracking_dialog = dialog

    print(f"3. 对话框创建完成，parent设置: {dialog.parent() is not None}")
    print(f"4. 对话框可用目标数量: {len(dialog.available_targets)}")

    # 测试可用目标列表加载
    dialog.load_available_targets()
    print(f"5. 重新加载后可用目标数量: {len(dialog.available_targets)}")

    # 验证是否加载了真实数据
    if dialog.available_targets and dialog.available_targets[0]['id'] in [101, 102, 103, 104, 105]:
        print("✅ 成功加载真实检测数据")
    else:
        print("❌ 仍在使用模拟数据")

    # 测试添加目标功能
    print("\n=== 测试添加目标功能 ===")
    if dialog.available_targets:
        # 测试第一个目标
        first_target = dialog.available_targets[0]
        target_id = first_target['id']

        print(f"尝试添加目标 ID: {target_id}")

        # 模拟双击事件
        from PySide6.QtWidgets import QListWidgetItem
        from PySide6.QtCore import Qt

        item = QListWidgetItem(f"ID: {target_id}")
        item.setData(Qt.UserRole, target_id)

        # 测试双击添加
        old_count = len(dialog.tracked_targets)
        dialog.on_target_double_clicked(item)
        new_count = len(dialog.tracked_targets)

        print(f"双击添加结果: {old_count} -> {new_count}")

        # 测试按钮添加
        if len(dialog.available_targets) > 1:
            second_target = dialog.available_targets[1]
            dialog.target_id_input.setText(str(second_target['id']))

            old_count = len(dialog.tracked_targets)
            dialog.add_target()
            new_count = len(dialog.tracked_targets)

            print(f"按钮添加结果: {old_count} -> {new_count}")

    # 测试图像同步功能
    print("\n=== 测试图像同步功能 ===")
    if dialog.tracked_targets:
        # 创建测试图像
        test_images = []
        colors = [(100, 150, 200), (200, 100, 150), (150, 200, 100), (200, 200, 100)]

        for i, target_id in enumerate(dialog.tracked_targets[:2]):
            color = colors[i % len(colors)]
            test_image = np.zeros((100, 150, 3), dtype=np.uint8)
            test_image[:] = color

            # 绘制车辆形状
            cv2.rectangle(test_image, (20, 30), (130, 70), (255, 255, 255), -1)
            cv2.rectangle(test_image, (30, 40), (120, 60), (0, 0, 0), 2)
            cv2.putText(test_image, f"ID:{target_id}", (40, 55), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 2)

            test_images.append((target_id, test_image))

            # 测试图像更新
            try:
                dialog.update_target_image(target_id, test_image)
                print(f"图像更新成功: 目标ID {target_id}")
            except Exception as e:
                print(f"图像更新失败: 目标ID {target_id}, 错误: {str(e)}")

    # 显示对话框
    dialog.show()

    # 创建定时器来模拟实时更新
    def simulate_real_time_updates():
        if dialog.tracked_targets and len(test_images) > 0:
            # 随机选择一个目标更新图像
            import random
            target_id, test_image = random.choice(test_images)

            # 添加一些随机变化
            noise = np.random.randint(-20, 20, test_image.shape, dtype=np.int16)
            updated_image = np.clip(test_image.astype(np.int16) + noise, 0, 255).astype(np.uint8)

            try:
                main_window.update_multi_tracking_preview(target_id, updated_image)
            except Exception as e:
                print(f"实时更新失败: {str(e)}")

    # 启动定时器
    timer = QTimer()
    timer.timeout.connect(simulate_real_time_updates)
    timer.start(2000)  # 每2秒更新一次

    print("\n=== 诊断结果总结 ===")
    print(f"✅ 主窗口创建: 成功")
    print(f"✅ YOLO检测器模拟: 成功 ({len(main_window.yolo_predict.detected_objects)} 个目标)")
    print(f"✅ 对话框创建: 成功")
    print(f"✅ 父子关系建立: {'成功' if dialog.parent() is not None else '失败'}")
    print(f"✅ 可用目标加载: {'成功' if len(dialog.available_targets) > 0 else '失败'} ({len(dialog.available_targets)} 个)")
    print(f"✅ 添加目标功能: {'成功' if len(dialog.tracked_targets) > 0 else '失败'} ({len(dialog.tracked_targets)} 个)")
    print(f"✅ 图像同步功能: 测试中...")
    print(f"✅ 最大追踪限制: 4个目标")

    print("\n请在对话框中测试以下功能:")
    print("1. 双击可用目标列表中的项目")
    print("2. 选择目标后点击'添加目标'按钮")
    print("3. 在输入框中输入目标ID并添加")
    print("4. 观察智能预览区域的图像更新")
    print("5. 启动多目标追踪功能")

    return app.exec()

if __name__ == "__main__":
    test_dialog_integration()
