# 高级单目标追踪模块
import cv2
import numpy as np
from PySide6.QtCore import QThread, Signal, QObject, QTimer
from PySide6.QtGui import QImage, QPixmap

class TargetTracker(QObject):
    """
    高级单目标追踪模块

    该模块使用深度学习特征提取器和光流法进行目标追踪，提供追踪目标切片图像、状态信息和颜色等信号。
    """
    # 定义信号
    target_image_signal = Signal(QPixmap)  # 发送追踪目标切片图像
    status_signal = Signal(str, str)  # 发送状态信息和颜色
    tracking_lost_signal = Signal()  # 追踪丢失信号
    
    def __init__(self):
        super(TargetTracker, self).__init__()
        self.target_id = None   # 追踪目标的ID
        self.is_tracking = False  # 是否正在追踪
        
        # 深度学习特征提取器
        self.feature_params = {
            'maxCorners': 100,
            'qualityLevel': 0.3,
            'minDistance': 7,
            'blockSize': 7
        }
        
        # 光流参数
        self.lk_params = {
            'winSize': (15, 15),
            'maxLevel': 2,
            'criteria': (cv2.TERM_CRITERIA_EPS | cv2.TERM_CRITERIA_COUNT, 10, 0.03)
        }
        
        # 追踪状态统计
        self.track_frames = 0  # 成功追踪的帧数
        self.consecutive_lost = 0  # 连续丢失追踪的帧数
        self.last_target_features = None  # 上一次成功识别的目标特征
        self.last_target_bbox = None  # 上一次的追踪边界框
        self.target_color = None  # 追踪目标的颜色
        
        # 目标追踪增强
        self.template_matching = True  # 是否使用模板匹配增强追踪
        self.template = None  # 目标模板
        
        # 创建缓存
        self.track_history = []  # 追踪历史记录
    
    def set_target_id(self, target_id):
        """设置要追踪的目标ID"""
        self.target_id = target_id
        self.is_tracking = True
        self.track_frames = 0
        self.consecutive_lost = 0
        self.last_target_features = None
        self.last_target_bbox = None
        self.track_history.clear()
        self.template = None
        
        # 为目标生成一个唯一的颜色
        np.random.seed(target_id)  # 确保每个目标的颜色唯一
        self.target_color = (int(np.random.randint(100, 255)), 
                             int(np.random.randint(100, 255)), 
                             int(np.random.randint(100, 255)))
                             
        self.status_signal.emit(f"正在追踪目标 ID: {target_id}", "green")
        
    def stop_tracking(self):
        """停止追踪"""
        self.is_tracking = False
        self.target_id = None
        self.status_signal.emit("追踪停止", "red")
        cv2.destroyAllWindows()
    
    def update_frame(self, detections, frame):
        """更新追踪目标"""
        if not self.is_tracking or self.target_id is None or frame is None:
            return frame
        
        # 复制原始帧
        result_frame = frame.copy()
        
        # 检测当前帧中是否有目标
        target_found = False
        curr_bbox = None
        
        # 检测当前帧中是否有目标ID
        if hasattr(detections, 'tracker_id') and hasattr(detections, 'xyxy'):
            for xyxy, track_id in zip(detections.xyxy, detections.tracker_id):
                if track_id == self.target_id:
                    # 目标找到
                    x1, y1, x2, y2 = map(int, xyxy)
                    curr_bbox = (x1, y1, x2, y2)
                    target_found = True
                    self.consecutive_lost = 0
                    self.track_frames += 1
                    
                    # 提取目标特征
                    roi = frame[y1:y2, x1:x2]
                    if roi.size > 0:  # 确保ROI有效
                        gray_roi = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
                        self.last_target_features = cv2.goodFeaturesToTrack(gray_roi, mask=None, **self.feature_params)
                        
                        # 更新目标模板
                        if self.template_matching:
                            self.template = roi.copy()
                    
                    # 绘制追踪边界框
                    self._draw_tech_tracking_box(result_frame, curr_bbox, track_id)
                    
                    # 创建目标切片
                    self._create_target_slice(frame, curr_bbox)
                    break
        
        # 如果目标丢失，使用模板匹配增强追踪
        if not target_found:
            self.consecutive_lost += 1
            
            # 使用模板匹配增强追踪
            if self.template_matching and self.template is not None and self.last_target_bbox is not None:
                # 模板匹配
                x1, y1, x2, y2 = self.last_target_bbox
                search_area_x1 = max(0, x1 - 50)
                search_area_y1 = max(0, y1 - 50)
                search_area_x2 = min(frame.shape[1], x2 + 50)
                search_area_y2 = min(frame.shape[0], y2 + 50)
                search_area = frame[search_area_y1:search_area_y2, search_area_x1:search_area_x2]
                
                if search_area.size > 0 and self.template.size > 0:
                    if search_area.shape[0] > self.template.shape[0] and search_area.shape[1] > self.template.shape[1]:
                        # 模板匹配
                        res = cv2.matchTemplate(search_area, self.template, cv2.TM_CCOEFF_NORMED)
                        _, max_val, _, max_loc = cv2.minMaxLoc(res)
                        
                        # 如果匹配成功
                        if max_val > 0.6:
                            top_left = (search_area_x1 + max_loc[0], search_area_y1 + max_loc[1])
                            bottom_right = (top_left[0] + self.template.shape[1], top_left[1] + self.template.shape[0])
                            curr_bbox = (top_left[0], top_left[1], bottom_right[0], bottom_right[1])
                            
                            # 绘制预测边界框
                            self._draw_tech_tracking_box(result_frame, curr_bbox, self.target_id, is_predicted=True)
                            
                            # 创建目标切片
                            self._create_target_slice(frame, curr_bbox, is_predicted=True)
                            
                            # 重置丢失帧数
                            self.consecutive_lost = 0
                            target_found = True
            
            # 如果目标丢失超过一定帧数，停止追踪
            if self.consecutive_lost > 30:
                self.status_signal.emit("目标丢失! 目标不在视野范围", "red")
                self.tracking_lost_signal.emit()
                # 保持追踪状态，防止追踪丢失
            elif self.consecutive_lost > 10:
                self.status_signal.emit("警告: 目标追踪不稳定", "orange")
        
        # 更新最后一次追踪的边界框
        if target_found and curr_bbox:
            self.last_target_bbox = curr_bbox
            
            # 添加到追踪历史记录
            center_x = (curr_bbox[0] + curr_bbox[2]) // 2
            center_y = (curr_bbox[1] + curr_bbox[3]) // 2
            self.track_history.append((center_x, center_y))
            
            # 保持追踪历史记录的长度
            if len(self.track_history) > 60:
                self.track_history.pop(0)
            
            # 绘制追踪历史记录
            if len(self.track_history) > 2:
                for i in range(1, len(self.track_history)):
                    # 使用点的距离计算线条的宽度
                    alpha = float(i) / len(self.track_history)
                    thickness = max(1, min(3, int(alpha * 3)))
                    
                    cv2.line(result_frame, 
                            self.track_history[i-1], 
                            self.track_history[i], 
                            self.target_color,
                            thickness)
        
        return result_frame
    
    def _draw_tech_tracking_box(self, frame, bbox, track_id, is_predicted=False):
        """绘制追踪边界框"""
        if bbox is None:
            return
        
        x1, y1, x2, y2 = bbox
        
        # 如果是预测边界框，使用虚线绘制
        if is_predicted:
            # 虚线样式
            line_type = cv2.LINE_AA if cv2.__version__ >= '3.0.0' else cv2.CV_AA
            for i in range(0, 10, 2):
                cv2.line(frame, (x1+i, y1), (x1+30, y1), self.target_color, 2, line_type)
                cv2.line(frame, (x1, y1+i), (x1, y1+30), self.target_color, 2, line_type)
                cv2.line(frame, (x2-i, y1), (x2-30, y1), self.target_color, 2, line_type)
                cv2.line(frame, (x2, y1+i), (x2, y1+30), self.target_color, 2, line_type)
                cv2.line(frame, (x1+i, y2), (x1+30, y2), self.target_color, 2, line_type)
                cv2.line(frame, (x1, y2-i), (x1, y2-30), self.target_color, 2, line_type)
                cv2.line(frame, (x2-i, y2), (x2-30, y2), self.target_color, 2, line_type)
                cv2.line(frame, (x2, y2-i), (x2, y2-30), self.target_color, 2, line_type)
        else:
            # 实线样式
            line_type = cv2.LINE_AA if cv2.__version__ >= '3.0.0' else cv2.CV_AA
            
            # 绘制边界框
            cv2.rectangle(frame, (x1, y1), (x2, y2), self.target_color, 2, line_type)
            
            # 绘制角点
            corner_length = 20
            thickness = 2
            
            # 左上角
            cv2.line(frame, (x1, y1), (x1 + corner_length, y1), self.target_color, thickness, line_type)
            cv2.line(frame, (x1, y1), (x1, y1 + corner_length), self.target_color, thickness, line_type)
            
            # 右上角
            cv2.line(frame, (x2, y1), (x2 - corner_length, y1), self.target_color, thickness, line_type)
            cv2.line(frame, (x2, y1), (x2, y1 + corner_length), self.target_color, thickness, line_type)
            
            # 左下角
            cv2.line(frame, (x1, y2), (x1 + corner_length, y2), self.target_color, thickness, line_type)
            cv2.line(frame, (x1, y2), (x1, y2 - corner_length), self.target_color, thickness, line_type)
            
            # 右下角
            cv2.line(frame, (x2, y2), (x2 - corner_length, y2), self.target_color, thickness, line_type)
            cv2.line(frame, (x2, y2), (x2, y2 - corner_length), self.target_color, thickness, line_type)
        
        # 绘制目标ID和状态
        label = f"ID: {track_id} TRACKING"
        font_scale = 0.6
        font_thickness = 2
        
        # 计算文本大小和背景大小
        (text_width, text_height), _ = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, font_scale, font_thickness)
        text_bg_x1, text_bg_y1, text_bg_x2, text_bg_y2 = x1, y1 - text_height - 10, x1 + text_width + 10, y1
        
        # 绘制背景
        sub_img = frame[text_bg_y1:text_bg_y2, text_bg_x1:text_bg_x2]
        rect = np.zeros(sub_img.shape, dtype=np.uint8)
        bg_color = (50, 50, 50) if is_predicted else (0, 0, 0)
        rect[:] = bg_color
        res = cv2.addWeighted(sub_img, 0.7, rect, 0.3, 0)
        frame[text_bg_y1:text_bg_y2, text_bg_x1:text_bg_x2] = res
        
        # 绘制文本
        cv2.putText(frame, label, (x1 + 5, y1 - 5), cv2.FONT_HERSHEY_SIMPLEX, font_scale, 
                    self.target_color, font_thickness, line_type)
    
    def _create_target_slice(self, frame, bbox, is_predicted=False):
        """创建目标切片"""
        if bbox is None or frame is None:
            return
        
        x1, y1, x2, y2 = bbox
        h, w = frame.shape[:2]
        
        # 校正边界框
        x1, y1 = max(0, x1), max(0, y1)
        x2, y2 = min(w, x2), min(h, y2)
        
        # 剪切目标区域
        if x2 > x1 and y2 > y1:
            target_slice = frame[y1:y2, x1:x2].copy()
            
            # 如果是预测目标，添加标签
            if is_predicted:
                cv2.putText(target_slice, "PREDICTED", (10, 20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, 
                            (0, 140, 255), 1, cv2.LINE_AA if cv2.__version__ >= '3.0.0' else cv2.CV_AA)
            
            # 转换为QPixmap并发送信号
            height, width, channel = target_slice.shape
            bytes_per_line = 3 * width
            q_img = QImage(target_slice.data, width, height, bytes_per_line, QImage.Format_RGB888).rgbSwapped()
            pixmap = QPixmap.fromImage(q_img)
            self.target_image_signal.emit(pixmap)
    
    def process_frame(self, frame, bbox, track_id):
        """处理从YOLO检测器传递的帧和边界框
        
        参数:
            frame: 完整的原始视频帧
            bbox: 目标的边界框 [(x1,y1), (x2,y2)]
            track_id: 目标的跟踪ID
        """
        if not self.is_tracking or track_id != self.target_id:
            return
            
        # 从边界框坐标构造完整的bbox格式 [x1, y1, x2, y2]
        x1, y1 = bbox[0]
        x2, y2 = bbox[1]
        bbox_format = [x1, y1, x2, y2]
        
        # 创建目标切片并发送信号
        self._create_target_slice(frame, bbox_format)
        
        # 更新状态信息
        self.track_frames += 1
        self.consecutive_lost = 0
        
        # 每10帧更新一次状态信息
        if self.track_frames % 10 == 0:
            self.status_signal.emit(f"正在稳定追踪目标 ID: {self.target_id}，已追踪{self.track_frames}帧", "green")
