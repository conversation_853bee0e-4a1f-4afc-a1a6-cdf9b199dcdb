
# 最小化登录测试 - 完全独立的实现
import pymysql
from flask import Flask, request, jsonify

app = Flask(__name__)

@app.route('/test-login', methods=['POST'])
def test_login():
    """最小化登录测试"""
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        
        # 直接数据库连接
        config = {
            'host': '127.0.0.1',
            'port': 3306,
            'user': 'root',
            'password': '123456',
            'database': 'yolo',
            'charset': 'utf8mb4',
            'autocommit': True,
            'cursorclass': pymysql.cursors.DictCursor
        }
        
        connection = pymysql.connect(**config)
        with connection.cursor() as cursor:
            cursor.execute(
                "SELECT * FROM user WHERE username=%s AND password=%s",
                (username, password)
            )
            user = cursor.fetchone()
        connection.close()
        
        if user:
            return jsonify({
                'success': True,
                'message': '登录成功',
                'user': {
                    'id': user['id'],
                    'username': user['username'],
                    'grade': user['grade']
                }
            })
        else:
            return jsonify({
                'success': False,
                'message': '用户名或密码错误'
            })
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'错误: {str(e)}'
        })

if __name__ == '__main__':
    app.run(host='127.0.0.1', port=5501, debug=True)
