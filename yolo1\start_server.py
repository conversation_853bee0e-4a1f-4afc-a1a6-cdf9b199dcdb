# -*- coding: utf-8 -*-
# @Description : 基于Yolov8与ByteTrack的高速公路智慧监控平台 - 简化启动脚本
# @Date : 2025年6月20日

import os
import sys

# 尝试导入dotenv，如果失败则使用备用方案
try:
    from dotenv import load_dotenv
    DOTENV_AVAILABLE = True
except ImportError:
    print("警告: python-dotenv 未安装，使用环境变量备用方案")
    DOTENV_AVAILABLE = False

    def load_dotenv(*args, **kwargs):
        """备用的load_dotenv函数"""
        pass

# 加载环境变量
load_dotenv(override=True, dotenv_path='config/end-back.env')

print("=" * 80)
print("基于Yolov8与ByteTrack的高速公路智慧监控平台")
print("=" * 80)

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'flask', 'pymysql', 'ultralytics', 'cv2',
        'numpy', 'PIL', 'torch'
    ]

    missing_packages = []
    for package in required_packages:
        try:
            if package == 'cv2':
                import cv2
            elif package == 'PIL':
                import PIL
            else:
                __import__(package)
        except ImportError:
            missing_packages.append(package)

    if missing_packages:
        print("缺少以下依赖包:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\n请运行以下命令安装:")
        if 'torch' in missing_packages:
            print("对于CUDA支持，请运行: python install_cuda_dependencies.py")
        else:
            print(f"pip install {' '.join([p for p in missing_packages if p != 'torch'])}")
        return False

    return True

def check_cuda_support():
    """检查CUDA支持"""
    try:
        import torch
        cuda_available = torch.cuda.is_available()
        if cuda_available:
            device_count = torch.cuda.device_count()
            device_name = torch.cuda.get_device_name(0) if device_count > 0 else "Unknown"
            print(f"✓ CUDA可用 - 设备: {device_name} (共{device_count}个GPU)")
            return True, "cuda"
        else:
            print("⚠ CUDA不可用，使用CPU模式")
            return True, "cpu"
    except ImportError:
        print("⚠ PyTorch未安装，无法检查CUDA")
        return False, "cpu"
    except Exception as e:
        print(f"⚠ CUDA检查失败: {e}")
        return True, "cpu"

def test_database():
    """测试数据库连接"""
    try:
        import pymysql
        
        config = {
            'host': os.getenv('MYSQL_HOST', '127.0.0.1'),
            'port': int(os.getenv('MYSQL_PORT', 3306)),
            'user': os.getenv('MYSQL_USER', os.getenv('MYSQL_user', 'root')),
            'password': os.getenv('MYSQL_PASSWORD', os.getenv('MYSQL_password', '123456')),
            'database': os.getenv('MYSQL_DATABASE', os.getenv('MYSQL_db', 'yolo')),
            'charset': 'utf8mb4',
            'autocommit': True,
            'cursorclass': pymysql.cursors.DictCursor
        }
        
        connection = pymysql.connect(**config)
        with connection.cursor() as cursor:
            cursor.execute("SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = %s", (config['database'],))
            result = cursor.fetchone()
            table_count = result['count']
        
        connection.close()
        
        if table_count > 0:
            print(f"✓ 数据库连接成功，共有 {table_count} 个表")
            return True
        else:
            print("✗ 数据库连接成功，但没有表，请导入数据库结构")
            return False
            
    except Exception as e:
        print(f"✗ 数据库连接失败: {e}")
        return False

def create_basic_app():
    """创建基础Flask应用"""
    from flask import Flask, jsonify, request
    from flask_cors import CORS
    import hashlib
    from datetime import datetime
    
    app = Flask(__name__, static_folder='static')
    app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'yolo-highway-monitoring-system-2025')
    app.config['JSON_AS_ASCII'] = False
    app.config['MAX_CONTENT_LENGTH'] = 100 * 1024 * 1024  # 100MB
    
    # 跨域支持
    CORS(app, supports_credentials=True)
    
    @app.route('/')
    def index():
        return jsonify({
            'message': '欢迎使用基于Yolov8与ByteTrack的高速公路智慧监控平台！',
            'version': '1.0.0',
            'status': 'running',
            'mode': 'basic',
            'timestamp': datetime.now().isoformat(),
            'api_docs': '/api/docs'
        })
    
    @app.route('/health')
    def health():
        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'database': test_database(),
            'mode': 'basic'
        })
    
    @app.route('/api/docs')
    def api_docs():
        return jsonify({
            'title': '基于Yolov8与ByteTrack的高速公路智慧监控平台 API',
            'version': '1.0.0',
            'description': '基础API接口',
            'endpoints': {
                'health': 'GET /health',
                'login': 'POST /api/login',
                'monitors': 'GET /api/monitors',
                'test': 'GET /api/test'
            }
        })
    
    @app.route('/api/test')
    def test_api():
        return jsonify({
            'status': 'success',
            'message': 'API测试成功',
            'timestamp': datetime.now().isoformat()
        })
    
    # 数据库登录验证函数
    def verify_user(username, password):
        """验证用户登录"""
        try:
            import pymysql
            import hashlib

            config = {
                'host': os.getenv('MYSQL_HOST', '127.0.0.1'),
                'port': int(os.getenv('MYSQL_PORT', 3306)),
                'user': os.getenv('MYSQL_USER', os.getenv('MYSQL_user', 'root')),
                'password': os.getenv('MYSQL_PASSWORD', os.getenv('MYSQL_password', '123456')),
                'database': os.getenv('MYSQL_DATABASE', os.getenv('MYSQL_db', 'yolo')),
                'charset': 'utf8mb4',
                'autocommit': True,
                'cursorclass': pymysql.cursors.DictCursor
            }

            connection = pymysql.connect(**config)
            with connection.cursor() as cursor:
                # MD5加密密码
                password_hash = hashlib.md5(password.encode()).hexdigest()
                cursor.execute("SELECT * FROM user WHERE username=%s AND password=%s", (username, password_hash))
                user = cursor.fetchone()

            connection.close()
            return user
        except Exception as e:
            print(f"数据库验证失败: {e}")
            return None

    # 基础登录接口 (兼容旧版)
    @app.route('/api/login', methods=['POST'])
    def login():
        try:
            data = request.get_json()
            username = data.get('username')
            password = data.get('password')

            if not username or not password:
                return jsonify({
                    'code': 400,
                    'message': '用户名和密码不能为空',
                    'success': False
                }), 400

            # 数据库验证
            user = verify_user(username, password)
            if user:
                return jsonify({
                    'code': 200,
                    'message': '登录成功',
                    'data': {
                        'token': 'basic_token_' + hashlib.md5(f"{username}{datetime.now()}".encode()).hexdigest()[:16],
                        'user': {
                            'id': user['id'],
                            'username': user['username'],
                            'email': user['email'],
                            'grade': user['grade'],
                            'avatar': user['avatar']
                        }
                    },
                    'success': True
                })
            else:
                return jsonify({
                    'code': 401,
                    'message': '用户名或密码错误',
                    'success': False
                }), 401

        except Exception as e:
            return jsonify({
                'code': 500,
                'message': f'登录失败: {str(e)}',
                'success': False
            }), 500

    # V1 API登录接口 (前端兼容)
    @app.route('/api/v1/auth/login', methods=['POST'])
    def login_v1():
        try:
            data = request.get_json()
            username = data.get('username')
            password = data.get('password')

            if not username or not password:
                return jsonify({
                    'code': 400,
                    'message': '用户名和密码不能为空',
                    'success': False
                }), 400

            # 数据库验证
            user = verify_user(username, password)
            if user:
                return jsonify({
                    'code': 200,
                    'message': '登录成功',
                    'data': {
                        'token': 'jwt_token_' + hashlib.md5(f"{username}{datetime.now()}".encode()).hexdigest()[:16],
                        'user': {
                            'id': user['id'],
                            'username': user['username'],
                            'email': user['email'],
                            'grade': user['grade'],
                            'avatar': user['avatar'],
                            'status': user.get('status', 1),
                            'last_login_time': user.get('last_login_time'),
                            'create_time': user.get('create_time')
                        }
                    },
                    'success': True
                })
            else:
                return jsonify({
                    'code': 401,
                    'message': '用户名或密码错误',
                    'success': False
                }), 401

        except Exception as e:
            return jsonify({
                'code': 500,
                'message': f'登录失败: {str(e)}',
                'success': False
            }), 500

    # V1 API系统健康检查
    @app.route('/api/v1/system/health-check', methods=['GET'])
    def health_check_v1():
        try:
            db_status = test_database()
            return jsonify({
                'code': 200,
                'message': '系统健康检查完成',
                'data': {
                    'status': 'healthy',
                    'database': 'connected' if db_status else 'disconnected',
                    'timestamp': datetime.now().isoformat(),
                    'version': '1.0.0',
                    'environment': 'development' if os.getenv('DEBUG', 'false').lower() == 'true' else 'production'
                },
                'success': True
            })
        except Exception as e:
            return jsonify({
                'code': 500,
                'message': f'健康检查失败: {str(e)}',
                'success': False
            }), 500

    # V1 API文档
    @app.route('/api/v1/docs', methods=['GET'])
    def api_docs_v1():
        return jsonify({
            'code': 200,
            'message': 'API文档获取成功',
            'data': {
                'title': '基于Yolov8与ByteTrack的高速公路智慧监控平台 API v1.0',
                'version': '1.0.0',
                'description': '基于Yolov8与ByteTrack的高速公路智慧监控平台RESTful API',
                'base_url': request.host_url + 'api/v1',
                'endpoints': {
                    'authentication': {
                        'login': 'POST /auth/login - 用户登录',
                        'logout': 'POST /auth/logout - 用户登出',
                        'profile': 'GET /auth/profile - 获取用户信息'
                    },
                    'monitor_management': {
                        'list_monitors': 'GET /monitor/list - 获取监控点列表',
                        'get_monitor': 'GET /monitor/{id} - 获取监控点详情',
                        'create_monitor': 'POST /monitor - 创建监控点',
                        'update_monitor': 'PUT /monitor/{id} - 更新监控点',
                        'delete_monitor': 'DELETE /monitor/{id} - 删除监控点'
                    },
                    'detection': {
                        'detect_image': 'POST /detection/image - 图像检测',
                        'detect_video': 'POST /detection/video - 视频检测',
                        'get_tasks': 'GET /detection/tasks - 获取检测任务'
                    },
                    'system': {
                        'health_check': 'GET /system/health-check - 系统健康检查',
                        'info': 'GET /system/info - 系统信息',
                        'docs': 'GET /docs - API文档'
                    }
                }
            },
            'success': True
        })

    # V1 API监控点列表
    @app.route('/api/v1/monitor/list', methods=['GET'])
    def monitor_list_v1():
        try:
            if test_database():
                import pymysql

                config = {
                    'host': os.getenv('MYSQL_HOST', '127.0.0.1'),
                    'port': int(os.getenv('MYSQL_PORT', 3306)),
                    'user': os.getenv('MYSQL_USER', os.getenv('MYSQL_user', 'root')),
                    'password': os.getenv('MYSQL_PASSWORD', os.getenv('MYSQL_password', '123456')),
                    'database': os.getenv('MYSQL_DATABASE', os.getenv('MYSQL_db', 'yolo')),
                    'charset': 'utf8mb4',
                    'autocommit': True,
                    'cursorclass': pymysql.cursors.DictCursor
                }

                connection = pymysql.connect(**config)
                with connection.cursor() as cursor:
                    # 获取分页参数
                    page = int(request.args.get('page', 1))
                    size = int(request.args.get('size', 10))
                    offset = (page - 1) * size

                    # 查询总数
                    cursor.execute("SELECT COUNT(*) as total FROM monitor")
                    total = cursor.fetchone()['total']

                    # 查询监控点列表
                    cursor.execute("""
                        SELECT id, name, location, highway_section, camera_position,
                               threshold, conf_threshold, iou_threshold, connection_status,
                               is_alarm, mode, enable_tracking, tracker_type, status,
                               create_time, remark
                        FROM monitor
                        ORDER BY id DESC
                        LIMIT %s OFFSET %s
                    """, (size, offset))
                    monitors = cursor.fetchall()

                connection.close()

                return jsonify({
                    'code': 200,
                    'message': '获取监控点列表成功',
                    'data': {
                        'list': monitors,
                        'pagination': {
                            'page': page,
                            'size': size,
                            'total': total,
                            'pages': (total + size - 1) // size
                        }
                    },
                    'success': True
                })
            else:
                return jsonify({
                    'code': 500,
                    'message': '数据库连接失败',
                    'success': False
                }), 500

        except Exception as e:
            return jsonify({
                'code': 500,
                'message': f'获取监控点失败: {str(e)}',
                'success': False
            }), 500

    # V1 API警报列表
    @app.route('/api/v1/analysis/alarms', methods=['GET'])
    def alarm_list_v1():
        try:
            if test_database():
                import pymysql

                config = {
                    'host': os.getenv('MYSQL_HOST', '127.0.0.1'),
                    'port': int(os.getenv('MYSQL_PORT', 3306)),
                    'user': os.getenv('MYSQL_USER', os.getenv('MYSQL_user', 'root')),
                    'password': os.getenv('MYSQL_PASSWORD', os.getenv('MYSQL_password', '123456')),
                    'database': os.getenv('MYSQL_DATABASE', os.getenv('MYSQL_db', 'yolo')),
                    'charset': 'utf8mb4',
                    'autocommit': True,
                    'cursorclass': pymysql.cursors.DictCursor
                }

                connection = pymysql.connect(**config)
                with connection.cursor() as cursor:
                    # 获取分页参数
                    page = int(request.args.get('page', 1))
                    size = int(request.args.get('size', 10))
                    offset = (page - 1) * size

                    # 查询总数
                    cursor.execute("SELECT COUNT(*) as total FROM alarm")
                    total = cursor.fetchone()['total']

                    # 查询警报列表
                    cursor.execute("""
                        SELECT a.*, m.name as monitor_name
                        FROM alarm a
                        LEFT JOIN monitor m ON a.monitor_id = m.id
                        ORDER BY a.create_time DESC
                        LIMIT %s OFFSET %s
                    """, (size, offset))
                    alarms = cursor.fetchall()

                connection.close()

                return jsonify({
                    'code': 200,
                    'message': '获取警报列表成功',
                    'data': {
                        'list': alarms,
                        'pagination': {
                            'page': page,
                            'size': size,
                            'total': total,
                            'pages': (total + size - 1) // size
                        }
                    },
                    'success': True
                })
            else:
                return jsonify({
                    'code': 500,
                    'message': '数据库连接失败',
                    'success': False
                }), 500

        except Exception as e:
            return jsonify({
                'code': 500,
                'message': f'获取警报失败: {str(e)}',
                'success': False
            }), 500
    
    # 监控点列表接口
    @app.route('/api/monitors')
    def get_monitors():
        try:
            if test_database():
                import pymysql
                
                config = {
                    'host': os.getenv('MYSQL_HOST', '127.0.0.1'),
                    'port': int(os.getenv('MYSQL_PORT', 3306)),
                    'user': os.getenv('MYSQL_USER', os.getenv('MYSQL_user', 'root')),
                    'password': os.getenv('MYSQL_PASSWORD', os.getenv('MYSQL_password', '123456')),
                    'database': os.getenv('MYSQL_DATABASE', os.getenv('MYSQL_db', 'yolo')),
                    'charset': 'utf8mb4',
                    'autocommit': True,
                    'cursorclass': pymysql.cursors.DictCursor
                }
                
                connection = pymysql.connect(**config)
                with connection.cursor() as cursor:
                    cursor.execute("SELECT * FROM monitor LIMIT 10")
                    monitors = cursor.fetchall()
                
                connection.close()
                
                return jsonify({
                    'code': 200,
                    'message': '获取监控点列表成功',
                    'data': {
                        'list': monitors,
                        'total': len(monitors)
                    },
                    'success': True
                })
            else:
                return jsonify({
                    'code': 500,
                    'message': '数据库连接失败',
                    'success': False
                }), 500
                
        except Exception as e:
            return jsonify({
                'code': 500,
                'message': f'获取监控点失败: {str(e)}',
                'success': False
            }), 500
    
    return app

def check_conda_env():
    """检查是否在Conda环境中"""
    conda_env = os.getenv('CONDA_DEFAULT_ENV')
    if conda_env:
        print(f"✓ 检测到Conda环境: {conda_env}")
        if conda_env == 'ByteTrack':
            print("✓ 正在使用推荐的ByteTrack环境")
        return True
    else:
        print("⚠ 未检测到Conda环境")
        return False

def main():
    """主函数"""
    print("正在检查系统环境...")

    # 检查Conda环境
    check_conda_env()

    # 检查依赖
    if not check_dependencies():
        print("\n请先安装缺少的依赖包")
        print("推荐安装方式:")
        print("1. Conda环境: 运行 setup_conda_env.bat")
        print("2. 或运行: python install_conda_cuda.py")
        print("3. 或运行: python install_cuda_dependencies.py")
        return

    print("✓ 依赖包检查通过")

    # 检查CUDA支持
    print("正在检查CUDA支持...")
    cuda_ok, device = check_cuda_support()

    # 设置环境变量
    if cuda_ok and device == "cuda":
        os.environ['YOLO_DEVICE'] = 'cuda'
        os.environ['USE_CUDA'] = 'true'
    else:
        os.environ['YOLO_DEVICE'] = 'cpu'
        os.environ['USE_CUDA'] = 'false'

    # 检查数据库
    print("正在检查数据库连接...")
    db_ok = test_database()

    if not db_ok:
        print("\n数据库连接有问题，但系统仍会启动基础功能")
        print("请检查:")
        print("1. MySQL服务是否启动")
        print("2. 数据库配置是否正确 (config/end-back.env)")
        print("3. 是否已导入数据库结构 (yolo.sql)")

    # 创建应用
    print("正在创建Flask应用...")
    app = create_basic_app()

    # 获取配置
    host = os.getenv('HOST_NAME', '127.0.0.1')
    port = int(os.getenv('PORT', 5500))
    debug = os.getenv('DEBUG', 'false').lower() == 'true'

    print("-" * 80)
    print(f"服务地址: http://{host}:{port}")
    print(f"调试模式: {debug}")
    print(f"计算设备: {device.upper()}")
    print(f"数据库状态: {'正常' if db_ok else '异常'}")
    print("-" * 80)
    print("系统启动中...")
    print("按 Ctrl+C 停止服务")

    try:
        app.run(host=host, port=port, debug=debug)
    except KeyboardInterrupt:
        print("\n系统正在关闭...")
        print("✓ 系统已关闭")
    except Exception as e:
        print(f"系统启动失败: {e}")

if __name__ == '__main__':
    main()
