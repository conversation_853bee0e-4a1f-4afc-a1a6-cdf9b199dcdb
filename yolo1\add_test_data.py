#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
添加测试数据 - 让前端显示有数据
"""

import pymysql
import random
from datetime import datetime, timedelta
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(override=True, dotenv_path='config/end-back.env')

def get_db_config():
    """获取数据库配置"""
    return {
        'host': '127.0.0.1',
        'port': 3306,
        'user': 'root',
        'password': '123456',
        'database': 'yolo',
        'charset': 'utf8mb4',
        'autocommit': True,
        'cursorclass': pymysql.cursors.DictCursor
    }

def add_alarm_test_data():
    """添加警报测试数据"""
    print("🔧 添加警报测试数据...")
    
    config = get_db_config()
    connection = pymysql.connect(**config)
    
    try:
        with connection.cursor() as cursor:
            # 获取监控点ID
            cursor.execute("SELECT id FROM monitor LIMIT 5")
            monitor_ids = [row['id'] for row in cursor.fetchall()]
            
            if not monitor_ids:
                print("   ❌ 没有监控点数据，请先添加监控点")
                return False
            
            # 清除旧的测试数据
            cursor.execute("DELETE FROM alarm WHERE create_time >= CURDATE() - INTERVAL 7 DAY")
            
            # 添加最近7天的警报数据
            for days_ago in range(7):
                date = datetime.now() - timedelta(days=days_ago)
                
                # 每天随机生成5-15个警报
                daily_alarms = random.randint(5, 15)
                
                for _ in range(daily_alarms):
                    monitor_id = random.choice(monitor_ids)
                    vehicle_count = random.randint(1, 20)
                    confidence_level = round(random.uniform(0.7, 0.99), 2)
                    
                    # 随机时间
                    alarm_time = date.replace(
                        hour=random.randint(0, 23),
                        minute=random.randint(0, 59),
                        second=random.randint(0, 59)
                    )
                    
                    cursor.execute("""
                        INSERT INTO alarm (pid, vehicle_count, confidence_level, create_time, detection_details)
                        VALUES (%s, %s, %s, %s, %s)
                    """, (
                        monitor_id,
                        vehicle_count,
                        confidence_level,
                        alarm_time,
                        f'{{"vehicles": {vehicle_count}, "confidence": {confidence_level}, "type": "traffic_congestion"}}'
                    ))
            
            print(f"   ✅ 成功添加警报测试数据")
            return True
            
    except Exception as e:
        print(f"   ❌ 添加警报测试数据失败: {e}")
        return False
    finally:
        connection.close()

def add_accident_test_data():
    """添加事故测试数据"""
    print("🔧 添加事故测试数据...")
    
    config = get_db_config()
    connection = pymysql.connect(**config)
    
    try:
        with connection.cursor() as cursor:
            # 获取监控点ID
            cursor.execute("SELECT id FROM monitor LIMIT 5")
            monitor_ids = [row['id'] for row in cursor.fetchall()]
            
            if not monitor_ids:
                print("   ❌ 没有监控点数据")
                return False
            
            # 清除旧的测试数据
            cursor.execute("DELETE FROM accident_record WHERE create_time >= CURDATE() - INTERVAL 30 DAY")
            
            # 添加最近30天的事故数据
            accident_types = ['追尾', '侧翻', '碰撞', '抛锚', '违停']
            severity_levels = ['轻微', '一般', '严重']
            
            for days_ago in range(30):
                date = datetime.now() - timedelta(days=days_ago)
                
                # 每天随机生成0-3个事故
                daily_accidents = random.randint(0, 3)
                
                for _ in range(daily_accidents):
                    monitor_id = random.choice(monitor_ids)
                    accident_type = random.choice(accident_types)
                    severity = random.choice(severity_levels)
                    
                    # 随机时间
                    accident_time = date.replace(
                        hour=random.randint(0, 23),
                        minute=random.randint(0, 59),
                        second=random.randint(0, 59)
                    )
                    
                    cursor.execute("""
                        INSERT INTO accident_record (monitor_id, accident_type, severity_level, 
                                                   description, create_time, status)
                        VALUES (%s, %s, %s, %s, %s, %s)
                    """, (
                        monitor_id,
                        accident_type,
                        severity,
                        f'监控点检测到{accident_type}事故，严重程度：{severity}',
                        accident_time,
                        'processed' if random.random() > 0.3 else 'pending'
                    ))
            
            print(f"   ✅ 成功添加事故测试数据")
            return True
            
    except Exception as e:
        print(f"   ❌ 添加事故测试数据失败: {e}")
        return False
    finally:
        connection.close()

def add_detection_task_test_data():
    """添加检测任务测试数据"""
    print("🔧 添加检测任务测试数据...")
    
    config = get_db_config()
    connection = pymysql.connect(**config)
    
    try:
        with connection.cursor() as cursor:
            # 获取监控点ID
            cursor.execute("SELECT id, location FROM monitor")
            monitors = cursor.fetchall()
            
            if not monitors:
                print("   ❌ 没有监控点数据")
                return False
            
            # 清除旧的测试数据
            cursor.execute("DELETE FROM detection_task")
            
            # 为每个监控点添加检测任务
            task_statuses = ['running', 'stopped', 'error', 'completed']
            
            for monitor in monitors:
                # 随机生成1-3个任务
                task_count = random.randint(1, 3)
                
                for i in range(task_count):
                    status = random.choice(task_statuses)
                    
                    # 随机时间
                    create_time = datetime.now() - timedelta(
                        days=random.randint(0, 7),
                        hours=random.randint(0, 23),
                        minutes=random.randint(0, 59)
                    )
                    
                    rtsp_url = f"rtsp://192.168.1.{100 + monitor['id']}/stream"
                    
                    cursor.execute("""
                        INSERT INTO detection_task (monitor_id, rtsp_url, status, create_time, 
                                                   create_by, task_name, description)
                        VALUES (%s, %s, %s, %s, %s, %s, %s)
                    """, (
                        monitor['id'],
                        rtsp_url,
                        status,
                        create_time,
                        1,  # admin用户ID
                        f"{monitor['location']}检测任务{i+1}",
                        f"监控点{monitor['location']}的视频流检测任务"
                    ))
            
            print(f"   ✅ 成功添加检测任务测试数据")
            return True
            
    except Exception as e:
        print(f"   ❌ 添加检测任务测试数据失败: {e}")
        return False
    finally:
        connection.close()

def update_monitor_rtsp_urls():
    """更新监控点RTSP地址"""
    print("🔧 更新监控点RTSP地址...")
    
    config = get_db_config()
    connection = pymysql.connect(**config)
    
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT id FROM monitor")
            monitor_ids = [row['id'] for row in cursor.fetchall()]
            
            for monitor_id in monitor_ids:
                rtsp_url = f"rtsp://192.168.1.{100 + monitor_id}/stream"
                cursor.execute("""
                    UPDATE monitor 
                    SET rtsp_url = %s, connection_status = '在线'
                    WHERE id = %s
                """, (rtsp_url, monitor_id))
            
            print(f"   ✅ 成功更新{len(monitor_ids)}个监控点的RTSP地址")
            return True
            
    except Exception as e:
        print(f"   ❌ 更新监控点RTSP地址失败: {e}")
        return False
    finally:
        connection.close()

def add_traffic_statistics_data():
    """添加交通统计数据"""
    print("🔧 添加交通统计测试数据...")
    
    config = get_db_config()
    connection = pymysql.connect(**config)
    
    try:
        with connection.cursor() as cursor:
            # 获取监控点ID
            cursor.execute("SELECT id FROM monitor")
            monitor_ids = [row['id'] for row in cursor.fetchall()]
            
            if not monitor_ids:
                print("   ❌ 没有监控点数据")
                return False
            
            # 清除旧数据
            cursor.execute("DELETE FROM traffic_statistics WHERE record_time >= CURDATE() - INTERVAL 7 DAY")
            
            # 添加最近7天的交通统计数据
            for days_ago in range(7):
                date = datetime.now() - timedelta(days=days_ago)
                
                for monitor_id in monitor_ids:
                    # 每小时一条记录
                    for hour in range(24):
                        record_time = date.replace(hour=hour, minute=0, second=0)
                        
                        # 模拟不同时段的车流量
                        if 7 <= hour <= 9 or 17 <= hour <= 19:  # 高峰期
                            vehicle_count = random.randint(50, 100)
                        elif 22 <= hour or hour <= 6:  # 夜间
                            vehicle_count = random.randint(5, 20)
                        else:  # 平常时段
                            vehicle_count = random.randint(20, 50)
                        
                        avg_speed = random.randint(60, 120)
                        
                        cursor.execute("""
                            INSERT INTO traffic_statistics (monitor_id, vehicle_count, avg_speed, record_time)
                            VALUES (%s, %s, %s, %s)
                        """, (monitor_id, vehicle_count, avg_speed, record_time))
            
            print(f"   ✅ 成功添加交通统计测试数据")
            return True
            
    except Exception as e:
        print(f"   ❌ 添加交通统计测试数据失败: {e}")
        return False
    finally:
        connection.close()

def add_tracking_targets_data():
    """添加追踪目标测试数据"""
    print("🔧 添加追踪目标测试数据...")

    config = get_db_config()
    connection = pymysql.connect(**config)

    try:
        with connection.cursor() as cursor:
            # 获取监控点ID
            cursor.execute("SELECT id FROM monitor LIMIT 5")
            monitor_ids = [row['id'] for row in cursor.fetchall()]

            if not monitor_ids:
                print("   ❌ 没有监控点数据")
                return False

            # 清除旧数据
            cursor.execute("DELETE FROM tracking_target WHERE create_time >= CURDATE()")

            # 添加追踪目标数据
            target_types = ['car', 'truck', 'bus', 'motorcycle']

            for monitor_id in monitor_ids:
                # 每个监控点添加5-10个追踪目标
                target_count = random.randint(5, 10)

                for i in range(target_count):
                    target_type = random.choice(target_types)
                    target_id = f"T{monitor_id:02d}{i+1:03d}"

                    # 随机位置和速度
                    x_position = random.randint(100, 1800)
                    y_position = random.randint(100, 900)
                    speed = random.randint(40, 120)
                    confidence = round(random.uniform(0.7, 0.95), 2)

                    # 随机时间
                    create_time = datetime.now() - timedelta(
                        hours=random.randint(0, 12),
                        minutes=random.randint(0, 59)
                    )

                    cursor.execute("""
                        INSERT INTO tracking_target (target_id, monitor_id, target_type,
                                                   x_position, y_position, speed, confidence,
                                                   status, create_time)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        target_id, monitor_id, target_type, x_position, y_position,
                        speed, confidence, 'active', create_time
                    ))

            print(f"   ✅ 成功添加追踪目标测试数据")
            return True

    except Exception as e:
        print(f"   ❌ 添加追踪目标测试数据失败: {e}")
        return False
    finally:
        connection.close()

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 添加测试数据 - 让前端显示有内容")
    print("=" * 60)

    success_count = 0
    total_count = 6

    # 1. 更新监控点RTSP地址
    if update_monitor_rtsp_urls():
        success_count += 1

    # 2. 添加警报数据
    if add_alarm_test_data():
        success_count += 1

    # 3. 添加事故数据
    if add_accident_test_data():
        success_count += 1

    # 4. 添加检测任务数据
    if add_detection_task_test_data():
        success_count += 1

    # 5. 添加交通统计数据
    if add_traffic_statistics_data():
        success_count += 1

    # 6. 添加追踪目标数据
    if add_tracking_targets_data():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"📋 测试数据添加完成: {success_count}/{total_count}")

    if success_count == total_count:
        print("🎉 所有测试数据添加成功!")
        print("💡 现在重新启动后端，前端应该能显示数据了")
        print("🔄 重启命令: python new_backend.py")
    else:
        print("⚠️ 部分测试数据添加失败，请检查错误信息")

    print("=" * 60)

if __name__ == "__main__":
    main()
