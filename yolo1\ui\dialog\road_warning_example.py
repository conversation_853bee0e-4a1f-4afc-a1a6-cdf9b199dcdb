#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
道路预警对话框使用示例
"""

import sys
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *
from road_warning_dialog import RoadWarningDialog

class MainWindow(QMainWindow):
    """主窗口 - 用于演示道路预警对话框"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("道路预警系统 - 演示")
        self.setGeometry(100, 100, 800, 600)
        
        # 设置主窗口样式
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(241, 245, 249, 0.95));
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgb(59, 130, 246),
                    stop:1 rgb(37, 99, 235));
                border: none;
                border-radius: 8px;
                color: white;
                font-size: 14px;
                font-weight: 500;
                padding: 12px 24px;
                min-width: 120px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgb(37, 99, 235),
                    stop:1 rgb(29, 78, 216));
            }
            QPushButton:pressed {
                background: rgb(29, 78, 216);
            }
            QLabel {
                color: rgb(51, 65, 85);
                font-size: 16px;
            }
        """)
        
        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        layout = QVBoxLayout(central_widget)
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(30)
        
        # 标题
        title = QLabel("道路预警系统演示")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 28px;
                font-weight: bold;
                color: white;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title)
        
        # 说明文本
        description = QLabel("点击下方按钮查看不同类型的道路预警对话框")
        description.setAlignment(Qt.AlignCenter)
        description.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: rgba(255, 255, 255, 0.8);
                margin-bottom: 30px;
            }
        """)
        layout.addWidget(description)
        
        # 按钮容器
        button_container = QWidget()
        button_layout = QHBoxLayout(button_container)
        button_layout.setSpacing(20)
        
        # 不同类型的预警按钮
        warning_types = [
            ("🚗 交通拥堵", self.show_traffic_warning),
            ("⚠️ 事故预警", self.show_accident_warning),
            ("🌧️ 天气预警", self.show_weather_warning),
            ("🚧 道路施工", self.show_construction_warning)
        ]
        
        for text, callback in warning_types:
            btn = QPushButton(text)
            btn.clicked.connect(callback)
            button_layout.addWidget(btn)
        
        layout.addWidget(button_container)
        
        # 添加一些状态信息
        status_label = QLabel("系统状态：正常运行")
        status_label.setAlignment(Qt.AlignCenter)
        status_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: rgba(34, 197, 94, 0.9);
                margin-top: 40px;
            }
        """)
        layout.addWidget(status_label)
        
    def show_traffic_warning(self):
        """显示交通拥堵预警"""
        dialog = RoadWarningDialog(self)
        dialog.set_warning_data({
            'title': '交通拥堵预警',
            'icon': '🚗',
            'warnings': [
                {
                    'icon': '🚗',
                    'title': '车流量异常',
                    'description': '当前车流量超过正常水平 35%',
                    'type': 'danger'
                },
                {
                    'icon': '🐌',
                    'title': '行驶缓慢',
                    'description': '平均车速低于 25 km/h',
                    'type': 'warning'
                },
                {
                    'icon': '⏰',
                    'title': '拥堵预测',
                    'description': '预计 10 分钟后形成严重拥堵',
                    'type': 'info'
                }
            ]
        })
        dialog.exec()
        
    def show_accident_warning(self):
        """显示事故预警"""
        dialog = RoadWarningDialog(self)
        dialog.set_warning_data({
            'title': '交通事故预警',
            'icon': '⚠️',
            'warnings': [
                {
                    'icon': '🚨',
                    'title': '事故发生',
                    'description': '前方 2 公里处发生交通事故',
                    'type': 'danger'
                },
                {
                    'icon': '🚧',
                    'title': '车道封闭',
                    'description': '左侧车道临时封闭，请注意避让',
                    'type': 'warning'
                },
                {
                    'icon': '🚑',
                    'title': '救援进行中',
                    'description': '救援车辆正在赶往现场',
                    'type': 'info'
                }
            ]
        })
        dialog.exec()
        
    def show_weather_warning(self):
        """显示天气预警"""
        dialog = RoadWarningDialog(self)
        dialog.set_warning_data({
            'title': '恶劣天气预警',
            'icon': '🌧️',
            'warnings': [
                {
                    'icon': '🌧️',
                    'title': '大雨天气',
                    'description': '当前降雨量较大，路面湿滑',
                    'type': 'warning'
                },
                {
                    'icon': '👁️',
                    'title': '能见度低',
                    'description': '能见度不足 200 米，请谨慎驾驶',
                    'type': 'danger'
                },
                {
                    'icon': '🛣️',
                    'title': '建议路线',
                    'description': '建议选择高架道路行驶',
                    'type': 'info'
                }
            ]
        })
        dialog.exec()
        
    def show_construction_warning(self):
        """显示道路施工预警"""
        dialog = RoadWarningDialog(self)
        dialog.set_warning_data({
            'title': '道路施工预警',
            'icon': '🚧',
            'warnings': [
                {
                    'icon': '🚧',
                    'title': '施工路段',
                    'description': '前方 1 公里处道路施工',
                    'type': 'warning'
                },
                {
                    'icon': '🔄',
                    'title': '车道变更',
                    'description': '请提前变更至右侧车道',
                    'type': 'info'
                },
                {
                    'icon': '⏱️',
                    'title': '施工时间',
                    'description': '施工时间：09:00 - 17:00',
                    'type': 'info'
                }
            ]
        })
        dialog.exec()


class EnhancedRoadWarningDialog(RoadWarningDialog):
    """增强版道路预警对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.warning_data = None
        
    def set_warning_data(self, data):
        """设置预警数据"""
        self.warning_data = data
        self.update_content()
        
    def update_content(self):
        """根据数据更新内容"""
        if not self.warning_data:
            return
            
        # 更新标题
        self.setWindowTitle(self.warning_data.get('title', '道路预警'))
        
        # 这里可以根据实际需要更新对话框内容
        # 由于原始对话框结构比较固定，这里只是示例
        pass


# 将增强版对话框设为默认
RoadWarningDialog = EnhancedRoadWarningDialog


if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyle('Fusion')
    
    # 创建主窗口
    window = MainWindow()
    window.show()
    
    sys.exit(app.exec())
