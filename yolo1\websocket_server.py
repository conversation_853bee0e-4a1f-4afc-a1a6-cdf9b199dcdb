# -*- coding: utf-8 -*-
# @Description : WebSocket实时推送服务
# @Date : 2025年6月21日

import asyncio
import websockets
import json
import threading
import time
from datetime import datetime
from collections import defaultdict
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WebSocketManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        self.connections = set()  # 活跃连接
        self.subscriptions = defaultdict(set)  # 订阅关系
        self.running = True
        
    async def register(self, websocket, path):
        """注册新连接"""
        self.connections.add(websocket)
        logger.info(f"新连接注册: {websocket.remote_address}")
        
        try:
            # 发送欢迎消息
            await websocket.send(json.dumps({
                'type': 'welcome',
                'message': '连接成功',
                'timestamp': datetime.now().isoformat()
            }))
            
            # 处理消息
            async for message in websocket:
                await self.handle_message(websocket, message)
                
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"连接关闭: {websocket.remote_address}")
        except Exception as e:
            logger.error(f"连接异常: {e}")
        finally:
            await self.unregister(websocket)
    
    async def unregister(self, websocket):
        """注销连接"""
        self.connections.discard(websocket)
        
        # 清理订阅
        for topic, subscribers in self.subscriptions.items():
            subscribers.discard(websocket)
        
        logger.info(f"连接注销: {websocket.remote_address}")
    
    async def handle_message(self, websocket, message):
        """处理客户端消息"""
        try:
            data = json.loads(message)
            msg_type = data.get('type')
            
            if msg_type == 'subscribe':
                # 订阅主题
                topic = data.get('topic')
                if topic:
                    self.subscriptions[topic].add(websocket)
                    await websocket.send(json.dumps({
                        'type': 'subscription_confirmed',
                        'topic': topic,
                        'timestamp': datetime.now().isoformat()
                    }))
                    logger.info(f"订阅主题: {topic}")
            
            elif msg_type == 'unsubscribe':
                # 取消订阅
                topic = data.get('topic')
                if topic:
                    self.subscriptions[topic].discard(websocket)
                    await websocket.send(json.dumps({
                        'type': 'unsubscription_confirmed',
                        'topic': topic,
                        'timestamp': datetime.now().isoformat()
                    }))
                    logger.info(f"取消订阅: {topic}")
            
            elif msg_type == 'ping':
                # 心跳检测
                await websocket.send(json.dumps({
                    'type': 'pong',
                    'timestamp': datetime.now().isoformat()
                }))
            
        except json.JSONDecodeError:
            await websocket.send(json.dumps({
                'type': 'error',
                'message': '无效的JSON格式',
                'timestamp': datetime.now().isoformat()
            }))
        except Exception as e:
            logger.error(f"处理消息异常: {e}")
    
    async def broadcast(self, topic, data):
        """广播消息到订阅者"""
        if topic not in self.subscriptions:
            return
            
        message = json.dumps({
            'type': 'broadcast',
            'topic': topic,
            'data': data,
            'timestamp': datetime.now().isoformat()
        })
        
        # 发送给所有订阅者
        subscribers = self.subscriptions[topic].copy()
        if subscribers:
            await asyncio.gather(
                *[self._send_safe(ws, message) for ws in subscribers],
                return_exceptions=True
            )
    
    async def _send_safe(self, websocket, message):
        """安全发送消息"""
        try:
            await websocket.send(message)
        except websockets.exceptions.ConnectionClosed:
            # 连接已关闭，从订阅中移除
            for topic, subscribers in self.subscriptions.items():
                subscribers.discard(websocket)
            self.connections.discard(websocket)
        except Exception as e:
            logger.error(f"发送消息失败: {e}")
    
    def get_stats(self):
        """获取连接统计"""
        return {
            'total_connections': len(self.connections),
            'subscriptions': {
                topic: len(subscribers) 
                for topic, subscribers in self.subscriptions.items()
            }
        }

class RealTimeDataPusher:
    """实时数据推送器"""
    
    def __init__(self, websocket_manager):
        self.ws_manager = websocket_manager
        self.data_queue = asyncio.Queue()
        self.running = True
        
    async def push_detection_update(self, monitor_id, detection_data):
        """推送检测更新"""
        await self.ws_manager.broadcast(f'detection_{monitor_id}', {
            'monitor_id': monitor_id,
            'vehicle_count': detection_data.get('vehicle_count', 0),
            'detections': detection_data.get('detections', []),
            'tracked_objects': detection_data.get('tracked_objects', []),
            'timestamp': detection_data.get('timestamp')
        })
        
        # 也推送到全局检测主题
        await self.ws_manager.broadcast('detection_all', {
            'monitor_id': monitor_id,
            'vehicle_count': detection_data.get('vehicle_count', 0),
            'timestamp': detection_data.get('timestamp')
        })
    
    async def push_alarm(self, alarm_data):
        """推送警报"""
        await self.ws_manager.broadcast('alarms', {
            'type': 'new_alarm',
            'alarm': alarm_data
        })
        
        # 推送到特定监控点
        monitor_id = alarm_data.get('monitor_id')
        if monitor_id:
            await self.ws_manager.broadcast(f'alarms_{monitor_id}', alarm_data)
    
    async def push_violation(self, violation_data):
        """推送违规事件"""
        await self.ws_manager.broadcast('violations', {
            'type': 'new_violation',
            'violation': violation_data
        })
    
    async def push_accident(self, accident_data):
        """推送事故事件"""
        await self.ws_manager.broadcast('accidents', {
            'type': 'new_accident',
            'accident': accident_data
        })
    
    async def push_statistics(self, stats_data):
        """推送统计数据"""
        await self.ws_manager.broadcast('statistics', {
            'type': 'stats_update',
            'data': stats_data
        })
    
    async def push_system_status(self, status_data):
        """推送系统状态"""
        await self.ws_manager.broadcast('system_status', {
            'type': 'status_update',
            'data': status_data
        })

class WebSocketServer:
    """WebSocket服务器"""
    
    def __init__(self, host='127.0.0.1', port=5502):
        self.host = host
        self.port = port
        self.manager = WebSocketManager()
        self.pusher = RealTimeDataPusher(self.manager)
        self.server = None
        
    async def start(self):
        """启动WebSocket服务器"""
        logger.info(f"启动WebSocket服务器: ws://{self.host}:{self.port}")
        
        self.server = await websockets.serve(
            self.manager.register,
            self.host,
            self.port,
            ping_interval=30,  # 30秒心跳
            ping_timeout=10    # 10秒超时
        )
        
        logger.info("WebSocket服务器启动成功")
        
        # 启动统计推送任务
        asyncio.create_task(self._periodic_stats_push())
        
        return self.server
    
    async def stop(self):
        """停止WebSocket服务器"""
        if self.server:
            self.server.close()
            await self.server.wait_closed()
            logger.info("WebSocket服务器已停止")
    
    async def _periodic_stats_push(self):
        """定期推送统计信息"""
        while self.manager.running:
            try:
                # 推送连接统计
                stats = self.manager.get_stats()
                await self.pusher.push_system_status({
                    'websocket_stats': stats,
                    'timestamp': datetime.now().isoformat()
                })
                
                await asyncio.sleep(30)  # 每30秒推送一次
                
            except Exception as e:
                logger.error(f"统计推送异常: {e}")
                await asyncio.sleep(5)

# 全局WebSocket服务器实例
ws_server = None
ws_pusher = None

def start_websocket_server():
    """启动WebSocket服务器（在新线程中）"""
    global ws_server, ws_pusher
    
    def run_server():
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        async def main():
            global ws_server, ws_pusher
            ws_server = WebSocketServer()
            ws_pusher = ws_server.pusher
            
            await ws_server.start()
            
            # 保持运行
            try:
                await asyncio.Future()  # 永远等待
            except asyncio.CancelledError:
                await ws_server.stop()
        
        try:
            loop.run_until_complete(main())
        except KeyboardInterrupt:
            logger.info("WebSocket服务器被中断")
        finally:
            loop.close()
    
    # 在新线程中启动
    thread = threading.Thread(target=run_server, daemon=True)
    thread.start()
    
    # 等待服务器启动
    time.sleep(2)
    
    logger.info("WebSocket服务器线程已启动")
    return thread

def get_websocket_pusher():
    """获取WebSocket推送器"""
    global ws_pusher
    return ws_pusher

# WebSocket客户端示例代码
WEBSOCKET_CLIENT_EXAMPLE = '''
// JavaScript WebSocket客户端示例
class HighwayWebSocket {
    constructor(url = 'ws://127.0.0.1:5502') {
        this.url = url
        this.ws = null
        this.reconnectInterval = 5000
        this.subscriptions = new Set()
    }
    
    connect() {
        this.ws = new WebSocket(this.url)
        
        this.ws.onopen = () => {
            console.log('WebSocket连接成功')
            // 重新订阅之前的主题
            this.subscriptions.forEach(topic => {
                this.subscribe(topic)
            })
        }
        
        this.ws.onmessage = (event) => {
            const data = JSON.parse(event.data)
            this.handleMessage(data)
        }
        
        this.ws.onclose = () => {
            console.log('WebSocket连接关闭，尝试重连...')
            setTimeout(() => this.connect(), this.reconnectInterval)
        }
        
        this.ws.onerror = (error) => {
            console.error('WebSocket错误:', error)
        }
    }
    
    subscribe(topic) {
        this.subscriptions.add(topic)
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify({
                type: 'subscribe',
                topic: topic
            }))
        }
    }
    
    unsubscribe(topic) {
        this.subscriptions.delete(topic)
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify({
                type: 'unsubscribe',
                topic: topic
            }))
        }
    }
    
    handleMessage(data) {
        switch(data.type) {
            case 'broadcast':
                this.onBroadcast(data.topic, data.data)
                break
            case 'welcome':
                console.log('收到欢迎消息:', data.message)
                break
            case 'subscription_confirmed':
                console.log('订阅确认:', data.topic)
                break
        }
    }
    
    onBroadcast(topic, data) {
        // 处理广播消息
        console.log(`收到广播 [${topic}]:`, data)
        
        if (topic === 'detection_all') {
            this.onDetectionUpdate(data)
        } else if (topic === 'alarms') {
            this.onAlarmUpdate(data)
        } else if (topic === 'violations') {
            this.onViolationUpdate(data)
        } else if (topic === 'accidents') {
            this.onAccidentUpdate(data)
        }
    }
    
    onDetectionUpdate(data) {
        // 更新检测数据显示
        console.log(`监控点${data.monitor_id}车辆数: ${data.vehicle_count}`)
    }
    
    onAlarmUpdate(data) {
        // 显示警报通知
        console.log('新警报:', data.alarm)
    }
    
    onViolationUpdate(data) {
        // 显示违规通知
        console.log('违规事件:', data.violation)
    }
    
    onAccidentUpdate(data) {
        // 显示事故通知
        console.log('事故事件:', data.accident)
    }
}

// 使用示例
const ws = new HighwayWebSocket()
ws.connect()

// 订阅所有检测更新
ws.subscribe('detection_all')

// 订阅特定监控点
ws.subscribe('detection_1')

// 订阅警报
ws.subscribe('alarms')

// 订阅违规事件
ws.subscribe('violations')

// 订阅事故事件
ws.subscribe('accidents')
'''

if __name__ == "__main__":
    # 测试WebSocket服务器
    print("启动WebSocket服务器测试...")
    
    # 启动服务器
    thread = start_websocket_server()
    
    try:
        # 模拟数据推送
        time.sleep(3)
        
        pusher = get_websocket_pusher()
        if pusher:
            # 模拟推送检测数据
            asyncio.run(pusher.push_detection_update(1, {
                'vehicle_count': 15,
                'detections': [],
                'tracked_objects': [],
                'timestamp': datetime.now().isoformat()
            }))
            
            print("测试数据已推送")
        
        # 保持运行
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("停止WebSocket服务器")
