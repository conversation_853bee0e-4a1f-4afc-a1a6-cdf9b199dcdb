#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多目标追踪系统修复测试脚本
测试修复后的多目标追踪功能是否正常工作
"""

import sys
import os
import traceback
from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import QTimer

def test_target_tracker_import():
    """测试TargetTracker类的导入和基本功能"""
    try:
        from classes.target_tracker import TargetTracker
        print("✓ TargetTracker 导入成功")

        # 测试实例化
        tracker = TargetTracker()
        print("✓ TargetTracker 实例化成功")

        # 测试信号是否存在
        assert hasattr(tracker, 'target_image_signal'), "缺少 target_image_signal"
        assert hasattr(tracker, 'status_signal'), "缺少 status_signal"
        assert hasattr(tracker, 'tracking_lost_signal'), "缺少 tracking_lost_signal"
        print("✓ TargetTracker 信号检查通过")

        # 测试方法是否存在
        assert hasattr(tracker, 'process_frame'), "缺少 process_frame 方法"
        assert hasattr(tracker, 'set_target_id'), "缺少 set_target_id 方法"
        assert hasattr(tracker, 'update_frame'), "缺少 update_frame 方法"
        print("✓ TargetTracker 方法检查通过")

        # 测试多目标追踪方法
        assert hasattr(tracker, 'start_multi_tracking'), "缺少 start_multi_tracking 方法"
        assert hasattr(tracker, 'stop_multi_tracking'), "缺少 stop_multi_tracking 方法"
        assert hasattr(tracker, 'update_multi_tracking'), "缺少 update_multi_tracking 方法"
        print("✓ TargetTracker 多目标追踪方法检查通过")

        return True
    except Exception as e:
        print(f"✗ TargetTracker 测试失败: {str(e)}")
        traceback.print_exc()
        return False

def test_multi_tracking_dialog_import():
    """测试多目标追踪对话框的导入"""
    try:
        from ui.dialog.multi_target_tracking_dialog import MultiTargetTrackingDialog
        print("✓ MultiTargetTrackingDialog 导入成功")
        return True
    except Exception as e:
        print(f"✗ MultiTargetTrackingDialog 导入失败: {str(e)}")
        traceback.print_exc()
        return False

def test_multi_tracking_thread_import():
    """测试多目标追踪线程的导入"""
    try:
        from ui.dialog.multi_tracking_thread import MultiTrackingThread
        print("✓ MultiTrackingThread 导入成功")

        # 测试实例化（使用虚拟视频源）
        thread = MultiTrackingThread(video_source=0)
        print("✓ MultiTrackingThread 实例化成功")

        # 测试信号是否存在
        assert hasattr(thread, 'update_signal'), "缺少 update_signal"
        assert hasattr(thread, 'error_signal'), "缺少 error_signal"
        assert hasattr(thread, 'status_signal'), "缺少 status_signal"
        print("✓ MultiTrackingThread 信号检查通过")

        return True
    except Exception as e:
        print(f"✗ MultiTrackingThread 测试失败: {str(e)}")
        traceback.print_exc()
        return False

def test_yolo_predictor_integration():
    """测试YOLO预测器的多目标追踪集成"""
    try:
        from classes.yolo import YoloPredictor
        print("✓ YoloPredictor 导入成功")

        # 测试实例化
        predictor = YoloPredictor()
        print("✓ YoloPredictor 实例化成功")

        # 测试多目标追踪属性
        assert hasattr(predictor, 'multi_tracking'), "缺少 multi_tracking 属性"
        assert hasattr(predictor, 'multi_tracking_ids'), "缺少 multi_tracking_ids 属性"
        assert hasattr(predictor, 'detected_objects'), "缺少 detected_objects 属性"
        print("✓ YoloPredictor 多目标追踪属性检查通过")

        # 测试方法是否存在
        assert hasattr(predictor, 'update_detected_objects'), "缺少 update_detected_objects 方法"
        print("✓ YoloPredictor 方法检查通过")

        return True
    except Exception as e:
        print(f"✗ YoloPredictor 测试失败: {str(e)}")
        traceback.print_exc()
        return False

def test_main_window_integration():
    """测试主窗口的多目标追踪集成"""
    try:
        # 这里只测试导入，不实际创建窗口
        from main import MainWindow
        print("✓ MainWindow 导入成功")

        # 检查是否有必要的方法
        methods_to_check = [
            'enable_multi_tracking',
            'on_multi_tracking_started',
            'on_multi_tracking_stopped',
            'on_target_added',
            'on_target_removed',
            'lock_id_selection'
        ]

        for method_name in methods_to_check:
            assert hasattr(MainWindow, method_name), f"缺少 {method_name} 方法"

        print("✓ MainWindow 方法检查通过")
        return True
    except Exception as e:
        print(f"✗ MainWindow 测试失败: {str(e)}")
        traceback.print_exc()
        return False

def run_all_tests():
    """运行所有测试"""
    print("=" * 60)
    print("多目标追踪系统修复测试")
    print("=" * 60)

    tests = [
        ("TargetTracker 类测试", test_target_tracker_import),
        ("MultiTargetTrackingDialog 测试", test_multi_tracking_dialog_import),
        ("MultiTrackingThread 测试", test_multi_tracking_thread_import),
        ("YoloPredictor 集成测试", test_yolo_predictor_integration),
        ("MainWindow 集成测试", test_main_window_integration),
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        if test_func():
            passed += 1
        print()

    print("=" * 60)
    print(f"测试结果: {passed}/{total} 通过")

    if passed == total:
        print("🎉 所有测试通过！多目标追踪系统修复成功！")
        return True
    else:
        print("❌ 部分测试失败，需要进一步修复")
        return False

def show_test_results_dialog(success):
    """显示测试结果对话框"""
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)

    msg_box = QMessageBox()
    msg_box.setWindowTitle("多目标追踪系统测试结果")

    if success:
        msg_box.setIcon(QMessageBox.Information)
        msg_box.setText("🎉 测试通过！")
        msg_box.setInformativeText("多目标追踪系统修复成功，所有组件都能正常工作。")
    else:
        msg_box.setIcon(QMessageBox.Warning)
        msg_box.setText("❌ 测试失败")
        msg_box.setInformativeText("部分组件仍有问题，请查看控制台输出了解详情。")

    msg_box.exec()

if __name__ == "__main__":
    # 设置工作目录
    if os.path.exists("c:/Users/<USER>/Desktop/yolo"):
        os.chdir("c:/Users/<USER>/Desktop/yolo")

    # 运行测试
    success = run_all_tests()

    # 显示结果对话框
    show_test_results_dialog(success)
