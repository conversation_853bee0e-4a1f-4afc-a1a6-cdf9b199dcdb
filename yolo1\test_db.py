# -*- coding: utf-8 -*-
# @Description : 数据库连接测试脚本
# @Date : 2025年6月20日

import os
import sys

# 添加项目根目录到Python路径
sys.path.append('.')

print("=" * 60)
print("基于Yolov8与ByteTrack的高速公路智慧监控平台 - 数据库连接测试")
print("=" * 60)

# 加载环境变量
try:
    from dotenv import load_dotenv
    load_dotenv(override=True, dotenv_path='config/end-back.env')
    print("✓ 环境变量加载成功")
except ImportError:
    print("✗ python-dotenv 未安装，请运行: pip install python-dotenv")
    sys.exit(1)

# 检查环境变量
print("\n检查环境变量:")
env_vars = ['MYSQL_HOST', 'MYSQL_PORT', 'MYSQL_USER', 'MYSQL_PASSWORD', 'MYSQL_DATABASE']
for var in env_vars:
    value = os.getenv(var) or os.getenv(var.replace('_', '_').lower())
    if value:
        if 'PASSWORD' in var:
            print(f"  {var}: {'*' * len(value)}")
        else:
            print(f"  {var}: {value}")
    else:
        print(f"  {var}: 未设置")

# 测试PyMySQL导入
try:
    import pymysql
    print("✓ PyMySQL 导入成功")
except ImportError:
    print("✗ PyMySQL 未安装，请运行: pip install pymysql")
    sys.exit(1)

# 测试基本数据库连接
print("\n测试基本数据库连接:")
try:
    config = {
        'host': os.getenv('MYSQL_HOST', '127.0.0.1'),
        'port': int(os.getenv('MYSQL_PORT', 3306)),
        'user': os.getenv('MYSQL_USER', os.getenv('MYSQL_user', 'root')),
        'password': os.getenv('MYSQL_PASSWORD', os.getenv('MYSQL_password', '123456')),
        'charset': 'utf8mb4',
        'autocommit': True,
        'cursorclass': pymysql.cursors.DictCursor
    }
    
    print(f"  连接参数: {config['user']}@{config['host']}:{config['port']}")
    
    # 测试连接到MySQL服务器
    connection = pymysql.connect(**config)
    print("✓ MySQL服务器连接成功")
    
    with connection.cursor() as cursor:
        cursor.execute("SELECT VERSION() as version")
        result = cursor.fetchone()
        print(f"✓ MySQL版本: {result['version']}")
        
        # 检查数据库是否存在
        database_name = os.getenv('MYSQL_DATABASE', os.getenv('MYSQL_db', 'yolo'))
        cursor.execute("SHOW DATABASES LIKE %s", (database_name,))
        db_exists = cursor.fetchone()
        
        if db_exists:
            print(f"✓ 数据库 '{database_name}' 存在")
        else:
            print(f"✗ 数据库 '{database_name}' 不存在")
            print(f"  请创建数据库: CREATE DATABASE {database_name} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;")
            connection.close()
            sys.exit(1)
    
    connection.close()
    
except pymysql.Error as e:
    print(f"✗ 数据库连接失败: {e}")
    print("\n可能的解决方案:")
    print("1. 检查MySQL服务是否启动")
    print("2. 检查用户名和密码是否正确")
    print("3. 检查主机和端口是否正确")
    sys.exit(1)

# 测试连接到指定数据库
print("\n测试连接到指定数据库:")
try:
    config['database'] = os.getenv('MYSQL_DATABASE', os.getenv('MYSQL_db', 'yolo'))
    connection = pymysql.connect(**config)
    print(f"✓ 连接到数据库 '{config['database']}' 成功")
    
    with connection.cursor() as cursor:
        # 检查表是否存在
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        
        if tables:
            print(f"✓ 数据库中有 {len(tables)} 个表:")
            for table in tables:
                table_name = list(table.values())[0]
                cursor.execute(f"SELECT COUNT(*) as count FROM {table_name}")
                count = cursor.fetchone()
                print(f"    - {table_name}: {count['count']} 条记录")
        else:
            print("✗ 数据库中没有表")
            print("  请导入数据库结构: mysql -u root -p yolo < yolo.sql")
    
    connection.close()
    
except pymysql.Error as e:
    print(f"✗ 连接数据库失败: {e}")
    sys.exit(1)

# 测试后端数据库工具
print("\n测试后端数据库工具:")
try:
    from backend.utils.database import init_database, get_db_connection, db_manager
    
    print("✓ 后端数据库模块导入成功")
    
    # 测试数据库管理器
    if db_manager.test_connection():
        print("✓ 数据库管理器连接测试成功")
    else:
        print("✗ 数据库管理器连接测试失败")
    
    # 测试初始化
    if init_database():
        print("✓ 数据库初始化成功")
        
        # 测试查询
        with get_db_connection() as db:
            # 测试用户表
            users = db.get_list("SELECT id, username, grade FROM user LIMIT 5")
            print(f"✓ 用户表查询成功，共 {len(users)} 个用户")
            for user in users:
                print(f"    - {user['username']} ({user['grade']})")
            
            # 测试监控点表
            monitors = db.get_list("SELECT id, location, highway_section, connection_status FROM monitor LIMIT 5")
            print(f"✓ 监控点表查询成功，共 {len(monitors)} 个监控点")
            for monitor in monitors:
                print(f"    - {monitor['location']} ({monitor['highway_section']}) - {monitor['connection_status']}")
            
            # 测试警报表
            alarms = db.get_list("SELECT id, location, description, create_time FROM alarm ORDER BY create_time DESC LIMIT 3")
            print(f"✓ 警报表查询成功，共 {len(alarms)} 条最新警报")
            for alarm in alarms:
                print(f"    - {alarm['location']}: {alarm['description']} ({alarm['create_time']})")
    else:
        print("✗ 数据库初始化失败")
        
except ImportError as e:
    print(f"✗ 后端模块导入失败: {e}")
    print("  请检查backend目录是否存在，以及依赖是否安装完整")
except Exception as e:
    print(f"✗ 后端数据库工具测试失败: {e}")

# 测试健康检查
print("\n测试数据库健康检查:")
try:
    from backend.utils.database import health_check
    
    health_status = health_check()
    if health_status.get('status') == 'healthy':
        print("✓ 数据库健康检查通过")
        print(f"    响应时间: {health_status.get('response_time_ms', 0):.2f}ms")
        print(f"    连接数: {health_status.get('connections', 0)}")
    else:
        print("✗ 数据库健康检查失败")
        print(f"    错误: {health_status.get('error', '未知错误')}")
        
except Exception as e:
    print(f"✗ 健康检查失败: {e}")

print("\n" + "=" * 60)
print("数据库连接测试完成")
print("=" * 60)

# 给出下一步建议
print("\n下一步操作建议:")
print("1. 如果所有测试都通过，可以启动应用: python app_main.py")
print("2. 如果有表不存在，请导入数据库: mysql -u root -p yolo < yolo.sql")
print("3. 如果连接失败，请检查MySQL服务和配置文件")
print("4. 访问 http://127.0.0.1:5500 查看应用是否正常运行")
