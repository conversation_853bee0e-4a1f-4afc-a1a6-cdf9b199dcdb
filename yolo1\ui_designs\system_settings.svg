<svg width="1200" height="800" viewBox="0 0 1200 800" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景渐变 -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8fafc;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="navGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#1e293b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#334155;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="buttonGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
  </defs>

  <!-- 主背景 -->
  <rect width="1200" height="800" fill="url(#bgGradient)"/>

  <!-- 标题栏 -->
  <rect x="0" y="0" width="1200" height="60" fill="url(#navGradient)" filter="url(#shadow)"/>
  <text x="30" y="35" font-family="Microsoft YaHei, sans-serif" font-size="18" font-weight="600" fill="#ffffff">
    ⚙️ 系统设置
  </text>

  <!-- 主容器 -->
  <rect x="20" y="80" width="1160" height="700" rx="12" fill="url(#cardGradient)" filter="url(#shadow)"/>

  <!-- 左侧导航菜单 -->
  <rect x="40" y="100" width="280" height="660" rx="8" fill="url(#navGradient)" filter="url(#shadow)"/>
  
  <!-- 导航标题 -->
  <text x="60" y="130" font-family="Microsoft YaHei, sans-serif" font-size="16" font-weight="600" fill="#ffffff">
    📋 导航菜单
  </text>

  <!-- 导航菜单项 -->
  <g id="navItems">
    <!-- 基础设置 (激活状态) -->
    <rect x="50" y="150" width="260" height="40" rx="6" fill="#3b82f6" opacity="0.8"/>
    <circle cx="70" cy="170" r="3" fill="#ffffff"/>
    <text x="85" y="175" font-family="Microsoft YaHei, sans-serif" font-size="14" font-weight="500" fill="#ffffff">基础设置</text>
    
    <!-- 算法配置 -->
    <circle cx="70" cy="210" r="3" fill="#94a3b8"/>
    <text x="85" y="215" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#cbd5e1">算法配置</text>
    
    <!-- 预警设置 -->
    <circle cx="70" cy="250" r="3" fill="#94a3b8"/>
    <text x="85" y="255" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#cbd5e1">预警设置</text>
    
    <!-- 用户管理 -->
    <circle cx="70" cy="290" r="3" fill="#94a3b8"/>
    <text x="85" y="295" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#cbd5e1">用户管理</text>
    
    <!-- 日志管理 -->
    <circle cx="70" cy="330" r="3" fill="#94a3b8"/>
    <text x="85" y="335" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#cbd5e1">日志管理</text>
    
    <!-- 备份恢复 -->
    <circle cx="70" cy="370" r="3" fill="#94a3b8"/>
    <text x="85" y="375" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#cbd5e1">备份恢复</text>
  </g>

  <!-- 右侧配置面板 -->
  <rect x="340" y="100" width="820" height="660" rx="8" fill="url(#cardGradient)" stroke="#e2e8f0" stroke-width="1"/>

  <!-- 基础配置区域 -->
  <g id="basicConfig">
    <text x="360" y="130" font-family="Microsoft YaHei, sans-serif" font-size="16" font-weight="600" fill="#1e293b">
      🔧 基础配置
    </text>
    
    <!-- 系统名称 -->
    <text x="360" y="165" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#475569">系统名称:</text>
    <rect x="450" y="150" width="300" height="30" rx="4" fill="#ffffff" stroke="#d1d5db" stroke-width="1"/>
    <text x="460" y="170" font-family="Microsoft YaHei, sans-serif" font-size="12" fill="#374151">基于Yolov8与ByteTrack的高速公路智慧监控平台</text>
    
    <!-- 系统版本 -->
    <text x="360" y="205" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#475569">系统版本:</text>
    <rect x="450" y="190" width="150" height="30" rx="4" fill="#ffffff" stroke="#d1d5db" stroke-width="1"/>
    <text x="460" y="210" font-family="Microsoft YaHei, sans-serif" font-size="12" fill="#374151">v2.0.1</text>
    
    <!-- 运行模式 -->
    <text x="360" y="245" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#475569">运行模式:</text>
    <rect x="450" y="230" width="150" height="30" rx="4" fill="#ffffff" stroke="#d1d5db" stroke-width="1"/>
    <text x="460" y="250" font-family="Microsoft YaHei, sans-serif" font-size="12" fill="#374151">生产模式</text>
    <polygon points="585,240 590,245 585,250" fill="#6b7280"/>
    
    <!-- 日志级别 -->
    <text x="360" y="285" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#475569">日志级别:</text>
    <rect x="450" y="270" width="150" height="30" rx="4" fill="#ffffff" stroke="#d1d5db" stroke-width="1"/>
    <text x="460" y="290" font-family="Microsoft YaHei, sans-serif" font-size="12" fill="#374151">INFO</text>
    <polygon points="585,280 590,285 585,290" fill="#6b7280"/>
  </g>

  <!-- YOLO算法配置区域 -->
  <g id="yoloConfig">
    <text x="360" y="340" font-family="Microsoft YaHei, sans-serif" font-size="16" font-weight="600" fill="#1e293b">
      🤖 YOLO算法配置
    </text>
    
    <!-- 默认模型 -->
    <text x="360" y="375" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#475569">默认模型:</text>
    <rect x="450" y="360" width="200" height="30" rx="4" fill="#ffffff" stroke="#d1d5db" stroke-width="1"/>
    <text x="460" y="380" font-family="Microsoft YaHei, sans-serif" font-size="12" fill="#374151">yolov8n.pt</text>
    <polygon points="635,370 640,375 635,380" fill="#6b7280"/>
    
    <!-- 置信度阈值 -->
    <text x="360" y="415" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#475569">置信度阈值:</text>
    <text x="460" y="415" font-family="Microsoft YaHei, sans-serif" font-size="12" fill="#374151">0.5</text>
    <rect x="500" y="405" width="200" height="8" rx="4" fill="#e5e7eb"/>
    <rect x="500" y="405" width="100" height="8" rx="4" fill="#3b82f6"/>
    <circle cx="600" cy="409" r="6" fill="#3b82f6"/>
    
    <!-- IOU阈值 -->
    <text x="360" y="455" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#475569">IOU阈值:</text>
    <text x="460" y="455" font-family="Microsoft YaHei, sans-serif" font-size="12" fill="#374151">0.4</text>
    <rect x="500" y="445" width="200" height="8" rx="4" fill="#e5e7eb"/>
    <rect x="500" y="445" width="80" height="8" rx="4" fill="#10b981"/>
    <circle cx="580" cy="449" r="6" fill="#10b981"/>
    
    <!-- 最大检测数 -->
    <text x="360" y="495" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#475569">最大检测数:</text>
    <rect x="460" y="480" width="100" height="30" rx="4" fill="#ffffff" stroke="#d1d5db" stroke-width="1"/>
    <text x="470" y="500" font-family="Microsoft YaHei, sans-serif" font-size="12" fill="#374151">100</text>
  </g>

  <!-- 预警配置区域 -->
  <g id="alertConfig">
    <text x="360" y="550" font-family="Microsoft YaHei, sans-serif" font-size="16" font-weight="600" fill="#1e293b">
      🔔 预警配置
    </text>
    
    <!-- SMTP服务器 -->
    <text x="360" y="585" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#475569">SMTP服务器:</text>
    <rect x="470" y="570" width="250" height="30" rx="4" fill="#ffffff" stroke="#d1d5db" stroke-width="1"/>
    <text x="480" y="590" font-family="Microsoft YaHei, sans-serif" font-size="12" fill="#374151">smtp.example.com</text>
    
    <!-- 邮箱账号 -->
    <text x="360" y="625" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#475569">邮箱账号:</text>
    <rect x="450" y="610" width="250" height="30" rx="4" fill="#ffffff" stroke="#d1d5db" stroke-width="1"/>
    <text x="460" y="630" font-family="Microsoft YaHei, sans-serif" font-size="12" fill="#374151"><EMAIL></text>
    
    <!-- 短信接口 -->
    <text x="360" y="665" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#475569">短信接口:</text>
    <rect x="450" y="650" width="120" height="30" rx="4" fill="#dcfce7" stroke="#16a34a" stroke-width="1"/>
    <text x="460" y="670" font-family="Microsoft YaHei, sans-serif" font-size="12" fill="#16a34a">已配置 ✓</text>
    
    <!-- Webhook URL -->
    <text x="360" y="705" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#475569">Webhook URL:</text>
    <rect x="470" y="690" width="280" height="30" rx="4" fill="#ffffff" stroke="#d1d5db" stroke-width="1"/>
    <text x="480" y="710" font-family="Microsoft YaHei, sans-serif" font-size="12" fill="#374151">https://webhook...</text>
  </g>

  <!-- 操作按钮区域 -->
  <g id="actionButtons">
    <!-- 保存设置按钮 -->
    <rect x="360" y="730" width="100" height="35" rx="6" fill="url(#buttonGradient)" filter="url(#shadow)"/>
    <text x="385" y="752" font-family="Microsoft YaHei, sans-serif" font-size="12" font-weight="500" fill="#ffffff">💾 保存设置</text>
    
    <!-- 重置按钮 -->
    <rect x="480" y="730" width="80" height="35" rx="6" fill="#6b7280" filter="url(#shadow)"/>
    <text x="505" y="752" font-family="Microsoft YaHei, sans-serif" font-size="12" font-weight="500" fill="#ffffff">🔄 重置</text>
    
    <!-- 测试连接按钮 -->
    <rect x="580" y="730" width="100" height="35" rx="6" fill="#10b981" filter="url(#shadow)"/>
    <text x="605" y="752" font-family="Microsoft YaHei, sans-serif" font-size="12" font-weight="500" fill="#ffffff">🧪 测试连接</text>
  </g>

  <!-- 装饰性元素 -->
  <circle cx="1150" cy="130" r="20" fill="#f1f5f9" opacity="0.5"/>
  <circle cx="1120" cy="160" r="15" fill="#e2e8f0" opacity="0.3"/>
  <circle cx="1140" cy="180" r="10" fill="#cbd5e1" opacity="0.4"/>

</svg>