# -*- coding: utf-8 -*-
"""
行人检测过滤功能测试脚本
测试YOLOv8是否能正确过滤出行人类别并忽略车辆
"""

import numpy as np
import cv2
from ultralytics import YOLO
from classes.yolo import YoloPredictor, create_detections_from_result

def test_pedestrian_filter():
    """测试行人过滤功能"""
    print("开始测试行人检测过滤功能...")
    
    try:
        # 模拟创建一个包含多种类别的检测结果
        print("模拟检测结果:")
        
        # 模拟检测数据 (xyxy, confidence, class_id, tracker_id)
        # class_id: 0=person, 2=car, 3=motorcycle, 5=bus, 7=truck
        mock_data = [
            ([100, 100, 200, 300], 0.85, 0, 1),  # 行人
            ([300, 150, 500, 400], 0.92, 2, 2),  # 汽车
            ([600, 200, 800, 500], 0.78, 0, 3),  # 行人
            ([900, 100, 1100, 350], 0.89, 5, 4), # 公交车
            ([50, 400, 150, 600], 0.73, 0, 5),   # 行人
            ([1200, 300, 1400, 550], 0.81, 7, 6) # 卡车
        ]
        
        # 显示原始检测结果
        print("\n原始检测结果:")
        for i, (bbox, conf, cls_id, track_id) in enumerate(mock_data):
            class_names = {0: "行人", 2: "汽车", 3: "摩托车", 5: "公交车", 7: "卡车"}
            print(f"  ID:{track_id} - {class_names.get(cls_id, f'类别{cls_id}')} (置信度: {conf:.2f})")
        
        # 创建YoloPredictor实例并启用行人检测
        predictor = YoloPredictor()
        predictor.pedestrian_detection_enabled = True
        
        print(f"\n启用行人检测过滤 (pedestrian_detection_enabled = {predictor.pedestrian_detection_enabled})")
        
        # 模拟过滤逻辑
        print("\n应用行人过滤逻辑后的结果:")
        filtered_count = 0
        
        for bbox, conf, cls_id, track_id in mock_data:
            if cls_id == 0:  # 只保留行人
                print(f"  保留: ID:{track_id} - 行人 (置信度: {conf:.2f})")
                filtered_count += 1
            else:
                class_names = {2: "汽车", 3: "摩托车", 5: "公交车", 7: "卡车"}
                print(f"  过滤: ID:{track_id} - {class_names.get(cls_id, f'类别{cls_id}')} (置信度: {conf:.2f})")
        
        print(f"\n过滤结果统计:")
        print(f"  原始检测数量: {len(mock_data)}")
        print(f"  过滤后数量: {filtered_count}")
        print(f"  行人数量: {filtered_count}")
        print(f"  车辆数量: {len(mock_data) - filtered_count} (已过滤)")
        
        # 验证过滤逻辑是否正确
        expected_pedestrians = sum(1 for _, _, cls_id, _ in mock_data if cls_id == 0)
        
        if filtered_count == expected_pedestrians:
            print("\n测试通过: 行人过滤功能工作正常")
            print("   系统成功识别并保留了所有行人，过滤了所有车辆")
            return True
        else:
            print(f"\n测试失败: 预期行人数量 {expected_pedestrians}，实际过滤后数量 {filtered_count}")
            return False
            
    except Exception as e:
        print(f"测试过程中出现错误: {str(e)}")
        return False

def test_detection_modes():
    """测试检测模式切换"""
    print("\n测试检测模式切换功能...")
    
    predictor = YoloPredictor()
    
    # 测试默认模式（车辆检测）
    print(f"默认模式 - pedestrian_detection_enabled: {getattr(predictor, 'pedestrian_detection_enabled', False)}")
    
    # 切换到行人检测模式
    predictor.pedestrian_detection_enabled = True
    predictor.violation_detection_enabled = False
    predictor.multi_tracking = False
    predictor.show_speed = False
    
    print(f"行人检测模式 - pedestrian_detection_enabled: {predictor.pedestrian_detection_enabled}")
    print("   其他功能已禁用，专注行人检测")
    
    # 恢复正常模式
    predictor.pedestrian_detection_enabled = False
    predictor.show_speed = True
    
    print(f"恢复正常模式 - pedestrian_detection_enabled: {predictor.pedestrian_detection_enabled}")
    print("   已恢复车辆检测功能")
    
    print("模式切换测试完成")

if __name__ == "__main__":
    print("YOLOv8行人检测过滤功能测试")
    print("=" * 50)
    
    # 测试过滤功能
    success = test_pedestrian_filter()
    
    # 测试模式切换
    test_detection_modes()
    
    print("\n" + "=" * 50)
    if success:
        print("所有测试通过! 行人检测过滤功能正常工作")
        print("使用说明:")
        print("   1. 点击'行人闯入检测'按钮启用行人检测模式")
        print("   2. 系统将自动过滤车辆，只显示和跟踪行人")
        print("   3. 停止检测后系统自动恢复正常模式")
    else:
        print("测试未完全通过，请检查代码实现")