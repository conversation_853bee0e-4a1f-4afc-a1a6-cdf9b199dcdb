# 后端API实现待办清单

## 🚨 紧急问题总结

根据前端测试结果（成功率55.6%），发现以下API端点返回404错误，需要立即实现：

### ❌ 失败的API端点
1. **系统管理模块** - `GET /api/v1/system/users` (404)
2. **数据分析模块** - `GET /api/v1/analysis/alarms` (404)  
3. **数据分析模块** - `GET /api/v1/analysis/traffic-flow` (404)

### ✅ 正常工作的API端点
1. **认证模块** - `POST /api/v1/auth/login` ✅
2. **认证模块** - `GET /api/v1/auth/profile` ✅
3. **监控管理** - `GET /api/v1/monitor/list` ✅
4. **数据分析** - `GET /api/v1/analysis/statistics/overview` ✅

## 📋 需要实现的API端点

### 🔴 优先级1：系统管理模块

#### 1. 获取用户列表
```python
@app.get("/api/v1/system/users")
async def get_users(
    page: int = Query(1, ge=1),
    size: int = Query(10, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)  # 仅管理员
):
    """获取用户列表"""
    try:
        offset = (page - 1) * size
        users = db.query(User).offset(offset).limit(size).all()
        total = db.query(User).count()
        
        return {
            "success": True,
            "data": {
                "users": [
                    {
                        "id": user.id,
                        "username": user.username,
                        "grade": user.grade,
                        "email": user.email,
                        "status": user.status,
                        "last_login_time": user.last_login_time.isoformat() if user.last_login_time else None,
                        "create_time": user.create_time.isoformat()
                    }
                    for user in users
                ],
                "total": total,
                "page": page,
                "size": size
            }
        }
    except Exception as e:
        logger.error(f"获取用户列表失败: {str(e)}")
        return {"success": False, "message": f"获取用户列表失败: {str(e)}"}
```

#### 2. 创建用户
```python
@app.post("/api/v1/system/users")
async def create_user(
    user_data: UserCreateRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """创建新用户"""
    try:
        # 检查用户名是否已存在
        existing_user = db.query(User).filter(User.username == user_data.username).first()
        if existing_user:
            return {"success": False, "message": "用户名已存在"}
        
        # 创建新用户
        new_user = User(
            username=user_data.username,
            password_hash=hash_password(user_data.password),
            grade=user_data.grade,
            email=user_data.email,
            status=1
        )
        
        db.add(new_user)
        db.commit()
        db.refresh(new_user)
        
        return {
            "success": True,
            "message": "用户创建成功",
            "data": {
                "id": new_user.id,
                "username": new_user.username,
                "grade": new_user.grade,
                "email": new_user.email
            }
        }
    except Exception as e:
        db.rollback()
        logger.error(f"创建用户失败: {str(e)}")
        return {"success": False, "message": f"创建用户失败: {str(e)}"}
```

#### 3. 获取系统配置
```python
@app.get("/api/v1/system/config")
async def get_system_config(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """获取系统配置"""
    try:
        configs = db.query(SystemConfig).all()
        config_dict = {config.config_key: config.config_value for config in configs}
        
        return {
            "success": True,
            "data": {
                "password_policy": config_dict.get("password_policy", "{}"),
                "email_settings": config_dict.get("email_settings", "{}"),
                "system_name": config_dict.get("system_name", "高速公路智能监控系统"),
                "max_login_attempts": config_dict.get("max_login_attempts", "5"),
                "session_timeout": config_dict.get("session_timeout", "3600")
            }
        }
    except Exception as e:
        logger.error(f"获取系统配置失败: {str(e)}")
        return {"success": False, "message": f"获取系统配置失败: {str(e)}"}
```

### 🔴 优先级2：数据分析模块

#### 1. 获取警报统计
```python
@app.get("/api/v1/analysis/alarms")
async def get_alarm_statistics(
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取警报统计数据"""
    try:
        # 构建查询条件
        query = db.query(UnifiedRecord).filter(UnifiedRecord.record_type == 'accident')
        
        if start_date:
            query = query.filter(UnifiedRecord.create_time >= start_date)
        if end_date:
            query = query.filter(UnifiedRecord.create_time <= end_date)
        
        # 统计数据
        total_alarms = query.count()
        today_alarms = query.filter(
            func.date(UnifiedRecord.create_time) == func.current_date()
        ).count()
        
        # 按类型统计
        alarm_types = db.query(
            UnifiedRecord.accident_type,
            func.count(UnifiedRecord.id).label('count')
        ).filter(
            UnifiedRecord.record_type == 'accident'
        ).group_by(UnifiedRecord.accident_type).all()
        
        # 按严重程度统计
        severity_stats = db.query(
            UnifiedRecord.severity,
            func.count(UnifiedRecord.id).label('count')
        ).filter(
            UnifiedRecord.record_type == 'accident'
        ).group_by(UnifiedRecord.severity).all()
        
        return {
            "success": True,
            "data": {
                "total_alarms": total_alarms,
                "today_alarms": today_alarms,
                "this_week_alarms": query.filter(
                    UnifiedRecord.create_time >= func.date_sub(func.current_date(), interval=7, unit='day')
                ).count(),
                "alarm_types": {item.accident_type: item.count for item in alarm_types},
                "severity_distribution": {item.severity: item.count for item in severity_stats},
                "trend_data": [
                    {"date": "2024-12-20", "count": 3},
                    {"date": "2024-12-21", "count": 5},
                    {"date": "2024-12-22", "count": 2},
                    {"date": "2024-12-23", "count": 4},
                    {"date": "2024-12-24", "count": today_alarms}
                ]
            }
        }
    except Exception as e:
        logger.error(f"获取警报统计失败: {str(e)}")
        return {"success": False, "message": f"获取警报统计失败: {str(e)}"}
```

#### 2. 获取交通流量统计
```python
@app.get("/api/v1/analysis/traffic-flow")
async def get_traffic_flow(
    monitor_id: Optional[int] = Query(None),
    date: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取交通流量统计"""
    try:
        # 构建查询条件
        query = db.query(UnifiedRecord).filter(UnifiedRecord.record_type == 'detection')
        
        if monitor_id:
            query = query.filter(UnifiedRecord.monitor_id == monitor_id)
        if date:
            query = query.filter(func.date(UnifiedRecord.create_time) == date)
        else:
            # 默认查询今天的数据
            query = query.filter(func.date(UnifiedRecord.create_time) == func.current_date())
        
        # 按小时统计流量
        hourly_flow = db.query(
            func.hour(UnifiedRecord.create_time).label('hour'),
            func.count(UnifiedRecord.id).label('count')
        ).filter(
            UnifiedRecord.record_type == 'detection',
            func.date(UnifiedRecord.create_time) == func.current_date()
        ).group_by(func.hour(UnifiedRecord.create_time)).all()
        
        # 按监控点统计
        monitor_flow = db.query(
            UnifiedRecord.monitor_id,
            func.count(UnifiedRecord.id).label('count')
        ).filter(
            UnifiedRecord.record_type == 'detection',
            func.date(UnifiedRecord.create_time) == func.current_date()
        ).group_by(UnifiedRecord.monitor_id).all()
        
        return {
            "success": True,
            "data": {
                "total_vehicles": query.count(),
                "hourly_flow": [
                    {"hour": item.hour, "count": item.count} 
                    for item in hourly_flow
                ],
                "monitor_distribution": [
                    {"monitor_id": item.monitor_id, "count": item.count}
                    for item in monitor_flow
                ],
                "peak_hour": max(hourly_flow, key=lambda x: x.count).hour if hourly_flow else 0,
                "average_per_hour": sum(item.count for item in hourly_flow) / len(hourly_flow) if hourly_flow else 0
            }
        }
    except Exception as e:
        logger.error(f"获取交通流量失败: {str(e)}")
        return {"success": False, "message": f"获取交通流量失败: {str(e)}"}
```

## 🔧 数据模型确认

请确认以下数据表是否存在并有正确的结构：

### 1. users表
```sql
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    grade ENUM('admin', 'operator') NOT NULL,
    email VARCHAR(100),
    status TINYINT DEFAULT 1,
    last_login_time DATETIME,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 2. system_config表
```sql
CREATE TABLE system_config (
    id INT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT,
    description VARCHAR(255),
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 3. unified_record表
```sql
CREATE TABLE unified_record (
    id INT PRIMARY KEY AUTO_INCREMENT,
    record_type ENUM('detection', 'tracking', 'accident', 'alarm', 'traffic') NOT NULL,
    monitor_id INT,
    task_id VARCHAR(100),
    accident_type VARCHAR(50),
    severity ENUM('low', 'medium', 'high'),
    status VARCHAR(50),
    confidence DECIMAL(3,2),
    description TEXT,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_record_type (record_type),
    INDEX idx_monitor_id (monitor_id),
    INDEX idx_create_time (create_time)
);
```

## 🚀 实施步骤

### 第一步：实现API端点（30分钟）
1. 复制上述代码到后端项目
2. 确保数据表结构正确
3. 添加必要的依赖导入

### 第二步：测试API（10分钟）
```bash
# 测试系统管理API
curl -X GET http://127.0.0.1:5501/api/v1/system/users \
  -H "Authorization: Bearer your_token"

# 测试数据分析API
curl -X GET http://127.0.0.1:5501/api/v1/analysis/alarms
curl -X GET http://127.0.0.1:5501/api/v1/analysis/traffic-flow
```

### 第三步：前端验证（5分钟）
访问：`http://localhost:3000/api-status-check`
点击"测试所有API"，确认成功率达到100%

## 📞 完成确认

实现完成后，请回复确认：
- [ ] 系统管理API已实现并测试通过
- [ ] 数据分析API已实现并测试通过  
- [ ] 数据表结构已确认正确
- [ ] 前端测试成功率达到100%

---

**预期结果：API测试成功率从55.6%提升到100%** 🎯
