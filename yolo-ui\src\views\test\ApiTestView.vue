<template>
  <div class="api-test-page">
    <a-card title="API连接测试">
      <a-space direction="vertical" fill>
        <a-alert type="info" show-icon>
          <template #title>API测试说明</template>
          <div>
            <p>后端服务地址: <code>http://127.0.0.1:5501</code></p>
            <p>前端代理地址: <code>/api/v1</code></p>
            <p>请确保后端服务正在运行</p>
          </div>
        </a-alert>
        
        <a-divider />
        
        <div class="test-section">
          <h3>连接状态测试</h3>
          <a-space>
            <a-button @click="testConnection" :loading="testing">
              测试连接
            </a-button>
            <a-tag :color="connectionStatus.color">
              {{ connectionStatus.text }}
            </a-tag>
          </a-space>
        </div>
        
        <a-divider />
        
        <div class="test-section">
          <h3>API接口测试</h3>
          <a-space direction="vertical" fill>
            <div v-for="api in apiTests" :key="api.name" class="api-test-item">
              <a-space>
                <a-button 
                  @click="testApi(api)" 
                  :loading="api.loading"
                  size="small"
                >
                  {{ api.name }}
                </a-button>
                <a-tag :color="api.status === 'success' ? 'green' : api.status === 'error' ? 'red' : 'gray'">
                  {{ api.status || '未测试' }}
                </a-tag>
                <span class="api-url">{{ api.url }}</span>
              </a-space>
              <div v-if="api.result" class="api-result">
                <pre>{{ JSON.stringify(api.result, null, 2) }}</pre>
              </div>
            </div>
          </a-space>
        </div>
        
        <a-divider />
        
        <div class="test-section">
          <h3>错误日志</h3>
          <div class="error-logs">
            <div v-if="errorLogs.length === 0" class="no-errors">
              暂无错误日志
            </div>
            <div v-else>
              <div v-for="(log, index) in errorLogs" :key="index" class="error-log">
                <div class="error-time">{{ log.time }}</div>
                <div class="error-message">{{ log.message }}</div>
                <div class="error-details">{{ log.details }}</div>
              </div>
            </div>
          </div>
        </div>
      </a-space>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import axios from 'axios'

const testing = ref(false)
const connectionStatus = reactive({
  color: 'gray',
  text: '未测试'
})

const errorLogs = ref<Array<{
  time: string
  message: string
  details: string
}>>([])

const apiTests = reactive([
  {
    name: '获取概览统计',
    url: '/api/v1/analysis/statistics/overview',
    loading: false,
    status: '',
    result: null
  },
  {
    name: '获取监控点列表',
    url: '/api/v1/monitor/list',
    loading: false,
    status: '',
    result: null
  },
  {
    name: '获取用户信息',
    url: '/api/v1/auth/profile',
    loading: false,
    status: '',
    result: null
  }
])

const addErrorLog = (message: string, details: string) => {
  errorLogs.value.unshift({
    time: new Date().toLocaleString(),
    message,
    details
  })
  
  // 只保留最近10条错误日志
  if (errorLogs.value.length > 10) {
    errorLogs.value = errorLogs.value.slice(0, 10)
  }
}

const testConnection = async () => {
  testing.value = true
  try {
    // 直接测试后端服务器
    const response = await axios.get('http://127.0.0.1:5501/api/v1/health', {
      timeout: 5000
    })
    
    connectionStatus.color = 'green'
    connectionStatus.text = '连接成功'
  } catch (error: any) {
    connectionStatus.color = 'red'
    connectionStatus.text = '连接失败'
    
    addErrorLog(
      '后端连接测试失败',
      `错误: ${error.message}\n状态码: ${error.response?.status || 'N/A'}\n详情: ${error.code || 'Unknown'}`
    )
  } finally {
    testing.value = false
  }
}

const testApi = async (api: any) => {
  api.loading = true
  api.status = ''
  api.result = null
  
  try {
    const response = await axios.get(api.url, {
      timeout: 5000,
      headers: {
        'Authorization': 'Bearer test-token'
      }
    })
    
    api.status = 'success'
    api.result = response.data
  } catch (error: any) {
    api.status = 'error'
    api.result = {
      error: error.message,
      status: error.response?.status,
      data: error.response?.data
    }
    
    addErrorLog(
      `API测试失败: ${api.name}`,
      `URL: ${api.url}\n错误: ${error.message}\n状态码: ${error.response?.status || 'N/A'}`
    )
  } finally {
    api.loading = false
  }
}
</script>

<style scoped>
.api-test-page {
  padding: 20px;
}

.test-section {
  margin: 16px 0;
}

.test-section h3 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
}

.api-test-item {
  margin-bottom: 16px;
  padding: 12px;
  border: 1px solid #e5e6eb;
  border-radius: 6px;
}

.api-url {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #86909c;
}

.api-result {
  margin-top: 8px;
  padding: 8px;
  background: #f7f8fa;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.error-logs {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e5e6eb;
  border-radius: 6px;
  padding: 12px;
}

.no-errors {
  text-align: center;
  color: #86909c;
  font-style: italic;
}

.error-log {
  margin-bottom: 12px;
  padding: 8px;
  background: #fff2f0;
  border-left: 3px solid #f5222d;
  border-radius: 4px;
}

.error-time {
  font-size: 12px;
  color: #86909c;
  margin-bottom: 4px;
}

.error-message {
  font-weight: 600;
  color: #f5222d;
  margin-bottom: 4px;
}

.error-details {
  font-family: 'Courier New', monospace;
  font-size: 11px;
  color: #4e5969;
  white-space: pre-wrap;
}
</style>
