# -*- coding: utf-8 -*-
# @Description : WebSocket事件处理
# @Date : 2025年6月20日

import json
from datetime import datetime
from flask import request
from flask_socketio import emit, join_room, leave_room, rooms
try:
    from ..services.realtime_service import RealtimeService
    from ..utils.database import get_db_connection
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    from services.realtime_service import RealtimeService
    from utils.database import get_db_connection

# 初始化实时服务
realtime_service = RealtimeService()

def register_websocket_events(socketio):
    """注册WebSocket事件处理器"""
    
    @socketio.on('connect')
    def handle_connect():
        """客户端连接"""
        print(f'WebSocket客户端连接: {request.sid}')
        emit('connected', {
            'message': '连接成功',
            'timestamp': datetime.now().isoformat(),
            'session_id': request.sid
        })
    
    @socketio.on('disconnect')
    def handle_disconnect():
        """客户端断开连接"""
        print(f'WebSocket客户端断开: {request.sid}')
    
    @socketio.on('join_monitor')
    def handle_join_monitor(data):
        """加入监控点房间"""
        try:
            monitor_id = data.get('monitor_id')
            if not monitor_id:
                emit('error', {'message': '监控点ID不能为空'})
                return
            
            room = f'monitor_{monitor_id}'
            join_room(room)
            
            emit('joined_monitor', {
                'monitor_id': monitor_id,
                'room': room,
                'message': f'已加入监控点 {monitor_id} 的实时数据推送',
                'timestamp': datetime.now().isoformat()
            })
            
            # 发送当前监控点状态
            try:
                current_data = realtime_service.get_monitor_realtime_data(monitor_id)
                emit('monitor_data', current_data, room=room)
            except Exception as e:
                emit('error', {'message': f'获取监控点数据失败: {str(e)}'})
                
        except Exception as e:
            emit('error', {'message': f'加入监控点失败: {str(e)}'})
    
    @socketio.on('leave_monitor')
    def handle_leave_monitor(data):
        """离开监控点房间"""
        try:
            monitor_id = data.get('monitor_id')
            if not monitor_id:
                emit('error', {'message': '监控点ID不能为空'})
                return
            
            room = f'monitor_{monitor_id}'
            leave_room(room)
            
            emit('left_monitor', {
                'monitor_id': monitor_id,
                'room': room,
                'message': f'已离开监控点 {monitor_id} 的实时数据推送',
                'timestamp': datetime.now().isoformat()
            })
        except Exception as e:
            emit('error', {'message': f'离开监控点失败: {str(e)}'})
    
    @socketio.on('join_alerts')
    def handle_join_alerts():
        """加入警报房间"""
        try:
            join_room('alerts')
            emit('joined_alerts', {
                'message': '已加入警报推送',
                'room': 'alerts',
                'timestamp': datetime.now().isoformat()
            })
        except Exception as e:
            emit('error', {'message': f'加入警报推送失败: {str(e)}'})
    
    @socketio.on('leave_alerts')
    def handle_leave_alerts():
        """离开警报房间"""
        try:
            leave_room('alerts')
            emit('left_alerts', {
                'message': '已离开警报推送',
                'timestamp': datetime.now().isoformat()
            })
        except Exception as e:
            emit('error', {'message': f'离开警报推送失败: {str(e)}'})
    
    @socketio.on('join_system')
    def handle_join_system():
        """加入系统状态房间"""
        try:
            join_room('system')
            emit('joined_system', {
                'message': '已加入系统状态推送',
                'room': 'system',
                'timestamp': datetime.now().isoformat()
            })
            
            # 发送当前系统状态
            try:
                system_status = realtime_service.get_system_status()
                emit('system_status', system_status, room='system')
            except Exception as e:
                emit('error', {'message': f'获取系统状态失败: {str(e)}'})
                
        except Exception as e:
            emit('error', {'message': f'加入系统状态推送失败: {str(e)}'})
    
    @socketio.on('leave_system')
    def handle_leave_system():
        """离开系统状态房间"""
        try:
            leave_room('system')
            emit('left_system', {
                'message': '已离开系统状态推送',
                'timestamp': datetime.now().isoformat()
            })
        except Exception as e:
            emit('error', {'message': f'离开系统状态推送失败: {str(e)}'})
    
    @socketio.on('get_monitor_list')
    def handle_get_monitor_list():
        """获取监控点列表"""
        try:
            with get_db_connection() as db:
                monitors = db.get_list("""
                    SELECT id, location, highway_section, connection_status, 
                           is_alarm, threshold, conf_threshold
                    FROM monitor 
                    ORDER BY location
                """)
            
            emit('monitor_list', {
                'monitors': monitors,
                'count': len(monitors),
                'timestamp': datetime.now().isoformat()
            })
        except Exception as e:
            emit('error', {'message': f'获取监控点列表失败: {str(e)}'})
    
    @socketio.on('get_active_monitors')
    def handle_get_active_monitors():
        """获取活跃监控点"""
        try:
            with get_db_connection() as db:
                monitors = db.get_list("""
                    SELECT id, location, highway_section, connection_status, 
                           is_alarm, threshold, conf_threshold
                    FROM monitor 
                    WHERE is_alarm = '开启' AND connection_status = 'connected'
                    ORDER BY location
                """)
            
            emit('active_monitors', {
                'monitors': monitors,
                'count': len(monitors),
                'timestamp': datetime.now().isoformat()
            })
        except Exception as e:
            emit('error', {'message': f'获取活跃监控点失败: {str(e)}'})
    
    @socketio.on('request_monitor_data')
    def handle_request_monitor_data(data):
        """请求监控点数据"""
        try:
            monitor_id = data.get('monitor_id')
            if not monitor_id:
                emit('error', {'message': '监控点ID不能为空'})
                return
            
            monitor_data = realtime_service.get_monitor_realtime_data(monitor_id)
            emit('monitor_data', monitor_data)
        except Exception as e:
            emit('error', {'message': f'获取监控点数据失败: {str(e)}'})
    
    @socketio.on('request_system_status')
    def handle_request_system_status():
        """请求系统状态"""
        try:
            system_status = realtime_service.get_system_status()
            emit('system_status', system_status)
        except Exception as e:
            emit('error', {'message': f'获取系统状态失败: {str(e)}'})
    
    @socketio.on('request_live_stats')
    def handle_request_live_stats():
        """请求实时统计"""
        try:
            live_stats = realtime_service.get_live_statistics()
            emit('live_statistics', live_stats)
        except Exception as e:
            emit('error', {'message': f'获取实时统计失败: {str(e)}'})
    
    @socketio.on('ping')
    def handle_ping():
        """心跳检测"""
        emit('pong', {
            'timestamp': datetime.now().isoformat(),
            'session_id': request.sid
        })
    
    @socketio.on('subscribe_all')
    def handle_subscribe_all():
        """订阅所有事件"""
        try:
            # 加入所有房间
            join_room('alerts')
            join_room('system')
            
            # 获取所有活跃监控点并加入房间
            with get_db_connection() as db:
                monitors = db.get_list("""
                    SELECT id FROM monitor 
                    WHERE is_alarm = '开启' AND connection_status = 'connected'
                """)
            
            for monitor in monitors:
                join_room(f'monitor_{monitor["id"]}')
            
            emit('subscribed_all', {
                'message': '已订阅所有事件',
                'monitor_count': len(monitors),
                'rooms': ['alerts', 'system'] + [f'monitor_{m["id"]}' for m in monitors],
                'timestamp': datetime.now().isoformat()
            })
        except Exception as e:
            emit('error', {'message': f'订阅所有事件失败: {str(e)}'})
    
    @socketio.on('unsubscribe_all')
    def handle_unsubscribe_all():
        """取消订阅所有事件"""
        try:
            # 获取当前用户的所有房间
            user_rooms = rooms()
            
            # 离开所有房间（除了默认房间）
            for room in user_rooms:
                if room != request.sid:  # 不离开默认的session房间
                    leave_room(room)
            
            emit('unsubscribed_all', {
                'message': '已取消订阅所有事件',
                'left_rooms': [room for room in user_rooms if room != request.sid],
                'timestamp': datetime.now().isoformat()
            })
        except Exception as e:
            emit('error', {'message': f'取消订阅失败: {str(e)}'})

# 实时数据推送函数（由后台任务调用）
def broadcast_monitor_data(socketio, monitor_id, data):
    """广播监控点数据"""
    try:
        room = f'monitor_{monitor_id}'
        socketio.emit('monitor_data', {
            'monitor_id': monitor_id,
            'data': data,
            'timestamp': datetime.now().isoformat()
        }, room=room)
    except Exception as e:
        print(f"广播监控点数据失败: {str(e)}")

def broadcast_alert(socketio, alert_data):
    """广播警报"""
    try:
        socketio.emit('new_alert', {
            'alert': alert_data,
            'timestamp': datetime.now().isoformat()
        }, room='alerts')
    except Exception as e:
        print(f"广播警报失败: {str(e)}")

def broadcast_system_status(socketio, status_data):
    """广播系统状态"""
    try:
        socketio.emit('system_status', {
            'status': status_data,
            'timestamp': datetime.now().isoformat()
        }, room='system')
    except Exception as e:
        print(f"广播系统状态失败: {str(e)}")

def broadcast_live_statistics(socketio, stats_data):
    """广播实时统计"""
    try:
        socketio.emit('live_statistics', {
            'statistics': stats_data,
            'timestamp': datetime.now().isoformat()
        }, broadcast=True)
    except Exception as e:
        print(f"广播实时统计失败: {str(e)}")

def broadcast_detection_result(socketio, monitor_id, detection_data):
    """广播检测结果"""
    try:
        room = f'monitor_{monitor_id}'
        socketio.emit('detection_result', {
            'monitor_id': monitor_id,
            'detection': detection_data,
            'timestamp': datetime.now().isoformat()
        }, room=room)
    except Exception as e:
        print(f"广播检测结果失败: {str(e)}")

def broadcast_monitor_status_change(socketio, monitor_id, status_change):
    """广播监控点状态变化"""
    try:
        room = f'monitor_{monitor_id}'
        socketio.emit('monitor_status_change', {
            'monitor_id': monitor_id,
            'status_change': status_change,
            'timestamp': datetime.now().isoformat()
        }, room=room)
        
        # 同时广播给系统状态订阅者
        socketio.emit('monitor_status_change', {
            'monitor_id': monitor_id,
            'status_change': status_change,
            'timestamp': datetime.now().isoformat()
        }, room='system')
    except Exception as e:
        print(f"广播监控点状态变化失败: {str(e)}")
