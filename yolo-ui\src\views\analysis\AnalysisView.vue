<template>
  <div class="analysis-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>数据分析</h2>
      <a-space>
        <a-range-picker
          v-model="dateRange"
          @change="onDateRangeChange"
          style="width: 300px;"
        />
        <a-button @click="refreshData">
          <template #icon><icon-refresh /></template>
          刷新数据
        </a-button>
        <a-button @click="showExportModal = true">
          <template #icon><icon-download /></template>
          导出数据
        </a-button>
      </a-space>
    </div>

    <!-- 数据分析选项卡 -->
    <a-tabs v-model:active-key="activeTab" type="card">
      <!-- 警报统计 -->
      <a-tab-pane key="alarms" title="警报统计">
        <a-row :gutter="24">
          <a-col :span="12">
            <a-card title="警报趋势" class="chart-card">
              <div ref="alarmTrendChart" class="chart-container"></div>
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card title="警报类型分布" class="chart-card">
              <div ref="alarmTypeChart" class="chart-container"></div>
            </a-card>
          </a-col>
        </a-row>

        <a-row :gutter="24" style="margin-top: 24px;">
          <a-col :span="12">
            <a-card title="严重程度分布" class="chart-card">
              <div ref="alarmSeverityChart" class="chart-container"></div>
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card title="监控点警报排行" class="chart-card">
              <div ref="alarmMonitorChart" class="chart-container"></div>
            </a-card>
          </a-col>
        </a-row>
      </a-tab-pane>

      <!-- 交通分析 -->
      <a-tab-pane key="traffic" title="交通分析">
        <div class="traffic-controls">
          <a-space>
            <a-select
              v-model="selectedMonitorId"
              placeholder="选择监控点"
              style="width: 200px;"
              @change="fetchTrafficData"
            >
              <a-option value="">全部监控点</a-option>
              <a-option
                v-for="monitor in monitors"
                :key="monitor.id"
                :value="monitor.id"
                :label="monitor.name"
              >
                {{ monitor.name }} - {{ monitor.location }}
              </a-option>
            </a-select>

            <a-radio-group
              v-model="trafficGranularity"
              @change="fetchTrafficData"
            >
              <a-radio value="hourly">小时统计</a-radio>
              <a-radio value="daily">日统计</a-radio>
            </a-radio-group>
          </a-space>
        </div>

        <a-row :gutter="24">
          <a-col :span="24">
            <a-card title="交通流量趋势" class="chart-card">
              <div ref="trafficFlowChart" class="chart-container-large"></div>
            </a-card>
          </a-col>
        </a-row>

        <a-row :gutter="24" style="margin-top: 24px;">
          <a-col :span="12">
            <a-card title="车辆类型分布" class="chart-card">
              <div ref="vehicleTypeChart" class="chart-container"></div>
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card title="平均速度分析" class="chart-card">
              <div ref="speedAnalysisChart" class="chart-container"></div>
            </a-card>
          </a-col>
        </a-row>
      </a-tab-pane>

      <!-- 热力图 -->
      <a-tab-pane key="heatmap" title="热力图">
        <div class="heatmap-controls">
          <a-space>
            <a-radio-group
              v-model="heatmapType"
              @change="fetchHeatmapData"
            >
              <a-radio value="accidents">事故热力图</a-radio>
              <a-radio value="traffic_flow">流量热力图</a-radio>
              <a-radio value="alerts">警报热力图</a-radio>
            </a-radio-group>
          </a-space>
        </div>

        <a-card title="区域热力分析" class="heatmap-card">
          <div ref="heatmapChart" class="heatmap-container"></div>
        </a-card>
      </a-tab-pane>

      <!-- 性能报告 -->
      <a-tab-pane key="performance" title="性能报告">
        <a-row :gutter="24">
          <a-col :span="8">
            <a-card title="系统性能指标" class="metrics-card">
              <a-space direction="vertical" fill>
                <a-statistic
                  title="平均响应时间"
                  :value="performanceMetrics?.avg_response_time || 0"
                  :precision="1"
                  suffix="ms"
                />
                <a-statistic
                  title="检测准确率"
                  :value="performanceMetrics?.detection_accuracy || 0"
                  :precision="2"
                  suffix="%"
                />
                <a-statistic
                  title="系统可用性"
                  :value="performanceMetrics?.system_uptime || 0"
                  :precision="2"
                  suffix="%"
                />
                <a-statistic
                  title="误报率"
                  :value="performanceMetrics?.false_positive_rate || 0"
                  :precision="2"
                  suffix="%"
                />
              </a-space>
            </a-card>
          </a-col>

          <a-col :span="16">
            <a-card title="性能趋势" class="chart-card">
              <div ref="performanceChart" class="chart-container-large"></div>
            </a-card>
          </a-col>
        </a-row>

        <a-row :gutter="24" style="margin-top: 24px;">
          <a-col :span="12">
            <a-card title="资源使用情况" class="chart-card">
              <div ref="resourceChart" class="chart-container"></div>
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card title="错误统计" class="chart-card">
              <div ref="errorChart" class="chart-container"></div>
            </a-card>
          </a-col>
        </a-row>
      </a-tab-pane>
    </a-tabs>

    <!-- 数据导出弹窗 -->
    <a-modal
      v-model:visible="showExportModal"
      title="数据导出"
      width="500px"
      @ok="handleExport"
      @cancel="showExportModal = false"
    >
      <a-form :model="exportForm" layout="vertical">
        <a-form-item label="导出类型">
          <a-checkbox-group v-model="exportForm.export_types">
            <a-checkbox value="alarms">警报数据</a-checkbox>
            <a-checkbox value="traffic">交通数据</a-checkbox>
            <a-checkbox value="performance">性能数据</a-checkbox>
          </a-checkbox-group>
        </a-form-item>

        <a-form-item label="导出格式">
          <a-radio-group v-model="exportForm.format">
            <a-radio value="csv">CSV</a-radio>
            <a-radio value="excel">Excel</a-radio>
            <a-radio value="pdf">PDF</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item label="时间范围">
          <a-range-picker
            v-model="exportForm.date_range"
            style="width: 100%;"
          />
        </a-form-item>

        <a-form-item label="监控点">
          <a-select
            v-model="exportForm.monitor_ids"
            placeholder="选择监控点（可多选）"
            multiple
            allow-clear
          >
            <a-option
              v-for="monitor in monitors"
              :key="monitor.id"
              :value="monitor.id"
              :label="monitor.name"
            >
              {{ monitor.name }} - {{ monitor.location }}
            </a-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { Message } from '@arco-design/web-vue'
import {
  IconRefresh,
  IconDownload
} from '@arco-design/web-vue/es/icon'
import * as echarts from 'echarts'
import { analysisApi, type AlarmStats, type TrafficFlow, type HeatmapData } from '@/api/analysis'
import { monitorApi, type Monitor } from '@/api/monitor'

// 响应式数据
const activeTab = ref('alarms')
const dateRange = ref<[string, string]>(['', ''])
const selectedMonitorId = ref<number | string>('')
const trafficGranularity = ref<'hourly' | 'daily'>('hourly')
const heatmapType = ref<'accidents' | 'traffic_flow' | 'alerts'>('accidents')
const showExportModal = ref(false)

const monitors = ref<Monitor[]>([])
const alarmStats = ref<AlarmStats | null>(null)
const trafficData = ref<TrafficFlow[]>([])
const heatmapData = ref<HeatmapData[]>([])
const performanceMetrics = ref<any>(null)

// 图表引用
const alarmTrendChart = ref<HTMLDivElement>()
const alarmTypeChart = ref<HTMLDivElement>()
const alarmSeverityChart = ref<HTMLDivElement>()
const alarmMonitorChart = ref<HTMLDivElement>()
const trafficFlowChart = ref<HTMLDivElement>()
const vehicleTypeChart = ref<HTMLDivElement>()
const speedAnalysisChart = ref<HTMLDivElement>()
const heatmapChart = ref<HTMLDivElement>()
const performanceChart = ref<HTMLDivElement>()
const resourceChart = ref<HTMLDivElement>()
const errorChart = ref<HTMLDivElement>()

// 图表实例
let alarmTrendChartInstance: echarts.ECharts | null = null
let alarmTypeChartInstance: echarts.ECharts | null = null
let alarmSeverityChartInstance: echarts.ECharts | null = null
let alarmMonitorChartInstance: echarts.ECharts | null = null
let trafficFlowChartInstance: echarts.ECharts | null = null
let vehicleTypeChartInstance: echarts.ECharts | null = null
let speedAnalysisChartInstance: echarts.ECharts | null = null
let heatmapChartInstance: echarts.ECharts | null = null
let performanceChartInstance: echarts.ECharts | null = null
let resourceChartInstance: echarts.ECharts | null = null
let errorChartInstance: echarts.ECharts | null = null

// 导出表单
const exportForm = reactive({
  export_types: ['alarms'],
  format: 'csv' as 'csv' | 'excel' | 'pdf',
  date_range: [] as [string, string],
  monitor_ids: [] as number[]
})

// 获取监控点列表
const fetchMonitors = async () => {
  try {
    const response = await monitorApi.getMonitorList({ page: 1, size: 100 })
    if (response.success) {
      monitors.value = response.data.monitors
    }
  } catch (error: any) {
    Message.error('获取监控点列表失败: ' + (error.message || '未知错误'))
  }
}

// 获取警报统计数据
const fetchAlarmStats = async () => {
  try {
    const params = {
      start_date: dateRange.value[0] || undefined,
      end_date: dateRange.value[1] || undefined
    }

    const response = await analysisApi.getAlarmStats(params)
    if (response.success) {
      alarmStats.value = response.data
      await nextTick()
      renderAlarmCharts()
    }
  } catch (error: any) {
    Message.error('获取警报统计失败: ' + (error.message || '未知错误'))
  }
}

// 获取交通数据
const fetchTrafficData = async () => {
  try {
    const params = {
      monitor_id: selectedMonitorId.value ? Number(selectedMonitorId.value) : undefined,
      start_date: dateRange.value[0] || undefined,
      end_date: dateRange.value[1] || undefined,
      granularity: trafficGranularity.value
    }

    const response = await analysisApi.getTrafficFlow(params)
    if (response.success) {
      trafficData.value = response.data
      await nextTick()
      renderTrafficCharts()
    }
  } catch (error: any) {
    Message.error('获取交通数据失败: ' + (error.message || '未知错误'))
  }
}

// 获取热力图数据
const fetchHeatmapData = async () => {
  try {
    const params = {
      data_type: heatmapType.value,
      start_date: dateRange.value[0] || undefined,
      end_date: dateRange.value[1] || undefined
    }

    const response = await analysisApi.getHeatmapData(params)
    if (response.success) {
      heatmapData.value = response.data
      await nextTick()
      renderHeatmapChart()
    }
  } catch (error: any) {
    Message.error('获取热力图数据失败: ' + (error.message || '未知错误'))
  }
}

// 获取性能数据
const fetchPerformanceData = async () => {
  try {
    const params = {
      start_date: dateRange.value[0] || undefined,
      end_date: dateRange.value[1] || undefined
    }

    const response = await analysisApi.getPerformanceReport(params)
    if (response.success) {
      performanceMetrics.value = response.data
      await nextTick()
      renderPerformanceCharts()
    }
  } catch (error: any) {
    Message.error('获取性能数据失败: ' + (error.message || '未知错误'))
  }
}

// 渲染警报图表
const renderAlarmCharts = () => {
  if (!alarmStats.value) return

  // 警报趋势图
  if (alarmTrendChart.value) {
    alarmTrendChartInstance = echarts.init(alarmTrendChart.value)
    const option = {
      title: { text: '警报趋势', left: 'center' },
      tooltip: { trigger: 'axis' },
      xAxis: {
        type: 'category',
        data: alarmStats.value.alarm_trend.map(item => item.date)
      },
      yAxis: { type: 'value' },
      series: [{
        data: alarmStats.value.alarm_trend.map(item => item.count),
        type: 'line',
        smooth: true,
        areaStyle: {}
      }]
    }
    alarmTrendChartInstance.setOption(option)
  }

  // 警报类型分布
  if (alarmTypeChart.value) {
    alarmTypeChartInstance = echarts.init(alarmTypeChart.value)
    const option = {
      title: { text: '警报类型分布', left: 'center' },
      tooltip: { trigger: 'item' },
      series: [{
        type: 'pie',
        radius: '50%',
        data: Object.entries(alarmStats.value.alarm_by_type).map(([name, value]) => ({
          name,
          value
        }))
      }]
    }
    alarmTypeChartInstance.setOption(option)
  }

  // 严重程度分布
  if (alarmSeverityChart.value) {
    alarmSeverityChartInstance = echarts.init(alarmSeverityChart.value)
    const option = {
      title: { text: '严重程度分布', left: 'center' },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [{
        name: '严重程度',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '30',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: Object.entries(alarmStats.value.alarm_by_severity).map(([name, value]) => ({
          name,
          value,
          itemStyle: {
            color: getSeverityColor(name)
          }
        }))
      }]
    }
    alarmSeverityChartInstance.setOption(option)
  }

  // 监控点警报排行
  if (alarmMonitorChart.value) {
    alarmMonitorChartInstance = echarts.init(alarmMonitorChart.value)
    const option = {
      title: { text: '监控点警报排行', left: 'center' },
      tooltip: { trigger: 'axis' },
      xAxis: { type: 'value' },
      yAxis: {
        type: 'category',
        data: alarmStats.value.alarm_by_monitor.map(item => item.monitor_name)
      },
      series: [{
        type: 'bar',
        data: alarmStats.value.alarm_by_monitor.map(item => item.count)
      }]
    }
    alarmMonitorChartInstance.setOption(option)
  }
}

// 渲染交通图表
const renderTrafficCharts = () => {
  if (!trafficData.value.length) return

  // 交通流量趋势
  if (trafficFlowChart.value) {
    trafficFlowChartInstance = echarts.init(trafficFlowChart.value)
    const firstMonitor = trafficData.value[0]
    const dataKey = trafficGranularity.value === 'hourly' ? 'hourly_flow' : 'daily_flow'
    const xAxisKey = trafficGranularity.value === 'hourly' ? 'hour' : 'date'

    const option = {
      title: { text: '交通流量趋势', left: 'center' },
      tooltip: { trigger: 'axis' },
      legend: { data: ['车辆数量', '平均速度'] },
      xAxis: {
        type: 'category',
        data: firstMonitor[dataKey].map((item: any) => item[xAxisKey])
      },
      yAxis: [
        { type: 'value', name: '车辆数量' },
        { type: 'value', name: '平均速度(km/h)' }
      ],
      series: [
        {
          name: '车辆数量',
          type: 'bar',
          data: firstMonitor[dataKey].map((item: any) => item.vehicle_count)
        },
        {
          name: '平均速度',
          type: 'line',
          yAxisIndex: 1,
          data: firstMonitor[dataKey].map((item: any) => item.avg_speed)
        }
      ]
    }
    trafficFlowChartInstance.setOption(option)
  }

  // 车辆类型分布
  if (vehicleTypeChart.value && trafficData.value[0]) {
    vehicleTypeChartInstance = echarts.init(vehicleTypeChart.value)
    const option = {
      title: { text: '车辆类型分布', left: 'center' },
      tooltip: { trigger: 'item' },
      series: [{
        type: 'pie',
        radius: '50%',
        data: Object.entries(trafficData.value[0].vehicle_types).map(([name, value]) => ({
          name,
          value
        }))
      }]
    }
    vehicleTypeChartInstance.setOption(option)
  }
}

// 渲染热力图
const renderHeatmapChart = () => {
  if (!heatmapData.value.length || !heatmapChart.value) return

  heatmapChartInstance = echarts.init(heatmapChart.value)

  // 转换数据格式
  const data = heatmapData.value.map(item => [
    item.location.longitude,
    item.location.latitude,
    item.intensity
  ])

  const option = {
    title: { text: `${getHeatmapTitle()}`, left: 'center' },
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        const item = heatmapData.value[params.dataIndex]
        return `${item.monitor_name}<br/>强度: ${item.intensity}`
      }
    },
    visualMap: {
      min: 0,
      max: Math.max(...heatmapData.value.map(item => item.intensity)),
      calculable: true,
      inRange: {
        color: ['#50a3ba', '#eac736', '#d94e5d']
      }
    },
    geo: {
      map: 'china',
      roam: true,
      itemStyle: {
        areaColor: '#323c48',
        borderColor: '#404a59'
      }
    },
    series: [{
      name: '热力值',
      type: 'heatmap',
      coordinateSystem: 'geo',
      data: data
    }]
  }

  heatmapChartInstance.setOption(option)
}

// 渲染性能图表
const renderPerformanceCharts = () => {
  // 这里可以添加性能图表的渲染逻辑
  // 由于篇幅限制，暂时省略具体实现
}

// 获取热力图标题
const getHeatmapTitle = () => {
  switch (heatmapType.value) {
    case 'accidents': return '事故热力图'
    case 'traffic_flow': return '流量热力图'
    case 'alerts': return '警报热力图'
    default: return '热力图'
  }
}

// 获取严重程度颜色
const getSeverityColor = (severity: string) => {
  switch (severity) {
    case '高危':
    case '高':
    case 'high':
      return '#ff4757'
    case '中危':
    case '中':
    case 'medium':
      return '#ffa502'
    case '低危':
    case '低':
    case 'low':
      return '#2ed573'
    default:
      return '#747d8c'
  }
}

// 时间范围变化
const onDateRangeChange = () => {
  refreshData()
}

// 刷新数据
const refreshData = () => {
  switch (activeTab.value) {
    case 'alarms':
      fetchAlarmStats()
      break
    case 'traffic':
      fetchTrafficData()
      break
    case 'heatmap':
      fetchHeatmapData()
      break
    case 'performance':
      fetchPerformanceData()
      break
  }
}

// 导出数据
const handleExport = async () => {
  try {
    const response = await analysisApi.exportData({
      export_type: exportForm.export_types.length === 1 ? exportForm.export_types[0] as any : 'all',
      format: exportForm.format,
      start_date: exportForm.date_range[0] || undefined,
      end_date: exportForm.date_range[1] || undefined,
      monitor_ids: exportForm.monitor_ids.length ? exportForm.monitor_ids : undefined
    })

    if (response.success) {
      // 下载文件
      const link = document.createElement('a')
      link.href = response.data.download_url
      link.download = `analysis_data.${exportForm.format}`
      link.click()

      Message.success('数据导出成功')
      showExportModal.value = false
    }
  } catch (error: any) {
    Message.error('数据导出失败: ' + (error.message || '未知错误'))
  }
}

// 生命周期
onMounted(async () => {
  await fetchMonitors()
  await fetchAlarmStats()
})
</script>

<style scoped>
.analysis-page {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.page-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.chart-card {
  margin-bottom: 16px;
}

.chart-container {
  width: 100%;
  height: 300px;
}

.chart-container-large {
  width: 100%;
  height: 400px;
}

.traffic-controls {
  margin-bottom: 16px;
  padding: 16px;
  background: #f7f8fa;
  border-radius: 8px;
}

.heatmap-controls {
  margin-bottom: 16px;
  padding: 16px;
  background: #f7f8fa;
  border-radius: 8px;
}

.heatmap-card {
  margin-bottom: 16px;
}

.heatmap-container {
  width: 100%;
  height: 500px;
}

.metrics-card {
  height: 400px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
</style>
