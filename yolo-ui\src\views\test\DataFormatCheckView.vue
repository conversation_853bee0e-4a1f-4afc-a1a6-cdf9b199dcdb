<template>
  <div class="data-format-check">
    <a-card title="数据格式检查工具">
      <a-alert type="info" show-icon style="margin-bottom: 16px;">
        <template #title>数据格式问题诊断</template>
        <div>
          <p>API已修复但前端仍显示空白，通常是数据格式不匹配导致的</p>
          <p>此工具将检查API返回的数据格式是否符合前端期望</p>
        </div>
      </a-alert>
      
      <a-tabs v-model:active-key="activeTab" type="card">
        <!-- 数据分析模块检查 -->
        <a-tab-pane key="analysis" title="数据分析模块">
          <div class="check-section">
            <h3>数据分析API数据格式检查</h3>
            
            <div class="api-checks">
              <div v-for="check in analysisChecks" :key="check.name" class="check-item">
                <div class="check-header">
                  <h4>{{ check.title }}</h4>
                  <a-button 
                    size="small" 
                    @click="checkApiFormat(check)"
                    :loading="check.loading"
                  >
                    检查格式
                  </a-button>
                </div>
                
                <div class="check-info">
                  <p><strong>API端点:</strong> <code>{{ check.endpoint }}</code></p>
                  <p><strong>前端期望格式:</strong></p>
                  <pre class="expected-format">{{ check.expectedFormat }}</pre>
                </div>
                
                <div class="check-result" v-if="check.result">
                  <h5>实际返回数据:</h5>
                  <pre class="actual-data">{{ JSON.stringify(check.result.data, null, 2) }}</pre>
                  
                  <h5>格式匹配分析:</h5>
                  <div class="format-analysis">
                    <div v-for="issue in check.result.issues" :key="issue.field" class="issue-item">
                      <a-tag :color="issue.type === 'error' ? 'red' : 'orange'">
                        {{ issue.type === 'error' ? '错误' : '警告' }}
                      </a-tag>
                      <span class="issue-field">{{ issue.field }}</span>
                      <span class="issue-message">{{ issue.message }}</span>
                    </div>
                    
                    <div v-if="check.result.issues.length === 0" class="no-issues">
                      <a-tag color="green">✅ 数据格式完全匹配</a-tag>
                    </div>
                  </div>
                  
                  <h5>修复建议:</h5>
                  <div class="fix-suggestions">
                    <div v-for="suggestion in check.result.suggestions" :key="suggestion" class="suggestion">
                      • {{ suggestion }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </a-tab-pane>

        <!-- 其他模块检查 -->
        <a-tab-pane key="other" title="其他模块">
          <div class="check-section">
            <h3>其他模块数据格式检查</h3>
            
            <div class="api-checks">
              <div v-for="check in otherChecks" :key="check.name" class="check-item">
                <div class="check-header">
                  <h4>{{ check.title }}</h4>
                  <a-button 
                    size="small" 
                    @click="checkApiFormat(check)"
                    :loading="check.loading"
                  >
                    检查格式
                  </a-button>
                </div>
                
                <div class="check-info">
                  <p><strong>API端点:</strong> <code>{{ check.endpoint }}</code></p>
                  <p><strong>前端期望格式:</strong></p>
                  <pre class="expected-format">{{ check.expectedFormat }}</pre>
                </div>
                
                <div class="check-result" v-if="check.result">
                  <h5>实际返回数据:</h5>
                  <pre class="actual-data">{{ JSON.stringify(check.result.data, null, 2) }}</pre>
                  
                  <h5>格式匹配分析:</h5>
                  <div class="format-analysis">
                    <div v-for="issue in check.result.issues" :key="issue.field" class="issue-item">
                      <a-tag :color="issue.type === 'error' ? 'red' : 'orange'">
                        {{ issue.type === 'error' ? '错误' : '警告' }}
                      </a-tag>
                      <span class="issue-field">{{ issue.field }}</span>
                      <span class="issue-message">{{ issue.message }}</span>
                    </div>
                    
                    <div v-if="check.result.issues.length === 0" class="no-issues">
                      <a-tag color="green">✅ 数据格式完全匹配</a-tag>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </a-tab-pane>

        <!-- 修复建议 -->
        <a-tab-pane key="fix" title="修复建议">
          <div class="fix-section">
            <h3>常见数据格式问题及修复方案</h3>
            
            <a-collapse>
              <a-collapse-item header="问题1：字段名不匹配" key="field-names">
                <div class="fix-content">
                  <p><strong>问题描述：</strong>后端返回的字段名与前端期望的不一致</p>
                  <p><strong>示例：</strong></p>
                  <pre class="code-example">
// 后端返回
{
  "alarm_types": {"collision": 5, "breakdown": 3}
}

// 前端期望
{
  "alarm_by_type": {"collision": 5, "breakdown": 3}
}
                  </pre>
                  <p><strong>解决方案：</strong>修改后端API返回字段名，或修改前端数据处理逻辑</p>
                </div>
              </a-collapse-item>
              
              <a-collapse-item header="问题2：数据结构不匹配" key="data-structure">
                <div class="fix-content">
                  <p><strong>问题描述：</strong>数据嵌套结构与前端期望不符</p>
                  <p><strong>示例：</strong></p>
                  <pre class="code-example">
// 后端返回
{
  "success": true,
  "data": {
    "hourly_flow": [{"hour": 8, "count": 120}]
  }
}

// 前端期望
{
  "success": true,
  "data": [
    {
      "hourly_flow": [{"hour": 8, "vehicle_count": 120}]
    }
  ]
}
                  </pre>
                  <p><strong>解决方案：</strong>调整API返回的数据结构</p>
                </div>
              </a-collapse-item>
              
              <a-collapse-item header="问题3：空数据处理" key="empty-data">
                <div class="fix-content">
                  <p><strong>问题描述：</strong>当没有数据时，返回null或undefined导致前端报错</p>
                  <p><strong>解决方案：</strong></p>
                  <pre class="code-example">
// 后端应该返回空数组而不是null
{
  "success": true,
  "data": {
    "alarm_types": {},  // 空对象
    "trend_data": [],   // 空数组
    "total_alarms": 0   // 默认值
  }
}
                  </pre>
                </div>
              </a-collapse-item>
            </a-collapse>
            
            <a-divider />
            
            <div class="global-actions">
              <a-space>
                <a-button @click="checkAllFormats" :loading="allChecking" type="primary">
                  检查所有格式
                </a-button>
                <a-button @click="generateFormatReport">
                  生成格式报告
                </a-button>
                <a-button @click="copyFixSuggestions">
                  复制修复建议
                </a-button>
              </a-space>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { Message } from '@arco-design/web-vue'
import axios from 'axios'

const activeTab = ref('analysis')
const allChecking = ref(false)

// 数据分析模块检查配置
const analysisChecks = reactive([
  {
    name: 'alarm_stats',
    title: '警报统计数据',
    endpoint: '/api/v1/analysis/alarms',
    expectedFormat: `{
  "success": true,
  "data": {
    "total_alarms": number,
    "alarm_by_type": Record<string, number>,
    "alarm_by_severity": Record<string, number>,
    "alarm_by_monitor": Array<{monitor_id, monitor_name, count}>,
    "alarm_trend": Array<{date, count}>
  }
}`,
    loading: false,
    result: null
  },
  {
    name: 'traffic_flow',
    title: '交通流量数据',
    endpoint: '/api/v1/analysis/traffic-flow',
    expectedFormat: `{
  "success": true,
  "data": Array<{
    "monitor_id": number,
    "monitor_name": string,
    "hourly_flow": Array<{hour, vehicle_count, avg_speed}>,
    "daily_flow": Array<{date, vehicle_count, avg_speed}>,
    "vehicle_types": Record<string, number>
  }>
}`,
    loading: false,
    result: null
  },
  {
    name: 'overview_stats',
    title: '概览统计数据',
    endpoint: '/api/v1/analysis/statistics/overview',
    expectedFormat: `{
  "success": true,
  "data": {
    "total_monitors": number,
    "online_monitors": number,
    "active_detections": number,
    "active_targets": number,
    "today_accidents": number,
    "active_alarms": number,
    "active_users": number
  }
}`,
    loading: false,
    result: null
  }
])

// 其他模块检查配置
const otherChecks = reactive([
  {
    name: 'system_users',
    title: '系统用户列表',
    endpoint: '/api/v1/system/users',
    expectedFormat: `{
  "success": true,
  "data": {
    "users": Array<{id, username, grade, email, status, last_login_time, create_time}>,
    "total": number,
    "page": number,
    "size": number
  }
}`,
    loading: false,
    result: null
  },
  {
    name: 'detection_tasks',
    title: '检测任务列表',
    endpoint: '/api/v1/detection/tasks',
    expectedFormat: `{
  "success": true,
  "data": {
    "tasks": Array<{task_id, monitor_id, status, create_time}>,
    "total": number
  }
}`,
    loading: false,
    result: null
  }
])

// 检查API数据格式
const checkApiFormat = async (check: any) => {
  check.loading = true

  try {
    const response = await axios.get(check.endpoint, {
      timeout: 5000,
      headers: {
        'Authorization': 'Bearer test-token'
      }
    })

    const data = response.data
    const issues = []
    const suggestions = []

    // 基础格式检查
    if (!data.success) {
      issues.push({
        field: 'success',
        type: 'error',
        message: 'success字段应该为true'
      })
    }

    if (!data.data) {
      issues.push({
        field: 'data',
        type: 'error',
        message: '缺少data字段'
      })
      suggestions.push('确保API返回包含data字段')
    }

    // 特定API格式检查
    if (check.name === 'alarm_stats' && data.data) {
      checkAlarmStatsFormat(data.data, issues, suggestions)
    } else if (check.name === 'traffic_flow' && data.data) {
      checkTrafficFlowFormat(data.data, issues, suggestions)
    } else if (check.name === 'overview_stats' && data.data) {
      checkOverviewStatsFormat(data.data, issues, suggestions)
    } else if (check.name === 'system_users' && data.data) {
      checkSystemUsersFormat(data.data, issues, suggestions)
    }

    check.result = {
      data: data,
      issues: issues,
      suggestions: suggestions
    }

    if (issues.length === 0) {
      Message.success(`${check.title} 数据格式检查通过`)
    } else {
      Message.warning(`${check.title} 发现 ${issues.length} 个格式问题`)
    }

  } catch (error: any) {
    check.result = {
      data: null,
      issues: [{
        field: 'request',
        type: 'error',
        message: `API请求失败: ${error.message}`
      }],
      suggestions: ['检查API端点是否正确实现', '确认后端服务正常运行']
    }
    Message.error(`${check.title} 检查失败: ${error.message}`)
  } finally {
    check.loading = false
  }
}

// 检查警报统计数据格式
const checkAlarmStatsFormat = (data: any, issues: any[], suggestions: any[]) => {
  const expectedFields = [
    'total_alarms',
    'today_alarms',
    'alarm_types',
    'severity_distribution',
    'trend_data'
  ]

  expectedFields.forEach(field => {
    if (!(field in data)) {
      issues.push({
        field: field,
        type: 'error',
        message: `缺少必需字段: ${field}`
      })
    }
  })

  // 检查数据类型
  if (data.alarm_types && typeof data.alarm_types !== 'object') {
    issues.push({
      field: 'alarm_types',
      type: 'error',
      message: 'alarm_types应该是对象类型'
    })
  }

  if (data.trend_data && !Array.isArray(data.trend_data)) {
    issues.push({
      field: 'trend_data',
      type: 'error',
      message: 'trend_data应该是数组类型'
    })
  }

  // 前端期望的字段名检查
  if (data.alarm_types && !data.alarm_by_type) {
    suggestions.push('前端期望字段名为alarm_by_type，当前为alarm_types')
  }
}

// 检查交通流量数据格式
const checkTrafficFlowFormat = (data: any, issues: any[], suggestions: any[]) => {
  const expectedFields = [
    'total_vehicles',
    'hourly_flow',
    'monitor_distribution'
  ]

  expectedFields.forEach(field => {
    if (!(field in data)) {
      issues.push({
        field: field,
        type: 'error',
        message: `缺少必需字段: ${field}`
      })
    }
  })

  if (data.hourly_flow && !Array.isArray(data.hourly_flow)) {
    issues.push({
      field: 'hourly_flow',
      type: 'error',
      message: 'hourly_flow应该是数组类型'
    })
  }

  // 检查数组元素格式
  if (data.hourly_flow && Array.isArray(data.hourly_flow) && data.hourly_flow.length > 0) {
    const firstItem = data.hourly_flow[0]
    if (!firstItem.hour || !firstItem.count) {
      suggestions.push('hourly_flow数组元素应包含hour和count字段')
    }
  }
}

// 检查概览统计数据格式
const checkOverviewStatsFormat = (data: any, issues: any[], suggestions: any[]) => {
  const expectedFields = [
    'total_monitors',
    'online_monitors',
    'active_detections',
    'active_targets',
    'today_accidents',
    'active_alarms',
    'active_users'
  ]

  expectedFields.forEach(field => {
    if (!(field in data)) {
      issues.push({
        field: field,
        type: 'error',
        message: `缺少必需字段: ${field}`
      })
    } else if (typeof data[field] !== 'number') {
      issues.push({
        field: field,
        type: 'warning',
        message: `${field}应该是数字类型`
      })
    }
  })
}

// 检查系统用户数据格式
const checkSystemUsersFormat = (data: any, issues: any[], suggestions: any[]) => {
  if (!data.users || !Array.isArray(data.users)) {
    issues.push({
      field: 'users',
      type: 'error',
      message: 'users字段应该是数组类型'
    })
  }

  if (typeof data.total !== 'number') {
    issues.push({
      field: 'total',
      type: 'error',
      message: 'total字段应该是数字类型'
    })
  }
}

// 检查所有格式
const checkAllFormats = async () => {
  allChecking.value = true

  const allChecks = [...analysisChecks, ...otherChecks]

  for (const check of allChecks) {
    await checkApiFormat(check)
    await new Promise(resolve => setTimeout(resolve, 300))
  }

  allChecking.value = false

  const totalIssues = allChecks.reduce((sum, check) => {
    return sum + (check.result?.issues?.length || 0)
  }, 0)

  if (totalIssues === 0) {
    Message.success('所有API数据格式检查通过！')
  } else {
    Message.warning(`发现 ${totalIssues} 个格式问题，请查看详细信息`)
  }
}

// 生成格式报告
const generateFormatReport = () => {
  const allChecks = [...analysisChecks, ...otherChecks]
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      total_apis: allChecks.length,
      checked_apis: allChecks.filter(c => c.result).length,
      total_issues: allChecks.reduce((sum, c) => sum + (c.result?.issues?.length || 0), 0)
    },
    details: allChecks.map(check => ({
      name: check.name,
      title: check.title,
      endpoint: check.endpoint,
      issues: check.result?.issues || [],
      suggestions: check.result?.suggestions || []
    }))
  }

  const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `data-format-report-${Date.now()}.json`
  link.click()
  URL.revokeObjectURL(url)

  Message.success('数据格式报告已生成')
}

// 复制修复建议
const copyFixSuggestions = () => {
  const allChecks = [...analysisChecks, ...otherChecks]
  const suggestions = allChecks.flatMap(check =>
    (check.result?.suggestions || []).map(s => `${check.title}: ${s}`)
  )

  const text = suggestions.join('\n')

  navigator.clipboard.writeText(text).then(() => {
    Message.success('修复建议已复制到剪贴板')
  }).catch(() => {
    Message.error('复制失败')
  })
}
</script>

<style scoped>
.data-format-check {
  padding: 20px;
}

.check-section {
  margin-bottom: 24px;
}

.check-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.api-checks {
  margin-top: 16px;
}

.check-item {
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid #e5e6eb;
  border-radius: 8px;
}

.check-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.check-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.check-info p {
  margin: 4px 0;
  font-size: 12px;
  color: #666;
}

.expected-format,
.actual-data {
  background: #f7f8fa;
  padding: 8px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 11px;
  margin: 8px 0;
  border: 1px solid #e5e6eb;
  overflow-x: auto;
}

.check-result {
  margin-top: 16px;
  border-top: 1px solid #e5e6eb;
  padding-top: 16px;
}

.check-result h5 {
  margin: 12px 0 8px 0;
  font-size: 12px;
  font-weight: 600;
}

.format-analysis {
  margin: 8px 0;
}

.issue-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 4px 0;
  font-size: 12px;
}

.issue-field {
  font-family: 'Courier New', monospace;
  font-weight: 600;
}

.issue-message {
  color: #666;
}

.no-issues {
  margin: 8px 0;
}

.fix-suggestions {
  margin: 8px 0;
}

.suggestion {
  margin: 4px 0;
  font-size: 12px;
  color: #666;
}

.fix-section {
  margin-bottom: 24px;
}

.fix-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.fix-content {
  padding: 16px;
}

.fix-content p {
  margin: 8px 0;
}

.code-example {
  background: #f7f8fa;
  padding: 12px;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  margin: 12px 0;
  border: 1px solid #e5e6eb;
  overflow-x: auto;
}

.global-actions {
  text-align: center;
  margin: 24px 0;
}
</style>
