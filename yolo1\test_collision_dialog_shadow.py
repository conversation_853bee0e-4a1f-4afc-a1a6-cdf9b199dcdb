#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试车辆碰撞检测对话框阴影效果
"""

import sys
from PySide6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget

try:
    from ui.dialog.collision_detection_dialog import CollisionDetectionDialog
except ImportError:
    print("❌ 无法导入碰撞检测对话框，请确保文件路径正确")
    sys.exit(1)

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("碰撞检测对话框阴影测试")
        self.setGeometry(100, 100, 400, 200)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建测试按钮
        self.test_button = QPushButton("显示碰撞检测对话框")
        self.test_button.setStyleSheet("padding: 15px; font-size: 16px;")
        self.test_button.clicked.connect(self.show_dialog)
        layout.addWidget(self.test_button)
    
    def show_dialog(self):
        """显示碰撞检测对话框"""
        try:
            dialog = CollisionDetectionDialog(self)
            dialog.show()
            print("✅ 碰撞检测对话框显示成功，请检查阴影效果是否为浅蓝色")
        except Exception as e:
            print(f"❌ 显示对话框失败: {e}")
            import traceback
            traceback.print_exc()

def main():
    app = QApplication(sys.argv)
    window = TestWindow()
    window.show()
    print("🚀 测试应用启动成功")
    print("请点击按钮测试碰撞检测对话框的阴影效果")
    sys.exit(app.exec())

if __name__ == "__main__":
    main()