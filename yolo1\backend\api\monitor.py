# -*- coding: utf-8 -*-
# @Description : 监控管理相关API接口
# @Date : 2025年6月20日

from datetime import datetime
from flask import request, jsonify
from . import api_v1
from .auth import token_required
from utils.response import success_response, error_response
from utils.database import get_db_connection
from utils.rtsp_utils import RTSPUtils

@api_v1.route('/monitor/list', methods=['GET'])
@token_required
def get_monitor_list(current_user_id):
    """获取监控点列表"""
    try:
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 10))
        location = request.args.get('location', '')
        highway_section = request.args.get('highway_section', '')
        
        offset = (page - 1) * page_size
        
        # 构建查询条件
        where_conditions = []
        params = []
        
        if location:
            where_conditions.append("location LIKE %s")
            params.append(f"%{location}%")
        
        if highway_section:
            where_conditions.append("highway_section LIKE %s")
            params.append(f"%{highway_section}%")
        
        where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""
        
        with get_db_connection() as db:
            # 获取总数
            count_sql = f"SELECT COUNT(*) as total FROM monitor{where_clause}"
            total = db.get_one(count_sql, params)['total']
            
            # 获取列表数据
            list_sql = f"""
                SELECT id, threshold, conf_threshold, iou_threshold, person, video, url, 
                       rtsp_format, connection_status, is_alarm, mode, show_labels, 
                       location, highway_section, camera_position, create_time, create_by, remark
                FROM monitor{where_clause} 
                ORDER BY create_time DESC 
                LIMIT %s OFFSET %s
            """
            params.extend([page_size, offset])
            monitors = db.get_list(list_sql, params)
        
        return success_response({
            'list': monitors,
            'total': total,
            'page': page,
            'page_size': page_size
        }, '获取监控点列表成功')
        
    except Exception as e:
        return error_response(f'获取监控点列表失败: {str(e)}')

@api_v1.route('/monitor/<int:monitor_id>', methods=['GET'])
@token_required
def get_monitor_detail(current_user_id, monitor_id):
    """获取监控点详情"""
    try:
        with get_db_connection() as db:
            monitor = db.get_one(
                """SELECT id, threshold, conf_threshold, iou_threshold, person, video, url, 
                          rtsp_format, connection_status, is_alarm, mode, show_labels, 
                          location, highway_section, camera_position, create_time, create_by, remark
                   FROM monitor WHERE id=%s""",
                (monitor_id,)
            )
        
        if not monitor:
            return error_response('监控点不存在')
        
        return success_response(monitor, '获取监控点详情成功')
        
    except Exception as e:
        return error_response(f'获取监控点详情失败: {str(e)}')

@api_v1.route('/monitor', methods=['POST'])
@token_required
def create_monitor(current_user_id):
    """创建监控点"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['threshold', 'person', 'video', 'url', 'location']
        for field in required_fields:
            if not data.get(field):
                return error_response(f'{field} 不能为空')
        
        # 获取当前用户信息
        with get_db_connection() as db:
            user = db.get_one("SELECT username FROM user WHERE id=%s", (current_user_id,))
            
            # 创建监控点
            monitor_id = db.create(
                """INSERT INTO monitor (threshold, conf_threshold, iou_threshold, person, video, url, 
                                      rtsp_format, connection_status, is_alarm, mode, show_labels, 
                                      location, highway_section, camera_position, create_time, create_by, remark)
                   VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)""",
                (
                    data['threshold'],
                    data.get('conf_threshold', 0.4),
                    data.get('iou_threshold', 0.5),
                    data['person'],
                    data['video'],
                    data['url'],
                    data.get('rtsp_format', 'standard'),
                    'unknown',
                    data.get('is_alarm', '开启'),
                    data.get('mode', '准确模式'),
                    data.get('show_labels', 1),
                    data['location'],
                    data.get('highway_section', ''),
                    data.get('camera_position', ''),
                    datetime.now(),
                    user['username'] if user else 'unknown',
                    data.get('remark', '')
                )
            )
        
        return success_response({'monitor_id': monitor_id}, '创建监控点成功')
        
    except Exception as e:
        return error_response(f'创建监控点失败: {str(e)}')

@api_v1.route('/monitor/<int:monitor_id>', methods=['PUT'])
@token_required
def update_monitor(current_user_id, monitor_id):
    """更新监控点"""
    try:
        data = request.get_json()
        
        # 检查监控点是否存在
        with get_db_connection() as db:
            monitor = db.get_one("SELECT id FROM monitor WHERE id=%s", (monitor_id,))
            if not monitor:
                return error_response('监控点不存在')
            
            # 构建更新字段
            update_fields = []
            params = []
            
            updatable_fields = [
                'threshold', 'conf_threshold', 'iou_threshold', 'person', 'video', 'url',
                'rtsp_format', 'is_alarm', 'mode', 'show_labels', 'location', 
                'highway_section', 'camera_position', 'remark'
            ]
            
            for field in updatable_fields:
                if field in data:
                    update_fields.append(f"{field}=%s")
                    params.append(data[field])
            
            if not update_fields:
                return error_response('没有需要更新的字段')
            
            params.append(monitor_id)
            
            # 执行更新
            db.modify(
                f"UPDATE monitor SET {', '.join(update_fields)} WHERE id=%s",
                params
            )
        
        return success_response(None, '更新监控点成功')
        
    except Exception as e:
        return error_response(f'更新监控点失败: {str(e)}')

@api_v1.route('/monitor/<int:monitor_id>', methods=['DELETE'])
@token_required
def delete_monitor(current_user_id, monitor_id):
    """删除监控点"""
    try:
        with get_db_connection() as db:
            # 检查监控点是否存在
            monitor = db.get_one("SELECT id FROM monitor WHERE id=%s", (monitor_id,))
            if not monitor:
                return error_response('监控点不存在')
            
            # 删除相关的警报记录
            db.modify("DELETE FROM alarm WHERE pid=%s", (monitor_id,))
            
            # 删除监控点
            db.modify("DELETE FROM monitor WHERE id=%s", (monitor_id,))
        
        return success_response(None, '删除监控点成功')
        
    except Exception as e:
        return error_response(f'删除监控点失败: {str(e)}')

@api_v1.route('/monitor/<int:monitor_id>/test-connection', methods=['POST'])
@token_required
def test_monitor_connection(current_user_id, monitor_id):
    """测试监控点连接"""
    try:
        with get_db_connection() as db:
            monitor = db.get_one("SELECT url FROM monitor WHERE id=%s", (monitor_id,))
            if not monitor:
                return error_response('监控点不存在')
            
            # 测试RTSP连接
            rtsp_utils = RTSPUtils()
            is_connected = rtsp_utils.test_connection(monitor['url'])
            
            # 更新连接状态
            status = 'connected' if is_connected else 'disconnected'
            db.modify(
                "UPDATE monitor SET connection_status=%s WHERE id=%s",
                (status, monitor_id)
            )
        
        return success_response({
            'connected': is_connected,
            'status': status
        }, '连接测试完成')
        
    except Exception as e:
        return error_response(f'连接测试失败: {str(e)}')

@api_v1.route('/monitor/<int:monitor_id>/start', methods=['POST'])
@token_required
def start_monitor(current_user_id, monitor_id):
    """启动监控"""
    try:
        with get_db_connection() as db:
            db.modify(
                "UPDATE monitor SET is_alarm=%s WHERE id=%s",
                ('开启', monitor_id)
            )
        
        return success_response(None, '监控启动成功')
        
    except Exception as e:
        return error_response(f'监控启动失败: {str(e)}')

@api_v1.route('/monitor/<int:monitor_id>/stop', methods=['POST'])
@token_required
def stop_monitor(current_user_id, monitor_id):
    """停止监控"""
    try:
        with get_db_connection() as db:
            db.modify(
                "UPDATE monitor SET is_alarm=%s WHERE id=%s",
                ('关闭', monitor_id)
            )
        
        return success_response(None, '监控停止成功')
        
    except Exception as e:
        return error_response(f'监控停止失败: {str(e)}')
