# -*- coding: utf-8 -*-
# @Description : API接口测试脚本
# @Date : 2025年6月20日

import requests
import json
import time

class APITester:
    def __init__(self, base_url="http://127.0.0.1:5500"):
        self.base_url = base_url
        self.token = None
        
    def test_request(self, endpoint, method='GET', data=None, headers=None):
        """测试API请求"""
        url = f"{self.base_url}{endpoint}"
        
        try:
            if method == 'GET':
                response = requests.get(url, headers=headers, timeout=10)
            elif method == 'POST':
                response = requests.post(url, json=data, headers=headers, timeout=10)
            elif method == 'PUT':
                response = requests.put(url, json=data, headers=headers, timeout=10)
            elif method == 'DELETE':
                response = requests.delete(url, headers=headers, timeout=10)
            
            print(f"\n{'='*80}")
            print(f"🔍 测试: {method} {endpoint}")
            print(f"📊 状态码: {response.status_code}")
            
            try:
                response_data = response.json()
                print(f"📝 响应数据:")
                print(json.dumps(response_data, ensure_ascii=False, indent=2))
                
                # 检查响应格式
                if 'code' in response_data and 'message' in response_data:
                    if response_data['code'] == 200:
                        print("✅ 接口调用成功")
                    else:
                        print(f"❌ 接口调用失败: {response_data['message']}")
                
                return response.status_code == 200, response_data
            except:
                print(f"📄 响应文本: {response.text}")
                return response.status_code == 200, response.text
                
        except Exception as e:
            print(f"\n{'='*80}")
            print(f"🔍 测试: {method} {endpoint}")
            print(f"❌ 请求异常: {e}")
            return False, None
    
    def test_login(self, username="admin", password="123456"):
        """测试登录接口"""
        print(f"\n🔐 测试登录: {username}")
        
        # 测试V1登录接口
        success, data = self.test_request(
            '/api/v1/auth/login', 
            'POST', 
            {'username': username, 'password': password}
        )
        
        if success and data and data.get('code') == 200:
            self.token = data['data']['token']
            print(f"🎫 获取Token: {self.token}")
            return True
        
        # 如果V1失败，尝试基础登录接口
        print("🔄 尝试基础登录接口...")
        success, data = self.test_request(
            '/api/login', 
            'POST', 
            {'username': username, 'password': password}
        )
        
        if success and data and data.get('code') == 200:
            self.token = data['data']['token']
            print(f"🎫 获取Token: {self.token}")
            return True
        
        return False
    
    def get_auth_headers(self):
        """获取认证头"""
        if self.token:
            return {'Authorization': f'Bearer {self.token}'}
        return {}
    
    def run_basic_tests(self):
        """运行基础测试"""
        print("🚀 开始基础API测试")
        print("="*80)
        
        tests = [
            # 基础接口
            ('/', 'GET', None, '首页接口'),
            ('/health', 'GET', None, '健康检查'),
            ('/api/docs', 'GET', None, 'API文档'),
            ('/api/test', 'GET', None, 'API测试'),
            
            # V1接口
            ('/api/v1/docs', 'GET', None, 'V1 API文档'),
            ('/api/v1/system/health-check', 'GET', None, 'V1 健康检查'),
        ]
        
        success_count = 0
        for endpoint, method, data, description in tests:
            print(f"\n📋 {description}")
            success, _ = self.test_request(endpoint, method, data)
            if success:
                success_count += 1
        
        print(f"\n📊 基础测试结果: {success_count}/{len(tests)} 成功")
        return success_count == len(tests)
    
    def run_auth_tests(self):
        """运行认证测试"""
        print("\n🔐 开始认证测试")
        print("="*80)
        
        # 测试错误登录
        print("🧪 测试错误登录...")
        self.test_request('/api/v1/auth/login', 'POST', {'username': 'wrong', 'password': 'wrong'})
        
        # 测试正确登录
        print("🧪 测试正确登录...")
        if self.test_login():
            print("✅ 登录成功")
            return True
        else:
            print("❌ 登录失败")
            return False
    
    def run_data_tests(self):
        """运行数据接口测试"""
        print("\n📊 开始数据接口测试")
        print("="*80)
        
        tests = [
            ('/api/v1/monitor/list', 'GET', None, '监控点列表'),
            ('/api/v1/monitor/list?page=1&size=5', 'GET', None, '监控点分页'),
            ('/api/v1/analysis/alarms', 'GET', None, '警报列表'),
            ('/api/v1/analysis/alarms?page=1&size=5', 'GET', None, '警报分页'),
            ('/api/monitors', 'GET', None, '基础监控点接口'),
        ]
        
        success_count = 0
        headers = self.get_auth_headers()
        
        for endpoint, method, data, description in tests:
            print(f"\n📋 {description}")
            success, _ = self.test_request(endpoint, method, data, headers)
            if success:
                success_count += 1
        
        print(f"\n📊 数据测试结果: {success_count}/{len(tests)} 成功")
        return success_count
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🎯 基于Yolov8与ByteTrack的高速公路智慧监控平台 - API接口测试")
        print("="*80)
        
        # 基础测试
        basic_ok = self.run_basic_tests()
        
        # 认证测试
        auth_ok = self.run_auth_tests()
        
        # 数据测试
        data_count = 0
        if auth_ok:
            data_count = self.run_data_tests()
        
        # 总结
        print(f"\n🎉 测试完成总结")
        print("="*80)
        print(f"✅ 基础接口: {'正常' if basic_ok else '异常'}")
        print(f"🔐 认证功能: {'正常' if auth_ok else '异常'}")
        print(f"📊 数据接口: {data_count}/5 正常")
        
        if basic_ok and auth_ok and data_count >= 3:
            print("\n🎊 系统API基本正常，可以对接前端！")
            print("\n📋 前端对接信息:")
            print(f"   🌐 API基础地址: {self.base_url}/api/v1")
            print(f"   🔑 登录接口: POST /auth/login")
            print(f"   📹 监控点接口: GET /monitor/list")
            print(f"   🚨 警报接口: GET /analysis/alarms")
            print(f"   💡 认证方式: Bearer Token")
        else:
            print("\n⚠️ 系统存在问题，需要修复后再对接前端")
            
        return basic_ok and auth_ok

def main():
    """主函数"""
    tester = APITester()
    
    print("等待服务器启动...")
    time.sleep(2)
    
    try:
        tester.run_all_tests()
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")

if __name__ == "__main__":
    main()
