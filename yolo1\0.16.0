Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple
Requirement already satisfied: supervision in c:\users\<USER>\appdata\roaming\python\python39\site-packages (0.15.0)
Collecting flask==2.3.3
  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/fd/56/26f0be8adc2b4257df20c1c4260ddd0aa396cf8e75d90ab2f7ff99bc34f9/flask-2.3.3-py3-none-any.whl (96 kB)
Collecting flask-cors==4.0.0
  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/10/69/1e6cfb87117568a9de088c32d6258219e9d1ff7c131abf74249ef2031279/Flask_Cors-4.0.0-py2.py3-none-any.whl (14 kB)
Collecting Werkzeug>=2.3.7 (from flask==2.3.3)
  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/52/24/ab44c871b0f07f491e5d2ad12c9bd7358e527510618cb1b803a88e986db1/werkzeug-3.1.3-py3-none-any.whl (224 kB)
Requirement already satisfied: Jinja2>=3.1.2 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from flask==2.3.3) (3.1.6)
Collecting itsdangerous>=2.1.2 (from flask==2.3.3)
  Using cached https://pypi.tuna.tsinghua.edu.cn/packages/04/96/92447566d16df59b2a776c0fb82dbc4d9e07cd95062562af01e408583fc4/itsdangerous-2.2.0-py3-none-any.whl (16 kB)
Collecting click>=8.1.3 (from flask==2.3.3)
  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/7e/d4/7ebdbd03970677812aac39c869717059dbb71a4cfc033ca6e5221787892c/click-8.1.8-py3-none-any.whl (98 kB)
Collecting blinker>=1.6.2 (from flask==2.3.3)
  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/10/cb/f2ad4230dc2eb1a74edf38f1a38b9b52277f75bef262d8908e60d957e13c/blinker-1.9.0-py3-none-any.whl (8.5 kB)
Collecting importlib-metadata>=3.6.0 (from flask==2.3.3)
  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/20/b0/36bd937216ec521246249be3bf9855081de4c5e06a0c9b4219dbeda50373/importlib_metadata-8.7.0-py3-none-any.whl (27 kB)
Requirement already satisfied: matplotlib<4.0.0,>=3.7.1 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from supervision) (3.7.2)
Requirement already satisfied: numpy<2.0.0,>=1.20.0 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from supervision) (1.24.3)
Requirement already satisfied: opencv-python-headless<*******,>=******** in c:\users\<USER>\appdata\roaming\python\python39\site-packages (from supervision) (*********)
Requirement already satisfied: pillow<11.0,>=9.4 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from supervision) (9.5.0)
Requirement already satisfied: pyyaml<7.0,>=6.0 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from supervision) (6.0.2)
Requirement already satisfied: scipy<2.0.0,>=1.9.0 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from supervision) (1.10.1)
Requirement already satisfied: contourpy>=1.0.1 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from matplotlib<4.0.0,>=3.7.1->supervision) (1.3.0)
Requirement already satisfied: cycler>=0.10 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from matplotlib<4.0.0,>=3.7.1->supervision) (0.12.1)
Requirement already satisfied: fonttools>=4.22.0 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from matplotlib<4.0.0,>=3.7.1->supervision) (4.58.4)
Requirement already satisfied: kiwisolver>=1.0.1 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from matplotlib<4.0.0,>=3.7.1->supervision) (1.4.7)
Requirement already satisfied: packaging>=20.0 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from matplotlib<4.0.0,>=3.7.1->supervision) (25.0)
Requirement already satisfied: pyparsing<3.1,>=2.3.1 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from matplotlib<4.0.0,>=3.7.1->supervision) (3.0.9)
Requirement already satisfied: python-dateutil>=2.7 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from matplotlib<4.0.0,>=3.7.1->supervision) (2.9.0.post0)
Requirement already satisfied: importlib-resources>=3.2.0 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from matplotlib<4.0.0,>=3.7.1->supervision) (6.5.2)
Requirement already satisfied: colorama in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from click>=8.1.3->flask==2.3.3) (0.4.6)
Requirement already satisfied: zipp>=3.20 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from importlib-metadata>=3.6.0->flask==2.3.3) (3.23.0)
Requirement already satisfied: MarkupSafe>=2.0 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from Jinja2>=3.1.2->flask==2.3.3) (3.0.2)
Requirement already satisfied: six>=1.5 in c:\users\<USER>\.conda\envs\bytetrack\lib\site-packages (from python-dateutil>=2.7->matplotlib<4.0.0,>=3.7.1->supervision) (1.17.0)
Installing collected packages: Werkzeug, itsdangerous, importlib-metadata, click, blinker, flask, flask-cors

Successfully installed Werkzeug-3.1.3 blinker-1.9.0 click-8.1.8 flask-2.3.3 flask-cors-4.0.0 importlib-metadata-8.7.0 itsdangerous-2.2.0
