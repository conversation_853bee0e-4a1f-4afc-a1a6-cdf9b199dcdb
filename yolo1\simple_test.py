# -*- coding: utf-8 -*-
# @Description : 简化的后端功能测试
# @Date : 2025年6月21日

import sys
import os

def test_imports():
    """测试导入"""
    print("🔍 测试Python模块导入...")
    
    # 基础模块
    try:
        import json
        import time
        import threading
        from datetime import datetime
        print("✅ 基础模块导入成功")
    except Exception as e:
        print(f"❌ 基础模块导入失败: {e}")
        return False
    
    # Flask相关
    try:
        from flask import Flask, request, jsonify
        from flask_cors import CORS
        print("✅ Flask模块导入成功")
    except Exception as e:
        print(f"❌ Flask模块导入失败: {e}")
        print("请安装: pip install flask flask-cors")
        return False
    
    # 科学计算
    try:
        import numpy as np
        print("✅ NumPy导入成功")
    except Exception as e:
        print(f"❌ NumPy导入失败: {e}")
        print("请安装: pip install numpy")
        return False
    
    # OpenCV
    try:
        import cv2
        print("✅ OpenCV导入成功")
    except Exception as e:
        print(f"❌ OpenCV导入失败: {e}")
        print("请安装: pip install opencv-python")
        return False
    
    # PyTorch
    try:
        import torch
        print(f"✅ PyTorch导入成功 (版本: {torch.__version__})")
        print(f"   CUDA可用: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"   GPU数量: {torch.cuda.device_count()}")
    except Exception as e:
        print(f"❌ PyTorch导入失败: {e}")
        print("请安装: pip install torch torchvision")
        return False
    
    # YOLO
    try:
        from ultralytics import YOLO
        print("✅ Ultralytics YOLO导入成功")
    except Exception as e:
        print(f"❌ YOLO导入失败: {e}")
        print("请安装: pip install ultralytics")
        return False
    
    # 网络请求
    try:
        import requests
        print("✅ Requests导入成功")
    except Exception as e:
        print(f"❌ Requests导入失败: {e}")
        print("请安装: pip install requests")
        return False
    
    return True

def test_file_structure():
    """测试文件结构"""
    print("\n📁 测试文件结构...")
    
    required_files = [
        'frontend_api_server.py',
        'core_detection_server.py',
        'advanced_detection_engine.py',
        'websocket_server.py',
        'config/end-back.env'
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} (缺失)")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n⚠️ 缺少文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 所有必要文件存在")
    return True

def test_basic_functionality():
    """测试基础功能"""
    print("\n🔧 测试基础功能...")
    
    try:
        # 测试YOLO模型加载
        from ultralytics import YOLO
        print("正在测试YOLO模型加载...")
        
        # 尝试加载模型（会自动下载）
        model = YOLO('yolov8n.pt')
        print("✅ YOLO模型加载成功")
        
        # 测试简单检测
        import numpy as np
        test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
        results = model(test_image, verbose=False)
        print("✅ YOLO检测功能正常")
        
    except Exception as e:
        print(f"❌ YOLO功能测试失败: {e}")
        return False
    
    try:
        # 测试Flask应用创建
        from flask import Flask
        app = Flask(__name__)
        
        @app.route('/test')
        def test():
            return {'status': 'ok'}
        
        print("✅ Flask应用创建成功")
        
    except Exception as e:
        print(f"❌ Flask功能测试失败: {e}")
        return False
    
    return True

def test_advanced_modules():
    """测试高级模块"""
    print("\n🚀 测试高级模块...")
    
    try:
        # 测试高级检测引擎
        from advanced_detection_engine import ViolationDetector, AccidentDetector, TrafficAnalyzer
        
        violation_detector = ViolationDetector()
        accident_detector = AccidentDetector()
        traffic_analyzer = TrafficAnalyzer()
        
        print("✅ 违规检测器创建成功")
        print("✅ 事故检测器创建成功")
        print("✅ 交通分析器创建成功")
        
    except Exception as e:
        print(f"❌ 高级模块测试失败: {e}")
        return False
    
    try:
        # 测试WebSocket服务器
        from websocket_server import WebSocketManager, RealTimeDataPusher
        
        ws_manager = WebSocketManager()
        pusher = RealTimeDataPusher(ws_manager)
        
        print("✅ WebSocket管理器创建成功")
        print("✅ 实时推送器创建成功")
        
    except Exception as e:
        print(f"❌ WebSocket模块测试失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("🎯 基于Yolov8与ByteTrack的高速公路智慧监控平台 - 简化功能测试")
    print("="*60)
    
    # 显示系统信息
    print(f"🐍 Python版本: {sys.version.split()[0]}")
    print(f"📁 工作目录: {os.getcwd()}")
    
    # 运行测试
    tests = [
        ("模块导入", test_imports),
        ("文件结构", test_file_structure),
        ("基础功能", test_basic_functionality),
        ("高级模块", test_advanced_modules)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n🎉 测试完成总结")
    print("="*60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:12} | {status}")
        if result:
            passed += 1
    
    print(f"\n📊 测试结果: {passed}/{len(tests)} 通过")
    
    if passed == len(tests):
        print("\n🎊 所有测试通过！系统准备就绪！")
        print("\n🚀 接下来可以:")
        print("   1. 启动完整系统: python start_complete_system.py")
        print("   2. 启动检测服务: python core_detection_server.py")
        print("   3. 启动基础API: python frontend_api_server.py")
        print("   4. 查看API文档: docs/完整后端API文档_v2.md")
        
        print("\n🔧 新增功能:")
        print("   ✅ YOLO目标检测")
        print("   ✅ 多目标追踪")
        print("   ✅ 违规检测 (违停、逆行)")
        print("   ✅ 事故检测 (碰撞、急停、拥堵)")
        print("   ✅ 交通分析统计")
        print("   ✅ WebSocket实时推送")
        
    else:
        print("\n⚠️ 部分测试失败，请根据上述提示安装缺失的依赖")
        print("\n📦 推荐安装命令:")
        print("   pip install flask flask-cors")
        print("   pip install numpy opencv-python")
        print("   pip install torch torchvision")
        print("   pip install ultralytics")
        print("   pip install requests websocket-client")
    
    return passed == len(tests)

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        sys.exit(1)
