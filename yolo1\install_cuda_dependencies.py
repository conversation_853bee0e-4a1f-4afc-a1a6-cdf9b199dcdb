# -*- coding: utf-8 -*-
# @Description : CUDA GPU依赖安装脚本
# @Date : 2025年6月20日

import subprocess
import sys
import os
import pkg_resources

def run_command(command, description):
    """运行命令并显示结果"""
    print(f"\n{description}...")
    print(f"执行命令: {command}")
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✓ {description}成功")
            if result.stdout:
                print(f"输出: {result.stdout.strip()}")
        else:
            print(f"✗ {description}失败")
            if result.stderr:
                print(f"错误: {result.stderr.strip()}")
            return False
    except Exception as e:
        print(f"✗ {description}异常: {e}")
        return False
    
    return True

def check_cuda_availability():
    """检查CUDA是否可用"""
    try:
        import torch
        cuda_available = torch.cuda.is_available()
        if cuda_available:
            print(f"✓ CUDA可用，设备数量: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                print(f"  设备 {i}: {torch.cuda.get_device_name(i)}")
        else:
            print("✗ CUDA不可用")
        return cuda_available
    except ImportError:
        print("PyTorch未安装，无法检查CUDA")
        return False

def get_installed_packages():
    """获取已安装的包列表"""
    installed_packages = {}
    try:
        for dist in pkg_resources.working_set:
            installed_packages[dist.project_name.lower()] = dist.version
    except Exception as e:
        print(f"获取已安装包列表失败: {e}")
    return installed_packages

def check_conflicts():
    """检查潜在的依赖冲突"""
    print("\n检查潜在的依赖冲突...")
    
    # 检查可能冲突的包
    conflict_packages = [
        ('torch', 'pytorch'),
        ('torchvision', 'torchvision'),
        ('opencv-python', 'opencv-contrib-python'),
        ('pillow', 'pil'),
        ('numpy', 'numpy')
    ]
    
    installed = get_installed_packages()
    conflicts = []
    
    for pkg1, pkg2 in conflict_packages:
        if pkg1 in installed and pkg2 in installed and pkg1 != pkg2:
            conflicts.append((pkg1, pkg2))
    
    # 检查OpenCV冲突
    opencv_packages = [pkg for pkg in installed.keys() if 'opencv' in pkg]
    if len(opencv_packages) > 1:
        print(f"⚠ 发现多个OpenCV包: {opencv_packages}")
        print("  建议只保留一个OpenCV包")
    
    return conflicts

def uninstall_conflicting_packages():
    """卸载可能冲突的包"""
    print("\n卸载可能冲突的PyTorch包...")
    
    torch_packages = [
        'torch', 'torchvision', 'torchaudio', 
        'pytorch', 'pytorch-cuda'
    ]
    
    for package in torch_packages:
        run_command(f"{sys.executable} -m pip uninstall {package} -y", f"卸载 {package}")

def install_cuda_pytorch():
    """安装CUDA版本的PyTorch"""
    print("\n安装CUDA版本的PyTorch...")
    
    # 本地wheel文件路径
    torch_wheel = r"C:\Users\<USER>\Downloads\torch-2.1.2+cu118-cp311-cp311-win_amd64.whl"
    torchvision_wheel = r"C:\Users\<USER>\Downloads\torchvision-0.16.1+cu118-cp311-cp311-win_amd64.whl"
    
    # 检查文件是否存在
    if not os.path.exists(torch_wheel):
        print(f"✗ PyTorch wheel文件不存在: {torch_wheel}")
        return False
    
    if not os.path.exists(torchvision_wheel):
        print(f"✗ TorchVision wheel文件不存在: {torchvision_wheel}")
        return False
    
    print(f"✓ 找到PyTorch wheel文件: {torch_wheel}")
    print(f"✓ 找到TorchVision wheel文件: {torchvision_wheel}")
    
    # 安装PyTorch
    if not run_command(f'{sys.executable} -m pip install "{torch_wheel}"', "安装PyTorch CUDA版本"):
        return False
    
    # 安装TorchVision
    if not run_command(f'{sys.executable} -m pip install "{torchvision_wheel}"', "安装TorchVision CUDA版本"):
        return False
    
    return True

def install_compatible_packages():
    """安装兼容的其他包"""
    print("\n安装兼容的其他包...")
    
    # 与PyTorch 2.1.2兼容的包版本
    compatible_packages = [
        "numpy==1.24.3",  # PyTorch 2.1.2兼容的numpy版本
        "pillow>=9.0.0,<11.0.0",  # 兼容的Pillow版本
        "opencv-python==********",  # 稳定的OpenCV版本
        "ultralytics>=8.0.0,<9.0.0",  # 兼容的ultralytics版本
        "supervision>=0.16.0",
        "matplotlib>=3.5.0",
        "scipy>=1.9.0"
    ]
    
    for package in compatible_packages:
        run_command(f"{sys.executable} -m pip install {package}", f"安装 {package}")

def install_web_framework():
    """安装Web框架相关包"""
    print("\n安装Web框架相关包...")
    
    web_packages = [
        "flask==2.3.3",
        "flask-cors==4.0.0",
        "pymysql==1.1.0",
        "python-dotenv==1.0.0",
        "pandas>=2.0.0",
        "psutil>=5.9.0"
    ]
    
    for package in web_packages:
        run_command(f"{sys.executable} -m pip install {package}", f"安装 {package}")

def test_cuda_installation():
    """测试CUDA安装"""
    print("\n测试CUDA安装...")
    
    test_script = '''
import torch
import torchvision
import numpy as np

print(f"PyTorch版本: {torch.__version__}")
print(f"TorchVision版本: {torchvision.__version__}")
print(f"CUDA可用: {torch.cuda.is_available()}")

if torch.cuda.is_available():
    print(f"CUDA版本: {torch.version.cuda}")
    print(f"GPU数量: {torch.cuda.device_count()}")
    for i in range(torch.cuda.device_count()):
        print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
    
    # 测试GPU计算
    device = torch.device("cuda:0")
    x = torch.randn(1000, 1000).to(device)
    y = torch.randn(1000, 1000).to(device)
    z = torch.mm(x, y)
    print(f"✓ GPU计算测试成功，结果形状: {z.shape}")
else:
    print("✗ CUDA不可用")

# 测试YOLO
try:
    from ultralytics import YOLO
    print("✓ Ultralytics YOLO导入成功")
    
    # 创建一个简单的模型测试
    model = YOLO("yolov8n.pt")
    print("✓ YOLO模型加载成功")
    
    if torch.cuda.is_available():
        model.to("cuda")
        print("✓ YOLO模型已移至GPU")
except Exception as e:
    print(f"✗ YOLO测试失败: {e}")
'''
    
    try:
        exec(test_script)
        return True
    except Exception as e:
        print(f"✗ CUDA测试失败: {e}")
        return False

def create_cuda_config():
    """创建CUDA配置文件"""
    print("\n创建CUDA配置...")
    
    config_content = '''# CUDA配置
# 启用CUDA加速
USE_CUDA=true
CUDA_DEVICE=0

# YOLO模型配置
YOLO_DEVICE=cuda
YOLO_HALF_PRECISION=true
YOLO_BATCH_SIZE=1

# 性能优化
TORCH_CUDNN_BENCHMARK=true
TORCH_BACKENDS_CUDNN_DETERMINISTIC=false
'''
    
    try:
        with open('config/cuda_config.env', 'w', encoding='utf-8') as f:
            f.write(config_content)
        print("✓ CUDA配置文件已创建: config/cuda_config.env")
    except Exception as e:
        print(f"✗ 创建CUDA配置文件失败: {e}")

def update_main_config():
    """更新主配置文件"""
    print("\n更新主配置文件...")
    
    config_file = 'config/end-back.env'
    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 添加CUDA配置
        cuda_config = '''
#---------------------------------------------------CUDA配置
USE_CUDA=true
CUDA_DEVICE=0
YOLO_DEVICE=cuda
YOLO_HALF_PRECISION=true
YOLO_BATCH_SIZE=1
'''
        
        if 'USE_CUDA' not in content:
            with open(config_file, 'a', encoding='utf-8') as f:
                f.write(cuda_config)
            print("✓ 主配置文件已更新")
        else:
            print("✓ 主配置文件已包含CUDA配置")

def main():
    """主函数"""
    print("=" * 80)
    print("基于Yolov8与ByteTrack的高速公路智慧监控平台 - CUDA GPU依赖安装")
    print("=" * 80)
    
    # 检查Python版本
    print(f"Python版本: {sys.version}")
    
    # 检查冲突
    conflicts = check_conflicts()
    if conflicts:
        print(f"发现依赖冲突: {conflicts}")
        response = input("是否继续安装？(y/n): ")
        if response.lower() != 'y':
            return
    
    # 卸载冲突包
    uninstall_conflicting_packages()
    
    # 安装CUDA PyTorch
    if not install_cuda_pytorch():
        print("PyTorch CUDA安装失败，退出")
        return
    
    # 安装兼容包
    install_compatible_packages()
    
    # 安装Web框架
    install_web_framework()
    
    # 测试安装
    if test_cuda_installation():
        print("\n" + "=" * 80)
        print("✓ CUDA GPU依赖安装成功！")
        print("=" * 80)
        
        # 创建配置
        create_cuda_config()
        update_main_config()
        
        print("\n下一步操作:")
        print("1. 重启Python环境")
        print("2. 运行: python test_db.py")
        print("3. 启动系统: python start_server.py")
        print("4. 系统将自动使用GPU加速")
    else:
        print("\n" + "=" * 80)
        print("✗ CUDA安装验证失败")
        print("=" * 80)
        print("请检查:")
        print("1. NVIDIA驱动是否正确安装")
        print("2. CUDA Toolkit是否安装")
        print("3. wheel文件路径是否正确")

if __name__ == "__main__":
    main()
