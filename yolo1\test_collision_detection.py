# -*- coding: utf-8 -*-
# @Description : 碰撞检测功能测试
# @Date : 2025年6月21日

import sys
import numpy as np
from datetime import datetime
from PySide6.QtWidgets import QApplication
from ui.dialog.collision_detection_dialog import CollisionDetectionDialog, CollisionDetectionAlgorithm

def test_collision_algorithm():
    """测试碰撞检测算法"""
    print("🚗 测试碰撞检测算法")
    print("="*50)
    
    # 创建算法实例
    algorithm = CollisionDetectionAlgorithm()
    
    # 模拟两个接近的车辆
    tracked_objects = [
        {
            'track_id': 1,
            'bbox': [100, 100, 150, 150],  # x1, y1, x2, y2
            'confidence': 0.9
        },
        {
            'track_id': 2,
            'bbox': [140, 140, 190, 190],  # 接近的车辆
            'confidence': 0.85
        }
    ]
    
    # 执行碰撞检测
    collisions = algorithm.detect_collision(tracked_objects, datetime.now())
    
    if collisions:
        print(f"✅ 检测到 {len(collisions)} 个碰撞事件:")
        for collision in collisions:
            print(f"   车辆 {collision['id1']} 与 {collision['id2']} 碰撞")
            print(f"   距离: {collision['distance']:.1f} 像素")
            print(f"   严重程度: {collision['severity']}")
    else:
        print("❌ 未检测到碰撞")
    
    # 测试距离较远的车辆
    print("\n测试距离较远的车辆:")
    distant_objects = [
        {
            'track_id': 3,
            'bbox': [100, 100, 150, 150],
            'confidence': 0.9
        },
        {
            'track_id': 4,
            'bbox': [300, 300, 350, 350],  # 距离较远
            'confidence': 0.85
        }
    ]
    
    distant_collisions = algorithm.detect_collision(distant_objects, datetime.now())
    
    if distant_collisions:
        print(f"检测到 {len(distant_collisions)} 个碰撞事件")
    else:
        print("✅ 正确：距离较远的车辆未检测到碰撞")

def test_collision_dialog():
    """测试碰撞检测对话框"""
    print("\n🖥️ 测试碰撞检测对话框")
    print("="*50)
    
    app = QApplication(sys.argv)
    
    # 创建对话框
    dialog = CollisionDetectionDialog()
    
    # 连接信号
    def on_detection_started():
        print("✅ 检测开始信号触发")
    
    def on_detection_stopped():
        print("✅ 检测停止信号触发")
    
    def on_collision_detected(collision_info):
        print(f"✅ 碰撞检测信号触发: {collision_info}")
    
    dialog.detection_started.connect(on_detection_started)
    dialog.detection_stopped.connect(on_detection_stopped)
    dialog.collision_detected.connect(on_collision_detected)
    
    # 显示对话框
    dialog.show()
    
    # 模拟一些数据
    print("模拟碰撞数据...")
    test_objects = [
        {
            'track_id': 1,
            'bbox': [100, 100, 150, 150],
            'confidence': 0.9
        },
        {
            'track_id': 2,
            'bbox': [130, 130, 180, 180],
            'confidence': 0.85
        }
    ]
    
    # 处理模拟数据
    dialog.process_frame_data(test_objects)
    
    print("对话框已显示，请手动测试界面功能")
    print("点击'开始检测'按钮测试功能")
    
    # 运行应用
    sys.exit(app.exec())

def main():
    """主函数"""
    print("🎯 车辆碰撞检测功能测试")
    print("="*60)
    
    # 测试算法
    test_collision_algorithm()
    
    # 询问是否测试GUI
    choice = input("\n是否测试GUI对话框? (y/n): ").lower().strip()
    if choice == 'y' or choice == 'yes':
        test_collision_dialog()
    else:
        print("测试完成！")

if __name__ == "__main__":
    main()
