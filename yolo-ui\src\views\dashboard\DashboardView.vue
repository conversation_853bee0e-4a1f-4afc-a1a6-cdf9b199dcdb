<template>
  <div class="dashboard">
    <!-- 系统概览统计 -->
    <a-card title="系统概览" class="overview-card" :loading="loading">
      <a-row :gutter="24">
        <a-col :span="6">
          <a-statistic
            title="总监控点"
            :value="stats?.total_monitors || 0"
            suffix="个"
            :value-style="{ color: '#165DFF' }"
          />
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="在线监控点"
            :value="stats?.online_monitors || 0"
            suffix="个"
            :value-style="{ color: '#00B42A' }"
          />
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="活跃检测"
            :value="stats?.active_detections || 0"
            suffix="个"
            :value-style="{ color: '#FF7D00' }"
          />
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="今日事故"
            :value="stats?.today_accidents || 0"
            suffix="起"
            :value-style="{ color: '#F53F3F' }"
          />
        </a-col>
      </a-row>

      <a-divider />

      <a-row :gutter="24">
        <a-col :span="8">
          <a-statistic
            title="活跃目标"
            :value="stats?.active_targets || 0"
            suffix="个"
          />
        </a-col>
        <a-col :span="8">
          <a-statistic
            title="活跃警报"
            :value="stats?.active_alarms || 0"
            suffix="条"
          />
        </a-col>
        <a-col :span="8">
          <a-statistic
            title="在线用户"
            :value="stats?.active_users || 0"
            suffix="人"
          />
        </a-col>
      </a-row>
    </a-card>

    <!-- 监控点状态和最新警报 -->
    <a-row :gutter="24">
      <a-col :span="14">
        <a-card title="监控点状态" class="monitor-card">
          <a-spin :loading="monitorLoading">
            <div class="monitor-grid">
              <div
                v-for="monitor in monitors"
                :key="monitor.id"
                class="monitor-item"
                :class="{ 'online': monitor.connection_status === 'online' }"
              >
                <div class="monitor-header">
                  <h4>{{ monitor.name }}</h4>
                  <a-tag
                    :color="monitor.connection_status === 'online' ? 'green' : 'red'"
                  >
                    {{ monitor.connection_status === 'online' ? '在线' : '离线' }}
                  </a-tag>
                </div>
                <p class="monitor-location">{{ monitor.location }}</p>
                <p class="monitor-section">{{ monitor.highway_section }}</p>
              </div>
            </div>
          </a-spin>
        </a-card>
      </a-col>

      <a-col :span="10">
        <a-card title="最新警报" class="alert-card">
          <div class="alert-list">
            <a-empty v-if="!alerts.length" description="暂无警报" />
            <div v-else>
              <div
                v-for="alert in alerts"
                :key="alert.id"
                class="alert-item"
              >
                <div class="alert-content">
                  <h5>{{ alert.title }}</h5>
                  <p>{{ alert.description }}</p>
                  <span class="alert-time">{{ alert.time }}</span>
                </div>
                <a-tag
                  :color="getAlertColor(alert.level)"
                  class="alert-level"
                >
                  {{ alert.level }}
                </a-tag>
              </div>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import { analysisApi, type OverviewStats } from '@/api/analysis'
import { monitorApi, type Monitor } from '@/api/monitor'

// 响应式数据
const loading = ref(false)
const monitorLoading = ref(false)
const stats = ref<OverviewStats | null>(null)
const monitors = ref<Monitor[]>([])

// 模拟警报数据
const alerts = ref([
  {
    id: 1,
    title: '车辆异常检测',
    description: '杭州收费站监控点检测到异常车辆行为',
    time: '2024-12-24 15:30:25',
    level: '高'
  },
  {
    id: 2,
    title: '连接异常',
    description: '富阳互通监控点连接不稳定',
    time: '2024-12-24 14:15:10',
    level: '中'
  },
  {
    id: 3,
    title: '系统维护',
    description: '系统将于今晚22:00进行例行维护',
    time: '2024-12-24 13:00:00',
    level: '低'
  }
])

// 获取概览统计数据
const fetchOverviewStats = async () => {
  loading.value = true
  try {
    const response = await analysisApi.getOverviewStats()
    if (response.success) {
      stats.value = response.data
    }
  } catch (error: any) {
    Message.error('获取概览数据失败: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

// 获取监控点列表
const fetchMonitors = async () => {
  monitorLoading.value = true
  try {
    const response = await monitorApi.getMonitorList({ page: 1, size: 10 })
    if (response.success) {
      monitors.value = response.data.monitors
    }
  } catch (error: any) {
    Message.error('获取监控点数据失败: ' + (error.message || '未知错误'))
  } finally {
    monitorLoading.value = false
  }
}

// 获取警报级别颜色
const getAlertColor = (level: string) => {
  switch (level) {
    case '高': return 'red'
    case '中': return 'orange'
    case '低': return 'blue'
    default: return 'gray'
  }
}

// 定时刷新数据
let refreshTimer: NodeJS.Timeout | null = null

const startAutoRefresh = () => {
  refreshTimer = setInterval(() => {
    fetchOverviewStats()
    fetchMonitors()
  }, 30000) // 30秒刷新一次
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 生命周期
onMounted(() => {
  fetchOverviewStats()
  fetchMonitors()
  startAutoRefresh()
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.dashboard {
  padding: 0;
}

.overview-card {
  margin-bottom: 24px;
}

.monitor-card {
  height: auto !important;
}

.monitor-card :deep(.arco-card-body) {
  overflow: visible !important;
  height: auto !important;
}

.alert-card {
  height: 400px;
}

.monitor-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 16px;
}

.monitor-item {
  padding: 16px;
  border: 1px solid #e5e6eb;
  border-radius: 8px;
  background: #f7f8fa;
  transition: all 0.3s ease;
}

.monitor-item.online {
  border-color: #00b42a;
  background: #f6ffed;
}

.monitor-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.monitor-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.monitor-location {
  margin: 4px 0;
  font-size: 12px;
  color: #86909c;
}

.monitor-section {
  margin: 0;
  font-size: 12px;
  color: #4e5969;
}

.alert-list {
  max-height: 320px;
  overflow-y: auto;
}

.alert-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid #e5e6eb;
}

.alert-item:last-child {
  border-bottom: none;
}

.alert-content {
  flex: 1;
}

.alert-content h5 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
}

.alert-content p {
  margin: 0 0 4px 0;
  font-size: 12px;
  color: #86909c;
  line-height: 1.4;
}

.alert-time {
  font-size: 11px;
  color: #c9cdd4;
}

.alert-level {
  margin-left: 12px;
}
</style>
