#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试兼容性函数的性能问题
"""

import torch
import cv2
import numpy as np
from ultralytics import YOLO
import supervision as sv
import time

def debug_create_detections_from_result(result):
    """调试版本的兼容性函数，添加详细日志"""
    print("\n--- 兼容性函数调试开始 ---")
    
    # 第一次尝试：from_ultralytics
    print("尝试 sv.Detections.from_ultralytics(result)...")
    start_time = time.time()
    try:
        detections = sv.Detections.from_ultralytics(result)
        end_time = time.time()
        print(f"from_ultralytics 成功，耗时: {end_time - start_time:.4f}秒")
        return detections
    except AttributeError as e:
        end_time = time.time()
        print(f"from_ultralytics 失败，耗时: {end_time - start_time:.4f}秒，错误: {e}")
    
    # 第二次尝试：from_yolov8
    print("尝试 sv.Detections.from_yolov8(result)...")
    start_time = time.time()
    try:
        detections = sv.Detections.from_yolov8(result)
        end_time = time.time()
        print(f"from_yolov8 成功，耗时: {end_time - start_time:.4f}秒")
        return detections
    except AttributeError as e:
        end_time = time.time()
        print(f"from_yolov8 失败，耗时: {end_time - start_time:.4f}秒，错误: {e}")
    
    # 第三次尝试：手动创建
    print("尝试手动创建 Detections...")
    start_time = time.time()
    
    if result.boxes is not None:
        print("result.boxes 不为空，开始提取数据...")
        
        # 检查数据在哪个设备上
        print(f"result.boxes.xyxy.device: {result.boxes.xyxy.device}")
        print(f"result.boxes.conf.device: {result.boxes.conf.device}")
        print(f"result.boxes.cls.device: {result.boxes.cls.device}")
        
        boxes_start = time.time()
        boxes = result.boxes.xyxy.cpu().numpy()
        boxes_end = time.time()
        print(f"提取 boxes 耗时: {boxes_end - boxes_start:.4f}秒")
        
        conf_start = time.time()
        confidences = result.boxes.conf.cpu().numpy()
        conf_end = time.time()
        print(f"提取 confidences 耗时: {conf_end - conf_start:.4f}秒")
        
        cls_start = time.time()
        class_ids = result.boxes.cls.cpu().numpy().astype(int)
        cls_end = time.time()
        print(f"提取 class_ids 耗时: {cls_end - cls_start:.4f}秒")
        
        id_start = time.time()
        track_ids = result.boxes.id.cpu().numpy().astype(int) if result.boxes.id is not None else None
        id_end = time.time()
        print(f"提取 track_ids 耗时: {id_end - id_start:.4f}秒")
        
        create_start = time.time()
        detections = sv.Detections(
            xyxy=boxes,
            confidence=confidences,
            class_id=class_ids,
            tracker_id=track_ids
        )
        create_end = time.time()
        print(f"创建 Detections 对象耗时: {create_end - create_start:.4f}秒")
        
        end_time = time.time()
        print(f"手动创建总耗时: {end_time - start_time:.4f}秒")
        return detections
    else:
        print("result.boxes 为空，返回空检测结果")
        detections = sv.Detections.empty()
        end_time = time.time()
        print(f"创建空检测结果耗时: {end_time - start_time:.4f}秒")
        return detections

def test_with_real_image():
    print("=== 使用真实图像测试 ===")
    
    # 创建一个有内容的测试图像
    test_image = np.zeros((640, 640, 3), dtype=np.uint8)
    # 画一些简单的形状
    cv2.rectangle(test_image, (100, 100), (200, 200), (255, 255, 255), -1)
    cv2.circle(test_image, (400, 400), 50, (128, 128, 128), -1)
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"使用设备: {device}")
    
    model = YOLO('yolov8n.pt')
    
    print("\n开始检测...")
    total_start = time.time()
    results = model.track(test_image, device=device, verbose=False)
    result = results[0]
    total_end = time.time()
    print(f"YOLO检测总耗时: {total_end - total_start:.4f}秒")
    
    print(f"\nresult.boxes: {result.boxes}")
    if result.boxes is not None:
        print(f"检测到的框数量: {len(result.boxes)}")
        print(f"boxes设备: {result.boxes.xyxy.device}")
    
    # 测试兼容性函数
    print("\n=== 测试调试版兼容性函数 ===")
    compat_start = time.time()
    detections = debug_create_detections_from_result(result)
    compat_end = time.time()
    print(f"兼容性函数总耗时: {compat_end - compat_start:.4f}秒")
    
    # 测试直接调用
    print("\n=== 测试直接调用 supervision ===")
    direct_start = time.time()
    try:
        detections_direct = sv.Detections.from_yolov8(result)
        direct_end = time.time()
        print(f"直接调用成功，耗时: {direct_end - direct_start:.4f}秒")
    except Exception as e:
        direct_end = time.time()
        print(f"直接调用失败，耗时: {direct_end - direct_start:.4f}秒，错误: {e}")

if __name__ == "__main__":
    test_with_real_image()