# -*- coding: utf-8 -*-
# 数据库完整导入和验证脚本

import os
import sys
import pymysql
import json
from datetime import datetime

def import_database():
    """导入完整数据库结构和数据"""
    print("=" * 80)
    print("🚀 基于Yolov8与ByteTrack的高速公路智慧监控平台 - 数据库完整导入")
    print("=" * 80)
    
    # 数据库配置
    config = {
        'host': '127.0.0.1',
        'port': 3306,
        'user': 'root',
        'password': '123456',
        'charset': 'utf8mb4'
    }
    
    try:
        # 连接MySQL服务器（不指定数据库）
        print("📡 连接MySQL服务器...")
        connection = pymysql.connect(**config)
        cursor = connection.cursor()
        
        print("✅ MySQL连接成功")
        
        # 读取SQL文件
        sql_file = '完整数据库结构.sql'
        if not os.path.exists(sql_file):
            print(f"❌ SQL文件不存在: {sql_file}")
            return False
        
        print(f"📖 读取SQL文件: {sql_file}")
        with open(sql_file, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # 分割SQL语句
        sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
        
        print(f"📝 共找到 {len(sql_statements)} 条SQL语句")
        
        # 执行SQL语句
        success_count = 0
        error_count = 0
        
        for i, statement in enumerate(sql_statements, 1):
            try:
                if statement.upper().startswith(('CREATE', 'DROP', 'INSERT', 'USE')):
                    cursor.execute(statement)
                    success_count += 1
                    if i % 10 == 0:
                        print(f"⏳ 已执行 {i}/{len(sql_statements)} 条语句...")
            except Exception as e:
                error_count += 1
                print(f"⚠️ 语句执行失败 ({i}): {str(e)[:100]}...")
        
        connection.commit()
        
        print(f"\n📊 执行结果:")
        print(f"   ✅ 成功: {success_count} 条")
        print(f"   ❌ 失败: {error_count} 条")
        
        # 验证数据库
        print("\n🔍 验证数据库结构...")
        cursor.execute("USE yolo")
        
        # 检查表
        cursor.execute("SHOW TABLES")
        tables = [table[0] for table in cursor.fetchall()]
        
        expected_tables = [
            'user', 'monitor', 'detection_task', 'detection_result', 'tracking_target',
            'tracking_performance', 'accident_record', 'violation_record', 'algorithm_config',
            'system_config', 'model_management', 'alarm', 'notification', 'traffic_statistics',
            'system_log', 'performance_metrics', 'realtime_data', 'websocket_session',
            'file_upload', 'api_access_log'
        ]
        
        print(f"\n📋 数据表检查 (期望 {len(expected_tables)} 个表):")
        missing_tables = []
        for table in expected_tables:
            if table in tables:
                print(f"   ✅ {table}")
            else:
                print(f"   ❌ {table} (缺失)")
                missing_tables.append(table)
        
        # 检查数据
        print(f"\n📊 数据内容检查:")
        
        data_checks = [
            ('user', '用户'),
            ('monitor', '监控点'),
            ('system_config', '系统配置'),
            ('model_management', '模型'),
            ('alarm', '警报'),
            ('traffic_statistics', '交通统计'),
            ('accident_record', '事故记录'),
            ('violation_record', '违规记录'),
            ('algorithm_config', '算法配置'),
            ('notification', '通知')
        ]
        
        for table, name in data_checks:
            if table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"   📈 {name}: {count} 条记录")
            else:
                print(f"   ❌ {name}: 表不存在")
        
        # 检查视图
        print(f"\n👁️ 视图检查:")
        cursor.execute("SHOW FULL TABLES WHERE Table_type = 'VIEW'")
        views = cursor.fetchall()
        
        if views:
            for view in views:
                print(f"   ✅ {view[0]}")
        else:
            print("   ⚠️ 未找到视图")
        
        # 检查索引
        print(f"\n🔍 索引检查:")
        cursor.execute("""
            SELECT TABLE_NAME, INDEX_NAME, COLUMN_NAME 
            FROM information_schema.STATISTICS 
            WHERE TABLE_SCHEMA = 'yolo' AND INDEX_NAME != 'PRIMARY'
            ORDER BY TABLE_NAME, INDEX_NAME
        """)
        indexes = cursor.fetchall()
        
        if indexes:
            current_table = None
            for table_name, index_name, column_name in indexes:
                if table_name != current_table:
                    print(f"   📋 {table_name}:")
                    current_table = table_name
                print(f"      - {index_name} ({column_name})")
        
        # 总结
        print("\n" + "=" * 80)
        print("📋 导入总结:")
        
        if not missing_tables and success_count > 0:
            print("✅ 数据库导入成功!")
            print("✅ 所有表结构完整")
            print("✅ 测试数据已导入")
            print("✅ 索引和视图已创建")
            print("\n🎉 系统已准备就绪，可以启动后端服务!")
            
            # 显示重要信息
            print("\n📝 重要信息:")
            print("   👥 默认用户账号:")
            print("      - admin/123456 (管理员)")
            print("      - operator/123456 (操作员)")
            print("      - viewer/123456 (查看者)")
            print("   📹 监控点数量: 15个 (杭州-千岛湖全程)")
            print("   🚨 测试警报: 7条")
            print("   📊 交通统计: 12条")
            print("   🔧 系统配置: 15项")
            
        else:
            print("❌ 数据库导入存在问题:")
            if missing_tables:
                print(f"   缺失表: {', '.join(missing_tables)}")
            if error_count > 0:
                print(f"   执行错误: {error_count} 条")
            print("\n🔧 建议重新检查SQL文件并重新导入")
        
        print("=" * 80)
        
        cursor.close()
        connection.close()
        
        return len(missing_tables) == 0 and error_count == 0
        
    except pymysql.Error as e:
        print(f"❌ 数据库操作失败: {e}")
        return False
    
    except Exception as e:
        print(f"❌ 导入过程出错: {e}")
        return False

def test_backend_connection():
    """测试后端数据库连接"""
    print("\n🧪 测试后端数据库连接...")
    
    try:
        # 模拟后端连接测试
        config = {
            'host': '127.0.0.1',
            'port': 3306,
            'user': 'root',
            'password': '123456',
            'database': 'yolo',
            'charset': 'utf8mb4'
        }
        
        connection = pymysql.connect(**config)
        cursor = connection.cursor()
        
        # 测试基本查询
        test_queries = [
            ("SELECT COUNT(*) FROM user", "用户表查询"),
            ("SELECT COUNT(*) FROM monitor", "监控点表查询"),
            ("SELECT COUNT(*) FROM system_config", "系统配置查询"),
            ("SELECT * FROM v_system_overview", "系统概览视图查询")
        ]
        
        for query, description in test_queries:
            try:
                cursor.execute(query)
                result = cursor.fetchone()
                print(f"   ✅ {description}: {result}")
            except Exception as e:
                print(f"   ❌ {description}: {e}")
        
        cursor.close()
        connection.close()
        
        print("✅ 后端数据库连接测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 后端数据库连接测试失败: {e}")
        return False

def main():
    """主函数"""
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 导入数据库
    success = import_database()
    
    if success:
        # 测试后端连接
        test_backend_connection()
        
        print("\n🎯 下一步操作:")
        print("1. 启动后端服务: python backend/app_enhanced.py")
        print("2. 访问系统: http://localhost:5500")
        print("3. 开始前端开发")
        
    else:
        print("\n🔧 故障排除:")
        print("1. 检查MySQL服务是否启动")
        print("2. 检查用户名密码是否正确")
        print("3. 检查SQL文件是否完整")
        print("4. 重新运行导入脚本")
    
    print(f"\n⏰ 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
