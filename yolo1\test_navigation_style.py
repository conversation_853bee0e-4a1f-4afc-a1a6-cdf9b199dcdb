#!/usr/bin/env python
# -*- coding: utf-8 -*-
# 测试导航栏样式修改

import sys
import os
from PySide6.QtWidgets import QApplication
from main import MainWindow
import apply_style

if __name__ == "__main__":
    print("启动应用以测试导航栏样式修改...")
    app = QApplication(sys.argv)
    
    # 应用样式
    apply_style.apply_style_to_app(app)
    
    # 检查CSS文件是否存在
    css_file_path = "ui/leftmenu_style.css"
    if os.path.exists(css_file_path):
        print(f"找到左侧导航栏样式文件: {css_file_path}")
    else:
        print(f"警告：左侧导航栏样式文件不存在: {css_file_path}")
    
    # 实例化主窗口
    window = MainWindow()
    
    # 确保导航栏没有阴影
    apply_style.remove_shadow_from_leftmenu(window)
    
    # 显示窗口
    window.show()
    
    # 打印提示
    print("正在测试导航栏样式，请检查导航栏按钮上是否还有阴影...")
    
    # 执行应用程序
    sys.exit(app.exec()) 