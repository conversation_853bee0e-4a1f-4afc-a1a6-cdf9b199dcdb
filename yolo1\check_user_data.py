#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查用户数据和密码
"""

import os
import sys
import pymysql
import hashlib
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(override=True, dotenv_path='config/end-back.env')

def check_user_data():
    """检查用户数据"""
    config = {
        'host': os.getenv('MYSQL_HOST', '127.0.0.1'),
        'port': int(os.getenv('MYSQL_PORT', 3306)),
        'user': os.getenv('MYSQL_USER', os.getenv('MYSQL_user', 'root')),
        'password': os.getenv('MYSQL_PASSWORD', os.getenv('MYSQL_password', '123456')),
        'database': os.getenv('MYSQL_DATABASE', os.getenv('MYSQL_db', 'yolo')),
        'charset': 'utf8mb4',
        'autocommit': True,
        'cursorclass': pymysql.cursors.DictCursor
    }
    
    try:
        connection = pymysql.connect(**config)
        with connection.cursor() as cursor:
            # 查询所有用户
            cursor.execute("SELECT id, username, password, email, grade FROM user")
            users = cursor.fetchall()
            
            print("🔍 数据库用户信息:")
            print("=" * 60)
            
            if users:
                for user in users:
                    print(f"ID: {user['id']}")
                    print(f"用户名: {user['username']}")
                    print(f"密码(MD5): {user['password']}")
                    print(f"邮箱: {user['email']}")
                    print(f"角色: {user['grade']}")
                    print("-" * 40)
                
                # 测试密码加密
                print("\n🔐 密码加密测试:")
                test_passwords = ['123456', 'admin', 'password']
                for pwd in test_passwords:
                    md5_hash = hashlib.md5(pwd.encode()).hexdigest()
                    print(f"'{pwd}' -> MD5: {md5_hash}")
                
                # 检查admin用户
                print("\n👤 检查admin用户:")
                admin_user = None
                for user in users:
                    if user['username'] == 'admin':
                        admin_user = user
                        break
                
                if admin_user:
                    print(f"✅ 找到admin用户")
                    print(f"   密码哈希: {admin_user['password']}")
                    
                    # 测试常见密码
                    common_passwords = ['123456', 'admin', 'password', '123', '111111']
                    for pwd in common_passwords:
                        md5_hash = hashlib.md5(pwd.encode()).hexdigest()
                        if md5_hash == admin_user['password']:
                            print(f"✅ 密码匹配: '{pwd}'")
                            break
                    else:
                        print("❌ 未找到匹配的密码")
                else:
                    print("❌ 未找到admin用户")
                    print("💡 可用用户:")
                    for user in users:
                        print(f"   - {user['username']}")
            else:
                print("❌ 数据库中没有用户数据")
                
        connection.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

if __name__ == "__main__":
    check_user_data()
