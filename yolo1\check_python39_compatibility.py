# -*- coding: utf-8 -*-
# @Description : Python 3.9.16兼容性检查脚本
# @Date : 2025年6月20日

import sys
import subprocess
import importlib.util

def check_python_version():
    """检查Python版本"""
    print("=" * 80)
    print("Python版本检查")
    print("=" * 80)
    
    version = sys.version_info
    print(f"当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major == 3 and version.minor == 9:
        print("✓ Python 3.9版本，兼容性良好")
        return True
    elif version.major == 3 and version.minor in [8, 10, 11]:
        print(f"⚠ Python {version.major}.{version.minor}版本，大部分包兼容")
        return True
    else:
        print(f"✗ Python {version.major}.{version.minor}版本，可能存在兼容性问题")
        return False

def check_wheel_compatibility():
    """检查wheel文件兼容性"""
    print("\n" + "=" * 80)
    print("Wheel文件兼容性检查")
    print("=" * 80)
    
    # 您的wheel文件信息
    torch_wheel = r"C:\Users\<USER>\Downloads\torch-2.1.2+cu118-cp311-cp311-win_amd64.whl"
    torchvision_wheel = r"C:\Users\<USER>\Downloads\torchvision-0.16.1+cu118-cp311-cp311-win_amd64.whl"
    
    import os
    
    print("检查wheel文件...")
    if os.path.exists(torch_wheel):
        print(f"✓ PyTorch wheel文件存在: {torch_wheel}")
        print("  文件信息: torch-2.1.2+cu118-cp311-cp311-win_amd64.whl")
        print("  编译版本: Python 3.11 (cp311)")
        print("  CUDA版本: 11.8")
        print("  平台: Windows AMD64")
    else:
        print(f"✗ PyTorch wheel文件不存在: {torch_wheel}")
    
    if os.path.exists(torchvision_wheel):
        print(f"✓ TorchVision wheel文件存在: {torchvision_wheel}")
        print("  文件信息: torchvision-0.16.1+cu118-cp311-cp311-win_amd64.whl")
        print("  编译版本: Python 3.11 (cp311)")
    else:
        print(f"✗ TorchVision wheel文件不存在: {torchvision_wheel}")
    
    # 兼容性分析
    current_version = sys.version_info
    if current_version.major == 3 and current_version.minor == 11:
        print("\n✓ 完美兼容: 当前Python 3.11与wheel文件匹配")
        return "perfect"
    elif current_version.major == 3 and current_version.minor == 9:
        print("\n⚠ 部分兼容: Python 3.9可能与cp311 wheel不兼容")
        print("建议:")
        print("1. 使用Conda安装PyTorch (推荐)")
        print("2. 寻找cp39版本的wheel文件")
        print("3. 从源码编译")
        return "partial"
    else:
        print(f"\n✗ 不兼容: Python {current_version.major}.{current_version.minor}与cp311 wheel不匹配")
        return "incompatible"

def get_compatible_versions():
    """获取Python 3.9兼容的包版本"""
    print("\n" + "=" * 80)
    print("Python 3.9.16兼容的包版本推荐")
    print("=" * 80)
    
    compatible_packages = {
        "PyTorch生态": {
            "torch": "2.1.2 (CUDA 11.8)",
            "torchvision": "0.16.2",
            "torchaudio": "2.1.2"
        },
        "科学计算": {
            "numpy": "1.24.3",
            "scipy": "1.10.1", 
            "matplotlib": "3.7.2",
            "pandas": "2.0.3"
        },
        "计算机视觉": {
            "opencv-python": "********",
            "pillow": "9.5.0",
            "supervision": "0.16.0+"
        },
        "机器学习": {
            "ultralytics": "8.0.0+",
            "scikit-learn": "1.3.0"
        },
        "Web框架": {
            "flask": "2.3.3",
            "flask-cors": "4.0.0",
            "werkzeug": "2.3.7"
        },
        "数据库": {
            "pymysql": "1.1.0",
            "sqlalchemy": "2.0.23"
        },
        "工具包": {
            "python-dotenv": "1.0.0",
            "requests": "2.31.0",
            "tqdm": "4.66.1",
            "psutil": "5.9.6"
        }
    }
    
    for category, packages in compatible_packages.items():
        print(f"\n{category}:")
        for package, version in packages.items():
            print(f"  {package}: {version}")
    
    return compatible_packages

def check_cuda_compatibility():
    """检查CUDA兼容性"""
    print("\n" + "=" * 80)
    print("CUDA兼容性检查")
    print("=" * 80)
    
    try:
        # 检查NVIDIA驱动
        result = subprocess.run("nvidia-smi", capture_output=True, text=True, shell=True)
        if result.returncode == 0:
            print("✓ NVIDIA驱动已安装")
            # 解析驱动版本
            lines = result.stdout.split('\n')
            for line in lines:
                if "Driver Version:" in line:
                    driver_version = line.split("Driver Version:")[1].split()[0]
                    print(f"  驱动版本: {driver_version}")
                    break
        else:
            print("✗ NVIDIA驱动未安装或nvidia-smi不可用")
            return False
    except FileNotFoundError:
        print("✗ nvidia-smi命令不存在，请安装NVIDIA驱动")
        return False
    
    # CUDA版本兼容性
    print("\nCUDA版本兼容性:")
    cuda_compatibility = {
        "CUDA 11.8": {
            "PyTorch": "2.1.2",
            "驱动要求": ">=520.61",
            "推荐": "✓ 推荐版本"
        },
        "CUDA 12.0": {
            "PyTorch": "2.1.0+",
            "驱动要求": ">=525.60",
            "推荐": "⚠ 较新版本"
        },
        "CUDA 11.7": {
            "PyTorch": "1.13.0-2.0.1",
            "驱动要求": ">=515.43",
            "推荐": "⚠ 较旧版本"
        }
    }
    
    for cuda_version, info in cuda_compatibility.items():
        print(f"  {cuda_version}:")
        for key, value in info.items():
            print(f"    {key}: {value}")
    
    return True

def generate_conda_commands():
    """生成Conda安装命令"""
    print("\n" + "=" * 80)
    print("推荐的Conda安装命令")
    print("=" * 80)
    
    commands = [
        "# 1. 创建Python 3.9.16环境",
        "conda create -n ByteTrack python=3.9.16 -y",
        "",
        "# 2. 激活环境",
        "conda activate ByteTrack",
        "",
        "# 3. 安装PyTorch CUDA版本 (推荐方式)",
        "conda install pytorch==2.1.2 torchvision==0.16.2 torchaudio==2.1.2 pytorch-cuda=11.8 -c pytorch -c nvidia -y",
        "",
        "# 4. 安装科学计算包",
        "conda install numpy=1.24.3 scipy=1.10.1 matplotlib=3.7.2 pandas=2.0.3 -y",
        "",
        "# 5. 安装其他依赖",
        "pip install opencv-python==********",
        "pip install pillow==9.5.0",
        "pip install ultralytics>=8.0.0",
        "pip install supervision>=0.16.0",
        "pip install flask==2.3.3 flask-cors==4.0.0",
        "pip install pymysql==1.1.0 python-dotenv==1.0.0",
        "pip install psutil==5.9.6 tqdm requests",
        "",
        "# 6. 验证安装",
        "python -c \"import torch; print(f'PyTorch: {torch.__version__}'); print(f'CUDA: {torch.cuda.is_available()}')\"",
        "python -c \"from ultralytics import YOLO; print('YOLO导入成功')\""
    ]
    
    print("复制以下命令到命令行执行:")
    print("-" * 60)
    for cmd in commands:
        print(cmd)
    
    # 保存到文件
    with open("conda_install_commands.txt", "w", encoding="utf-8") as f:
        f.write("\n".join(commands))
    
    print("-" * 60)
    print("✓ 命令已保存到: conda_install_commands.txt")

def check_conflicts():
    """检查潜在冲突"""
    print("\n" + "=" * 80)
    print("潜在冲突检查")
    print("=" * 80)
    
    conflicts = {
        "OpenCV冲突": {
            "问题": "opencv-python 与 opencv-contrib-python 冲突",
            "解决": "只安装 opencv-python==********",
            "命令": "pip uninstall opencv-contrib-python -y"
        },
        "NumPy版本": {
            "问题": "NumPy版本与PyTorch不兼容",
            "解决": "使用 numpy==1.24.3",
            "命令": "pip install numpy==1.24.3"
        },
        "Pillow版本": {
            "问题": "Pillow版本过新或过旧",
            "解决": "使用 pillow==9.5.0",
            "命令": "pip install pillow==9.5.0"
        },
        "PyTorch版本": {
            "问题": "多个PyTorch版本冲突",
            "解决": "完全卸载后重新安装",
            "命令": "pip uninstall torch torchvision torchaudio -y"
        }
    }
    
    for conflict_name, info in conflicts.items():
        print(f"\n{conflict_name}:")
        print(f"  问题: {info['问题']}")
        print(f"  解决: {info['解决']}")
        print(f"  命令: {info['命令']}")

def main():
    """主函数"""
    print("基于Yolov8与ByteTrack的高速公路智慧监控平台")
    print("Python 3.9.16 + CUDA兼容性检查")
    
    # 检查Python版本
    python_ok = check_python_version()
    
    # 检查wheel兼容性
    wheel_compat = check_wheel_compatibility()
    
    # 获取兼容版本
    get_compatible_versions()
    
    # 检查CUDA
    cuda_ok = check_cuda_compatibility()
    
    # 检查冲突
    check_conflicts()
    
    # 生成安装命令
    generate_conda_commands()
    
    # 总结建议
    print("\n" + "=" * 80)
    print("总结和建议")
    print("=" * 80)
    
    if wheel_compat == "perfect":
        print("✓ 可以直接使用您的wheel文件")
        print("  命令: pip install torch-2.1.2+cu118-cp311-cp311-win_amd64.whl")
    elif wheel_compat == "partial":
        print("⚠ 建议使用Conda安装PyTorch")
        print("  原因: wheel文件为Python 3.11编译，您使用Python 3.9")
        print("  推荐: conda install pytorch==2.1.2 pytorch-cuda=11.8 -c pytorch -c nvidia")
    else:
        print("✗ 不建议使用当前wheel文件")
        print("  建议: 使用Conda或寻找兼容的wheel文件")
    
    print(f"\nPython版本: {'✓ 兼容' if python_ok else '✗ 不兼容'}")
    print(f"CUDA支持: {'✓ 可用' if cuda_ok else '✗ 不可用'}")
    
    print("\n推荐操作顺序:")
    print("1. 运行: setup_conda_env.bat")
    print("2. 或手动执行: conda_install_commands.txt 中的命令")
    print("3. 测试: python test_cuda.py")
    print("4. 启动: python start_server.py")

if __name__ == "__main__":
    main()
