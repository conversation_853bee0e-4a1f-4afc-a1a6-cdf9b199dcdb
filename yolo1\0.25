Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple
Collecting numpy<2.0.0,>=1.20.0
  Using cached https://pypi.tuna.tsinghua.edu.cn/packages/b5/42/054082bd8220bbf6f297f982f0a8f5479fcbc55c8b511d928df07b965869/numpy-1.26.4-cp39-cp39-win_amd64.whl (15.8 MB)
Collecting pandas
  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/2f/49/5c30646e96c684570925b772eac4eb0a8cb0ca590fa978f56c5d3ae73ea1/pandas-2.2.3-cp39-cp39-win_amd64.whl (11.6 MB)
     ---------------------------------------- 11.6/11.6 MB 15.8 MB/s eta 0:00:00
Requirement already satisfied: python-dateutil>=2.8.2 in c:\users\<USER>\.conda\envs\yolo_env\lib\site-packages (from pandas) (2.9.0.post0)
Collecting pytz>=2020.1 (from pandas)
  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/81/c4/34e93fe5f5429d7570ec1fa436f1986fb1f00c3e0f43a589fe2bbcd22c3f/pytz-2025.2-py2.py3-none-any.whl (509 kB)
Collecting tzdata>=2022.7 (from pandas)
  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/5c/23/c7abc0ca0a1526a0774eca151daeb8de62ec457e77262b66b359c3c7679e/tzdata-2025.2-py2.py3-none-any.whl (347 kB)
Requirement already satisfied: six>=1.5 in c:\users\<USER>\.conda\envs\yolo_env\lib\site-packages (from python-dateutil>=2.8.2->pandas) (1.17.0)
Installing collected packages: pytz, tzdata, numpy, pandas
  Attempting uninstall: numpy
    Found existing installation: numpy 2.0.2
    Uninstalling numpy-2.0.2:
      Successfully uninstalled numpy-2.0.2

Successfully installed numpy-1.26.4 pandas-2.2.3 pytz-2025.2 tzdata-2025.2
