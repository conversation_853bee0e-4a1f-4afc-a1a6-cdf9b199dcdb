# -*- coding: utf-8 -*-
# 数据库状态检查脚本

import os
import sys
import pymysql
from datetime import datetime

def check_database():
    """检查数据库状态"""
    print("=" * 60)
    print("🔍 基于Yolov8与ByteTrack的高速公路智慧监控平台 - 数据库状态检查")
    print("=" * 60)
    
    # 数据库配置
    config = {
        'host': '127.0.0.1',
        'port': 3306,
        'user': 'root',
        'password': '123456',
        'database': 'yolo',
        'charset': 'utf8mb4'
    }
    
    try:
        # 测试连接
        print("📡 测试数据库连接...")
        connection = pymysql.connect(**config)
        cursor = connection.cursor()
        
        print("✅ 数据库连接成功")
        
        # 检查数据库版本
        cursor.execute("SELECT VERSION()")
        version = cursor.fetchone()[0]
        print(f"📊 MySQL版本: {version}")
        
        # 检查数据库
        cursor.execute("SHOW DATABASES LIKE 'yolo'")
        db_exists = cursor.fetchone()
        if db_exists:
            print("✅ 数据库 'yolo' 存在")
        else:
            print("❌ 数据库 'yolo' 不存在")
            return False
        
        # 切换到yolo数据库
        cursor.execute("USE yolo")
        
        # 检查必要的表
        required_tables = [
            'user', 'monitor', 'alarm', 'detection_task', 'detection_result',
            'system_config', 'system_log', 'traffic_statistics', 'file_upload',
            'performance_metrics', 'realtime_data', 'websocket_sessions',
            'model_management', 'api_access_log', 'notification'
        ]
        
        # 扩展表
        extended_tables = [
            'violation_records', 'accident_records', 'algorithm_configs', 'tracking_performance'
        ]
        
        print("\n📋 检查数据表:")
        cursor.execute("SHOW TABLES")
        existing_tables = [table[0] for table in cursor.fetchall()]
        
        missing_tables = []
        for table in required_tables:
            if table in existing_tables:
                print(f"✅ {table}")
            else:
                print(f"❌ {table} (缺失)")
                missing_tables.append(table)
        
        print("\n📋 检查扩展表:")
        missing_extended = []
        for table in extended_tables:
            if table in existing_tables:
                print(f"✅ {table}")
            else:
                print(f"❌ {table} (缺失)")
                missing_extended.append(table)
        
        # 检查数据
        print("\n📊 检查数据内容:")
        
        # 检查用户数据
        cursor.execute("SELECT COUNT(*) FROM user")
        user_count = cursor.fetchone()[0]
        print(f"👥 用户数量: {user_count}")
        
        if user_count > 0:
            cursor.execute("SELECT username, grade FROM user")
            users = cursor.fetchall()
            for username, grade in users:
                print(f"   - {username} ({grade})")
        
        # 检查监控点数据
        cursor.execute("SELECT COUNT(*) FROM monitor")
        monitor_count = cursor.fetchone()[0]
        print(f"📹 监控点数量: {monitor_count}")
        
        if monitor_count > 0:
            cursor.execute("SELECT name, location, connection_status FROM monitor LIMIT 5")
            monitors = cursor.fetchall()
            for name, location, status in monitors:
                print(f"   - {name} ({location}) - {status}")
            if monitor_count > 5:
                print(f"   ... 还有 {monitor_count - 5} 个监控点")
        
        # 检查警报数据
        cursor.execute("SELECT COUNT(*) FROM alarm")
        alarm_count = cursor.fetchone()[0]
        print(f"🚨 警报数量: {alarm_count}")
        
        # 检查扩展表数据（如果存在）
        if 'accident_records' in existing_tables:
            cursor.execute("SELECT COUNT(*) FROM accident_records")
            accident_count = cursor.fetchone()[0]
            print(f"🚗 事故记录: {accident_count}")
        
        if 'violation_records' in existing_tables:
            cursor.execute("SELECT COUNT(*) FROM violation_records")
            violation_count = cursor.fetchone()[0]
            print(f"⚠️ 违规记录: {violation_count}")
        
        if 'algorithm_configs' in existing_tables:
            cursor.execute("SELECT COUNT(*) FROM algorithm_configs")
            config_count = cursor.fetchone()[0]
            print(f"⚙️ 算法配置: {config_count}")
        
        # 总结
        print("\n" + "=" * 60)
        print("📋 检查总结:")
        
        if not missing_tables and not missing_extended:
            print("✅ 所有数据表都存在")
            print("✅ 数据库结构完整")
            
            if user_count > 0 and monitor_count > 0:
                print("✅ 基础数据已导入")
                print("🎉 数据库状态良好，可以启动系统")
            else:
                print("⚠️ 缺少基础数据，需要导入初始数据")
                
        else:
            print("❌ 数据库结构不完整")
            if missing_tables:
                print(f"   缺失核心表: {', '.join(missing_tables)}")
            if missing_extended:
                print(f"   缺失扩展表: {', '.join(missing_extended)}")
            print("🔧 需要重新导入数据库结构")
        
        print("=" * 60)
        
        cursor.close()
        connection.close()
        
        return len(missing_tables) == 0
        
    except pymysql.Error as e:
        print(f"❌ 数据库连接失败: {e}")
        print("\n🔧 可能的解决方案:")
        print("1. 检查MySQL服务是否启动")
        print("2. 检查数据库配置信息")
        print("3. 检查网络连接")
        print("4. 导入数据库结构: mysql -u root -p yolo < yolo.sql")
        return False
    
    except Exception as e:
        print(f"❌ 检查过程出错: {e}")
        return False

def check_missing_tables():
    """检查并提供缺失表的解决方案"""
    print("\n🔧 数据库修复建议:")
    print("1. 导入完整数据库结构:")
    print("   mysql -u root -p yolo < yolo.sql")
    print("\n2. 导入扩展表结构:")
    print("   mysql -u root -p yolo < database_extension.sql")
    print("\n3. 重新启动后端服务:")
    print("   python backend/app_enhanced.py")

if __name__ == "__main__":
    success = check_database()
    
    if not success:
        check_missing_tables()
    
    print(f"\n⏰ 检查完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
