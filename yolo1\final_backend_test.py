#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终后端测试脚本 - 测试修复后的数据库连接
"""

import os
import sys
import pymysql
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(override=True, dotenv_path='config/end-back.env')

def test_show_tables():
    """测试SHOW TABLES查询"""
    print("🔍 测试SHOW TABLES查询...")
    
    config = {
        'host': os.getenv('MYSQL_HOST', '127.0.0.1'),
        'port': int(os.getenv('MYSQL_PORT', 3306)),
        'user': os.getenv('MYSQL_USER', os.getenv('MYSQL_user', 'root')),
        'password': os.getenv('MYSQL_PASSWORD', os.getenv('MYSQL_password', '123456')),
        'database': os.getenv('MYSQL_DATABASE', os.getenv('MYSQL_db', 'yolo')),
        'charset': 'utf8mb4',
        'autocommit': True,
        'cursorclass': pymysql.cursors.DictCursor
    }
    
    try:
        connection = pymysql.connect(**config)
        with connection.cursor() as cursor:
            # 测试SHOW TABLES
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            
            print(f"✅ 查询成功，找到 {len(tables)} 个表")
            
            if tables:
                print("第一个表的结果格式:")
                first_table = tables[0]
                print(f"  类型: {type(first_table)}")
                print(f"  内容: {first_table}")
                print(f"  字段: {list(first_table.keys())}")
                
                # 获取数据库名
                db_name = config['database']
                table_key = f'Tables_in_{db_name}'
                
                if table_key in first_table:
                    print(f"✅ 正确的字段名: {table_key}")
                    table_names = [table[table_key] for table in tables]
                    print(f"✅ 表列表: {', '.join(table_names)}")
                else:
                    print(f"❌ 字段名不匹配，可用字段: {list(first_table.keys())}")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_backend_database_module():
    """测试后端数据库模块"""
    print("\n🔍 测试后端数据库模块...")
    
    try:
        # 添加backend路径
        sys.path.append('backend')
        
        from utils.database import get_db_connection, init_database
        
        print("✅ 数据库模块导入成功")
        
        # 测试init_database函数
        print("测试init_database函数...")
        success = init_database()
        
        if success:
            print("✅ init_database函数测试成功")
            return True
        else:
            print("❌ init_database函数测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 后端数据库模块测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 最终后端数据库测试")
    print("=" * 60)
    
    # 1. 测试基础SHOW TABLES查询
    basic_success = test_show_tables()
    
    # 2. 测试后端数据库模块
    backend_success = test_backend_database_module()
    
    print("\n" + "=" * 60)
    print("📋 测试结果:")
    print(f"   基础查询: {'✅ 通过' if basic_success else '❌ 失败'}")
    print(f"   后端模块: {'✅ 通过' if backend_success else '❌ 失败'}")
    
    if basic_success and backend_success:
        print("\n🎉 所有测试通过！")
        print("✅ 现在可以启动后端服务:")
        print("   python backend/app_enhanced.py")
        print("✅ 应该看到所有13个表的信息")
    else:
        print("\n⚠️ 测试失败，请检查错误信息")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
