#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API路由测试脚本 - 验证所有API路由是否正确注册
"""

import requests
import json

def test_api_routes():
    """测试API路由"""
    base_url = "http://127.0.0.1:5500"
    
    print("🚀 API路由测试")
    print("=" * 60)
    
    # 1. 测试基础连接
    print("1. 测试基础连接...")
    try:
        response = requests.get(base_url, timeout=5)
        if response.status_code == 200:
            print("✅ 基础连接成功")
            data = response.json()
            print(f"   状态: {data.get('status')}")
        else:
            print(f"❌ 基础连接失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 基础连接异常: {e}")
        return False
    
    # 2. 测试API文档
    print("\n2. 测试API文档...")
    try:
        response = requests.get(f"{base_url}/api/v1/docs", timeout=5)
        if response.status_code == 200:
            print("✅ API文档正常")
            data = response.json()
            endpoints = data.get('data', {}).get('endpoints', {})
            print(f"   可用模块: {list(endpoints.keys())}")
        else:
            print(f"❌ API文档失败: {response.status_code}")
    except Exception as e:
        print(f"❌ API文档异常: {e}")
    
    # 3. 测试登录接口
    print("\n3. 测试登录接口...")
    try:
        login_data = {"username": "admin", "password": "123456"}
        response = requests.post(f"{base_url}/api/v1/auth/login", json=login_data, timeout=5)
        print(f"   登录响应状态: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 登录接口正常")
            try:
                data = response.json()
                print(f"   响应数据: {data}")

                # 尝试不同的token获取方式
                token = None
                if isinstance(data, dict):
                    # 方式1: data.data.token
                    if 'data' in data and isinstance(data['data'], dict):
                        token = data['data'].get('token')
                    # 方式2: data.token
                    elif 'token' in data:
                        token = data['token']

                if token:
                    print(f"   获取Token: {token[:30]}...")
                    return test_protected_routes(base_url, token)
                else:
                    print("⚠️ 登录成功但未获取到Token")
                    print(f"   数据结构: {list(data.keys()) if isinstance(data, dict) else type(data)}")
            except Exception as e:
                print(f"   JSON解析失败: {e}")
                print(f"   原始响应: {response.text}")
        else:
            print(f"❌ 登录失败: {response.status_code}")
            print(f"   响应内容: {response.text}")
    except Exception as e:
        print(f"❌ 登录接口异常: {e}")
    
    return False

def test_protected_routes(base_url, token):
    """测试需要认证的路由"""
    print("\n4. 测试需要认证的路由...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # 测试路由列表
    test_routes = [
        ("GET", "/api/v1/auth/profile", "用户信息"),
        ("GET", "/api/v1/monitor/list", "监控点列表"),
        ("GET", "/api/v1/analysis/statistics/overview", "概览统计"),
        ("GET", "/api/v1/system/health-check", "健康检查")
    ]
    
    success_count = 0
    total_count = len(test_routes)
    
    for method, path, name in test_routes:
        try:
            if method == "GET":
                response = requests.get(f"{base_url}{path}", headers=headers, timeout=5)
            elif method == "POST":
                response = requests.post(f"{base_url}{path}", headers=headers, timeout=5)
            
            print(f"   {name}: {response.status_code}", end="")
            
            if response.status_code == 200:
                print(" ✅")
                success_count += 1
            elif response.status_code == 404:
                print(" ❌ (404 - 路由不存在)")
                print(f"     响应: {response.text[:100]}...")
            else:
                print(f" ⚠️ ({response.status_code})")
                
        except Exception as e:
            print(f"   {name}: ❌ 异常 - {e}")
    
    print(f"\n📊 路由测试结果: {success_count}/{total_count} 成功")
    
    return success_count == total_count

def main():
    """主函数"""
    print("=" * 60)
    print("🔍 高速公路智能监控系统 - API路由测试")
    print("=" * 60)
    
    success = test_api_routes()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有API路由测试通过！")
        print("✅ 前端可以正常访问后端API")
        print("✅ 404错误已解决")
    else:
        print("⚠️ 部分API路由测试失败")
        print("💡 请检查:")
        print("   1. 后端服务是否正常启动")
        print("   2. 数据库连接是否正常")
        print("   3. API路由是否正确注册")
    print("=" * 60)

if __name__ == "__main__":
    main()
