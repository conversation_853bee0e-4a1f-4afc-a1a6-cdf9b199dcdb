# -*- coding: utf-8 -*-
# @Description : 文件处理工具
# @Date : 2025年6月20日

import os
import cv2
import uuid
import hashlib
from datetime import datetime
from typing import List, Optional, Tuple
import numpy as np
from PIL import Image

# 允许的文件扩展名
ALLOWED_IMAGE_EXTENSIONS = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
ALLOWED_VIDEO_EXTENSIONS = {'.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.webm'}
ALLOWED_MODEL_EXTENSIONS = {'.pt', '.onnx', '.engine', '.trt'}

def allowed_file(filename: str, file_type: str = 'image') -> bool:
    """
    检查文件是否为允许的类型
    
    Args:
        filename: 文件名
        file_type: 文件类型 ('image', 'video', 'model')
    
    Returns:
        bool: 是否允许的文件类型
    """
    if not filename:
        return False
    
    ext = os.path.splitext(filename)[1].lower()
    
    if file_type == 'image':
        return ext in ALLOWED_IMAGE_EXTENSIONS
    elif file_type == 'video':
        return ext in ALLOWED_VIDEO_EXTENSIONS
    elif file_type == 'model':
        return ext in ALLOWED_MODEL_EXTENSIONS
    else:
        return ext in ALLOWED_IMAGE_EXTENSIONS | ALLOWED_VIDEO_EXTENSIONS

def generate_unique_filename(original_filename: str, prefix: str = '') -> str:
    """
    生成唯一的文件名
    
    Args:
        original_filename: 原始文件名
        prefix: 文件名前缀
    
    Returns:
        str: 唯一的文件名
    """
    ext = os.path.splitext(original_filename)[1]
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    unique_id = str(uuid.uuid4())[:8]
    
    if prefix:
        return f"{prefix}_{timestamp}_{unique_id}{ext}"
    else:
        return f"{timestamp}_{unique_id}{ext}"

def save_image(image: np.ndarray, category: str = 'detection', 
               filename: Optional[str] = None) -> str:
    """
    保存图像文件
    
    Args:
        image: 图像数组
        category: 图像分类 ('original', 'detection', 'result')
        filename: 指定的文件名
    
    Returns:
        str: 保存的文件路径
    """
    # 创建保存目录
    save_dir = f"static/images/{category}/{datetime.now().strftime('%Y-%m-%d')}"
    os.makedirs(save_dir, exist_ok=True)
    
    # 生成文件名
    if not filename:
        filename = generate_unique_filename('image.jpg', category)
    
    file_path = os.path.join(save_dir, filename)
    
    # 保存图像
    cv2.imwrite(file_path, image)
    
    return file_path

def save_uploaded_file(file, category: str = 'uploads', 
                      allowed_extensions: Optional[set] = None) -> Tuple[bool, str]:
    """
    保存上传的文件
    
    Args:
        file: 上传的文件对象
        category: 文件分类
        allowed_extensions: 允许的扩展名集合
    
    Returns:
        Tuple[bool, str]: (是否成功, 文件路径或错误信息)
    """
    try:
        if not file or file.filename == '':
            return False, "没有选择文件"
        
        # 检查文件类型
        if allowed_extensions:
            ext = os.path.splitext(file.filename)[1].lower()
            if ext not in allowed_extensions:
                return False, f"不支持的文件类型: {ext}"
        
        # 创建保存目录
        save_dir = f"uploads/{category}/{datetime.now().strftime('%Y-%m-%d')}"
        os.makedirs(save_dir, exist_ok=True)
        
        # 生成唯一文件名
        filename = generate_unique_filename(file.filename, category)
        file_path = os.path.join(save_dir, filename)
        
        # 保存文件
        file.save(file_path)
        
        return True, file_path
    except Exception as e:
        return False, str(e)

def get_file_info(file_path: str) -> dict:
    """
    获取文件信息
    
    Args:
        file_path: 文件路径
    
    Returns:
        dict: 文件信息
    """
    try:
        if not os.path.exists(file_path):
            return {'error': '文件不存在'}
        
        stat = os.stat(file_path)
        file_info = {
            'filename': os.path.basename(file_path),
            'size': stat.st_size,
            'created_time': datetime.fromtimestamp(stat.st_ctime).isoformat(),
            'modified_time': datetime.fromtimestamp(stat.st_mtime).isoformat(),
            'extension': os.path.splitext(file_path)[1].lower()
        }
        
        # 如果是图像文件，获取图像信息
        if file_info['extension'] in ALLOWED_IMAGE_EXTENSIONS:
            try:
                with Image.open(file_path) as img:
                    file_info.update({
                        'width': img.width,
                        'height': img.height,
                        'format': img.format,
                        'mode': img.mode
                    })
            except Exception:
                pass
        
        # 如果是视频文件，获取视频信息
        elif file_info['extension'] in ALLOWED_VIDEO_EXTENSIONS:
            try:
                cap = cv2.VideoCapture(file_path)
                if cap.isOpened():
                    file_info.update({
                        'width': int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)),
                        'height': int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT)),
                        'fps': cap.get(cv2.CAP_PROP_FPS),
                        'frame_count': int(cap.get(cv2.CAP_PROP_FRAME_COUNT)),
                        'duration': cap.get(cv2.CAP_PROP_FRAME_COUNT) / cap.get(cv2.CAP_PROP_FPS)
                    })
                cap.release()
            except Exception:
                pass
        
        return file_info
    except Exception as e:
        return {'error': str(e)}

def calculate_file_hash(file_path: str, algorithm: str = 'md5') -> Optional[str]:
    """
    计算文件哈希值
    
    Args:
        file_path: 文件路径
        algorithm: 哈希算法 ('md5', 'sha1', 'sha256')
    
    Returns:
        Optional[str]: 文件哈希值
    """
    try:
        if algorithm == 'md5':
            hash_obj = hashlib.md5()
        elif algorithm == 'sha1':
            hash_obj = hashlib.sha1()
        elif algorithm == 'sha256':
            hash_obj = hashlib.sha256()
        else:
            return None
        
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_obj.update(chunk)
        
        return hash_obj.hexdigest()
    except Exception:
        return None

def cleanup_old_files(directory: str, days: int = 30, 
                     extensions: Optional[List[str]] = None) -> int:
    """
    清理旧文件
    
    Args:
        directory: 目录路径
        days: 保留天数
        extensions: 要清理的文件扩展名列表
    
    Returns:
        int: 清理的文件数量
    """
    try:
        if not os.path.exists(directory):
            return 0
        
        cutoff_time = datetime.now().timestamp() - (days * 24 * 60 * 60)
        cleaned_count = 0
        
        for root, dirs, files in os.walk(directory):
            for file in files:
                file_path = os.path.join(root, file)
                
                # 检查文件扩展名
                if extensions:
                    ext = os.path.splitext(file)[1].lower()
                    if ext not in extensions:
                        continue
                
                # 检查文件修改时间
                try:
                    if os.path.getmtime(file_path) < cutoff_time:
                        os.remove(file_path)
                        cleaned_count += 1
                except Exception:
                    continue
        
        return cleaned_count
    except Exception:
        return 0

def create_thumbnail(image_path: str, thumbnail_path: str, 
                    size: Tuple[int, int] = (150, 150)) -> bool:
    """
    创建缩略图
    
    Args:
        image_path: 原图路径
        thumbnail_path: 缩略图保存路径
        size: 缩略图尺寸
    
    Returns:
        bool: 是否成功创建
    """
    try:
        with Image.open(image_path) as img:
            # 创建缩略图
            img.thumbnail(size, Image.Resampling.LANCZOS)
            
            # 确保保存目录存在
            os.makedirs(os.path.dirname(thumbnail_path), exist_ok=True)
            
            # 保存缩略图
            img.save(thumbnail_path, optimize=True, quality=85)
            
        return True
    except Exception:
        return False

def compress_image(image_path: str, output_path: str, 
                  quality: int = 85, max_size: Optional[Tuple[int, int]] = None) -> bool:
    """
    压缩图像
    
    Args:
        image_path: 原图路径
        output_path: 输出路径
        quality: 压缩质量 (1-100)
        max_size: 最大尺寸 (width, height)
    
    Returns:
        bool: 是否成功压缩
    """
    try:
        with Image.open(image_path) as img:
            # 如果指定了最大尺寸，进行缩放
            if max_size:
                img.thumbnail(max_size, Image.Resampling.LANCZOS)
            
            # 确保保存目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 保存压缩后的图像
            img.save(output_path, optimize=True, quality=quality)
            
        return True
    except Exception:
        return False

def get_directory_size(directory: str) -> int:
    """
    获取目录大小
    
    Args:
        directory: 目录路径
    
    Returns:
        int: 目录大小（字节）
    """
    try:
        total_size = 0
        for root, dirs, files in os.walk(directory):
            for file in files:
                file_path = os.path.join(root, file)
                if os.path.exists(file_path):
                    total_size += os.path.getsize(file_path)
        return total_size
    except Exception:
        return 0

def format_file_size(size_bytes: int) -> str:
    """
    格式化文件大小
    
    Args:
        size_bytes: 文件大小（字节）
    
    Returns:
        str: 格式化的文件大小
    """
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.2f} {size_names[i]}"

def validate_image_file(file_path: str) -> Tuple[bool, str]:
    """
    验证图像文件
    
    Args:
        file_path: 图像文件路径
    
    Returns:
        Tuple[bool, str]: (是否有效, 错误信息)
    """
    try:
        if not os.path.exists(file_path):
            return False, "文件不存在"
        
        # 检查文件扩展名
        ext = os.path.splitext(file_path)[1].lower()
        if ext not in ALLOWED_IMAGE_EXTENSIONS:
            return False, f"不支持的图像格式: {ext}"
        
        # 尝试打开图像
        with Image.open(file_path) as img:
            # 验证图像
            img.verify()
            
        # 重新打开获取信息（verify后需要重新打开）
        with Image.open(file_path) as img:
            width, height = img.size
            
            # 检查图像尺寸
            if width < 1 or height < 1:
                return False, "无效的图像尺寸"
            
            # 检查图像尺寸是否过大
            if width > 10000 or height > 10000:
                return False, "图像尺寸过大"
        
        return True, "图像文件有效"
    except Exception as e:
        return False, f"图像文件无效: {str(e)}"

def validate_video_file(file_path: str) -> Tuple[bool, str]:
    """
    验证视频文件
    
    Args:
        file_path: 视频文件路径
    
    Returns:
        Tuple[bool, str]: (是否有效, 错误信息)
    """
    try:
        if not os.path.exists(file_path):
            return False, "文件不存在"
        
        # 检查文件扩展名
        ext = os.path.splitext(file_path)[1].lower()
        if ext not in ALLOWED_VIDEO_EXTENSIONS:
            return False, f"不支持的视频格式: {ext}"
        
        # 尝试打开视频
        cap = cv2.VideoCapture(file_path)
        
        if not cap.isOpened():
            return False, "无法打开视频文件"
        
        # 获取视频信息
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        cap.release()
        
        # 验证视频参数
        if frame_count <= 0:
            return False, "视频帧数无效"
        
        if fps <= 0:
            return False, "视频帧率无效"
        
        if width <= 0 or height <= 0:
            return False, "视频尺寸无效"
        
        return True, "视频文件有效"
    except Exception as e:
        return False, f"视频文件无效: {str(e)}"
