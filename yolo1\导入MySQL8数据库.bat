@echo off
chcp 65001 >nul
echo ================================================================================
echo 🚀 高速公路YOLOv8智能监控系统 - MySQL 8.0数据库导入工具
echo ================================================================================
echo.

:: 设置变量
set MYSQL_USER=root
set MYSQL_PASSWORD=123456
set DATABASE_NAME=yolo
set SQL_FILE=MySQL8.0优化数据库.sql

echo 📋 导入配置:
echo    MySQL版本: 8.0+
echo    MySQL用户: %MYSQL_USER%
echo    数据库名: %DATABASE_NAME%
echo    SQL文件: %SQL_FILE%
echo.

:: 检查MySQL是否可用
echo 🔍 检查MySQL服务...
mysql --version >nul 2>&1
if errorlevel 1 (
    echo ❌ MySQL命令不可用，请检查MySQL是否已安装并添加到PATH
    echo.
    echo 💡 解决方案:
    echo    1. 确保MySQL 8.0已正确安装
    echo    2. 将MySQL的bin目录添加到系统PATH
    echo    3. 重新打开命令提示符
    pause
    exit /b 1
)
echo ✅ MySQL命令可用

:: 检查SQL文件是否存在
echo.
echo 🔍 检查SQL文件...
if not exist "%SQL_FILE%" (
    echo ❌ SQL文件不存在: %SQL_FILE%
    echo.
    echo 💡 请确保以下文件存在:
    echo    - MySQL8.0优化数据库.sql
    pause
    exit /b 1
)
echo ✅ SQL文件存在

:: 测试MySQL连接
echo.
echo 🔍 测试MySQL连接...
mysql -u %MYSQL_USER% -p%MYSQL_PASSWORD% -e "SELECT VERSION();" >nul 2>&1
if errorlevel 1 (
    echo ❌ MySQL连接失败，请检查用户名和密码
    echo.
    echo 💡 解决方案:
    echo    1. 确保MySQL服务已启动
    echo    2. 检查用户名和密码是否正确
    echo    3. 修改本脚本中的MYSQL_USER和MYSQL_PASSWORD变量
    pause
    exit /b 1
)
echo ✅ MySQL连接成功

:: 显示MySQL版本
for /f "tokens=*" %%i in ('mysql -u %MYSQL_USER% -p%MYSQL_PASSWORD% -e "SELECT VERSION();" -s -N') do set MYSQL_VERSION=%%i
echo    MySQL版本: %MYSQL_VERSION%

:: 确认导入
echo.
echo ⚠️  警告: 此操作将删除现有的 '%DATABASE_NAME%' 数据库并重新创建
echo.
set /p confirm=是否继续? (y/N): 
if /i not "%confirm%"=="y" (
    echo 操作已取消
    pause
    exit /b 0
)

:: 导入数据库
echo.
echo 🚀 开始导入数据库...
echo.

mysql -u %MYSQL_USER% -p%MYSQL_PASSWORD% < "%SQL_FILE%"
if errorlevel 1 (
    echo ❌ 数据库导入失败
    echo.
    echo 💡 可能的原因:
    echo    1. SQL文件格式错误
    echo    2. MySQL权限不足
    echo    3. 数据库连接中断
    echo    4. MySQL版本不兼容
    pause
    exit /b 1
)

echo ✅ 数据库导入成功

:: 验证导入结果
echo.
echo 🧪 验证导入结果...
mysql -u %MYSQL_USER% -p%MYSQL_PASSWORD% -e "USE %DATABASE_NAME%; SELECT 'Tables:' as info, COUNT(*) as count FROM information_schema.tables WHERE table_schema='%DATABASE_NAME%' UNION ALL SELECT 'Users:', COUNT(*) FROM user UNION ALL SELECT 'Monitors:', COUNT(*) FROM monitor UNION ALL SELECT 'Configs:', COUNT(*) FROM system_config UNION ALL SELECT 'Models:', COUNT(*) FROM model_management UNION ALL SELECT 'Alarms:', COUNT(*) FROM alarm;"

echo.
echo ================================================================================
echo 🎉 MySQL 8.0数据库导入完成！
echo ================================================================================

echo.
echo 📊 优化后的数据库特点:
echo    ✅ MySQL 8.0兼容性优化
echo    ✅ 精简表结构，删除冗余表
echo    ✅ 严格按照后端API功能设计
echo    ✅ 支持YOLOv8检测和ByteTrack追踪
echo    ✅ 支持事故检测和多路视频流
echo    ✅ 优化索引和查询性能

echo.
echo 📋 数据库包含:
echo    👥 用户表: 2个用户 (admin, operator)
echo    📹 监控点表: 5个监控点 (杭州至千岛湖)
echo    🚨 警报表: 3条警报记录
echo    ⚙️  系统配置表: 7项配置
echo    🤖 模型管理表: 2个YOLO模型
echo    📊 交通统计表: 4条统计数据
echo    🚗 事故记录表: 2条事故记录

echo.
echo 🔑 默认登录账号:
echo    管理员: admin / admin (MD5: 21232f297a57a5a743894a0e4a801fc3)
echo    班长: operator / operator (MD5: 4b583376b2767b923c3e1da60d10de59)

echo.
echo 🎯 下一步操作:
echo    1. 启动后端服务: python backend/app_enhanced.py
echo    2. 访问系统: http://localhost:5500
echo    3. 开始前端开发 (Vue 3 + Arco Design)

echo.
echo 📞 如有问题，请检查:
echo    1. MySQL 8.0服务状态
echo    2. 用户权限设置
echo    3. 数据库连接配置
echo    4. SQL文件完整性

echo.
echo 🚀 前端开发建议:
echo    1. 按照《前端功能模块规划.md》逐步实现
echo    2. 先完成认证模块，再实现业务模块
echo    3. 每个模块完成后充分测试
echo    4. 严格按照后端API接口设计前端功能

echo.
pause
