# -*- coding: utf-8 -*-
# @Description : 测试登录功能
# @Date : 2025年6月20日

import requests
import json

def test_login():
    """测试登录功能"""
    base_url = "http://127.0.0.1:5500"
    
    print("🔐 测试登录功能")
    print("="*50)
    
    # 测试账号
    test_accounts = [
        ('admin', '123456', '管理员'),
        ('operator', 'operator', '操作员'),
        ('viewer', 'hello', '观察员'),
        ('admin', 'wrong', '错误密码测试'),
        ('wrong', '123456', '错误用户名测试')
    ]
    
    for username, password, description in test_accounts:
        print(f"\n📋 测试: {description}")
        print(f"👤 用户名: {username}")
        print(f"🔑 密码: {password}")
        
        try:
            response = requests.post(f"{base_url}/api/v1/auth/login", 
                json={
                    'username': username,
                    'password': password
                },
                timeout=10
            )
            
            print(f"📊 状态码: {response.status_code}")
            
            try:
                data = response.json()
                print(f"📝 响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
                
                if data.get('code') == 200:
                    print("✅ 登录成功!")
                    token = data['data']['token']
                    user = data['data']['user']
                    print(f"🎫 Token: {token}")
                    print(f"👤 用户信息: {user['username']} ({user['grade']})")
                    
                    # 测试使用token访问其他接口
                    print("\n🧪 测试Token访问监控点列表...")
                    headers = {'Authorization': f'Bearer {token}'}
                    monitor_response = requests.get(f"{base_url}/api/v1/monitor/list", headers=headers, timeout=5)
                    print(f"📊 监控点接口状态码: {monitor_response.status_code}")
                    
                    if monitor_response.status_code == 200:
                        monitor_data = monitor_response.json()
                        if monitor_data.get('code') == 200:
                            print(f"✅ Token验证成功! 获取到 {len(monitor_data['data']['list'])} 个监控点")
                        else:
                            print(f"❌ Token验证失败: {monitor_data.get('message')}")
                    else:
                        print(f"❌ 监控点接口访问失败")
                        
                else:
                    print(f"❌ 登录失败: {data.get('message')}")
                    
            except json.JSONDecodeError:
                print(f"📄 非JSON响应: {response.text}")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
    
    print(f"\n{'='*50}")
    print("🎯 登录测试完成!")

if __name__ == "__main__":
    test_login()
