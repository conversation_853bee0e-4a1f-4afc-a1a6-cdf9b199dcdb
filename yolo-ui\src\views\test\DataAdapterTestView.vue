<template>
  <div class="data-adapter-test">
    <a-card title="数据适配器测试">
      <a-alert type="info" show-icon style="margin-bottom: 16px;">
        <template #title>数据适配器验证</template>
        <div>
          <p>测试后端API返回的数据格式，验证数据适配器是否正确工作</p>
          <p>确保前端能够正确处理各种后端数据格式</p>
        </div>
      </a-alert>
      
      <a-tabs v-model:active-key="activeTab" type="card">
        <!-- 实时警报适配测试 -->
        <a-tab-pane key="alerts" title="实时警报适配">
          <div class="adapter-test-section">
            <h3>实时警报数据适配测试</h3>
            
            <div class="test-controls">
              <a-space>
                <a-button @click="testAlertAdapter" :loading="alertTesting" type="primary">
                  测试警报适配器
                </a-button>
                <a-button @click="testAlertApi" :loading="alertApiTesting">
                  测试实际API
                </a-button>
                <a-button @click="generateMockAlertData">
                  生成模拟数据
                </a-button>
              </a-space>
            </div>
            
            <a-row :gutter="24" style="margin-top: 16px;">
              <a-col :span="12">
                <a-card title="原始数据" size="small">
                  <a-textarea
                    v-model="alertRawData"
                    placeholder="粘贴后端返回的原始JSON数据"
                    :rows="10"
                    style="font-family: monospace; font-size: 12px;"
                  />
                  <div style="margin-top: 8px;">
                    <a-button size="small" @click="formatAlertJson">格式化JSON</a-button>
                    <a-button size="small" @click="clearAlertData" style="margin-left: 8px;">清空</a-button>
                  </div>
                </a-card>
              </a-col>
              
              <a-col :span="12">
                <a-card title="适配后数据" size="small">
                  <div class="adapted-data">
                    <pre v-if="alertAdaptedData">{{ JSON.stringify(alertAdaptedData, null, 2) }}</pre>
                    <a-empty v-else description="暂无适配数据" size="small" />
                  </div>
                  <div style="margin-top: 8px;">
                    <a-tag v-if="alertAdaptedData" color="green">
                      适配成功：{{ alertAdaptedData.length }} 条记录
                    </a-tag>
                  </div>
                </a-card>
              </a-col>
            </a-row>
            
            <div v-if="alertAdapterResult" class="adapter-result">
              <h4>适配结果分析</h4>
              <a-descriptions :column="2" size="small">
                <a-descriptions-item label="原始格式">{{ alertAdapterResult.originalFormat }}</a-descriptions-item>
                <a-descriptions-item label="适配状态">
                  <a-tag :color="alertAdapterResult.success ? 'green' : 'red'">
                    {{ alertAdapterResult.success ? '成功' : '失败' }}
                  </a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="记录数量">{{ alertAdapterResult.recordCount }}</a-descriptions-item>
                <a-descriptions-item label="字段映射">{{ alertAdapterResult.fieldMappings }}</a-descriptions-item>
              </a-descriptions>
              
              <div v-if="alertAdapterResult.issues.length > 0" class="adapter-issues">
                <h5>发现的问题：</h5>
                <ul>
                  <li v-for="issue in alertAdapterResult.issues" :key="issue">{{ issue }}</li>
                </ul>
              </div>
            </div>
          </div>
        </a-tab-pane>

        <!-- 事故记录适配测试 -->
        <a-tab-pane key="records" title="事故记录适配">
          <div class="adapter-test-section">
            <h3>事故记录数据适配测试</h3>
            
            <div class="test-controls">
              <a-space>
                <a-button @click="testRecordAdapter" :loading="recordTesting" type="primary">
                  测试记录适配器
                </a-button>
                <a-button @click="testRecordApi" :loading="recordApiTesting">
                  测试实际API
                </a-button>
                <a-button @click="generateMockRecordData">
                  生成模拟数据
                </a-button>
              </a-space>
            </div>
            
            <a-row :gutter="24" style="margin-top: 16px;">
              <a-col :span="12">
                <a-card title="原始数据" size="small">
                  <a-textarea
                    v-model="recordRawData"
                    placeholder="粘贴后端返回的原始JSON数据"
                    :rows="10"
                    style="font-family: monospace; font-size: 12px;"
                  />
                  <div style="margin-top: 8px;">
                    <a-button size="small" @click="formatRecordJson">格式化JSON</a-button>
                    <a-button size="small" @click="clearRecordData" style="margin-left: 8px;">清空</a-button>
                  </div>
                </a-card>
              </a-col>
              
              <a-col :span="12">
                <a-card title="适配后数据" size="small">
                  <div class="adapted-data">
                    <pre v-if="recordAdaptedData">{{ JSON.stringify(recordAdaptedData, null, 2) }}</pre>
                    <a-empty v-else description="暂无适配数据" size="small" />
                  </div>
                  <div style="margin-top: 8px;">
                    <a-tag v-if="recordAdaptedData" color="green">
                      适配成功：{{ recordAdaptedData.length }} 条记录
                    </a-tag>
                  </div>
                </a-card>
              </a-col>
            </a-row>
            
            <div v-if="recordAdapterResult" class="adapter-result">
              <h4>适配结果分析</h4>
              <a-descriptions :column="2" size="small">
                <a-descriptions-item label="原始格式">{{ recordAdapterResult.originalFormat }}</a-descriptions-item>
                <a-descriptions-item label="适配状态">
                  <a-tag :color="recordAdapterResult.success ? 'green' : 'red'">
                    {{ recordAdapterResult.success ? '成功' : '失败' }}
                  </a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="记录数量">{{ recordAdapterResult.recordCount }}</a-descriptions-item>
                <a-descriptions-item label="字段映射">{{ recordAdapterResult.fieldMappings }}</a-descriptions-item>
              </a-descriptions>
              
              <div v-if="recordAdapterResult.issues.length > 0" class="adapter-issues">
                <h5>发现的问题：</h5>
                <ul>
                  <li v-for="issue in recordAdapterResult.issues" :key="issue">{{ issue }}</li>
                </ul>
              </div>
            </div>
          </div>
        </a-tab-pane>

        <!-- 适配器代码 -->
        <a-tab-pane key="code" title="适配器代码">
          <div class="adapter-code-section">
            <h3>当前使用的数据适配器代码</h3>
            
            <a-collapse>
              <a-collapse-item header="实时警报适配器" key="alert-adapter">
                <pre class="adapter-code">{{ alertAdapterCode }}</pre>
              </a-collapse-item>
              
              <a-collapse-item header="事故记录适配器" key="record-adapter">
                <pre class="adapter-code">{{ recordAdapterCode }}</pre>
              </a-collapse-item>
            </a-collapse>
            
            <div style="margin-top: 16px;">
              <a-button @click="copyAdapterCode">复制所有适配器代码</a-button>
              <a-button @click="exportAdapterReport" style="margin-left: 8px;">导出适配报告</a-button>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Message } from '@arco-design/web-vue'
import axios from 'axios'

const activeTab = ref('alerts')

// 实时警报相关
const alertTesting = ref(false)
const alertApiTesting = ref(false)
const alertRawData = ref('')
const alertAdaptedData = ref(null)
const alertAdapterResult = ref(null)

// 事故记录相关
const recordTesting = ref(false)
const recordApiTesting = ref(false)
const recordRawData = ref('')
const recordAdaptedData = ref(null)
const recordAdapterResult = ref(null)

// 数据适配函数（与事故检测页面保持一致）
const adaptAlertData = (backendData: any) => {
  if (backendData?.alerts) {
    return backendData.alerts.map((alert: any) => ({
      alert_id: alert.id || alert.alert_id,
      alert_type: alert.alarm_type || alert.alert_type || 'unknown',
      monitor_id: alert.monitor_id,
      monitor_name: alert.location || alert.monitor_name || `监控点${alert.monitor_id}`,
      severity: alert.severity || 'medium',
      confidence: alert.confidence_level || alert.confidence || 0,
      description: alert.description || '暂无描述',
      status: alert.status || 'pending',
      create_time: alert.create_time || alert.timestamp || new Date().toISOString(),
      location: alert.location_info || { x: 0, y: 0 }
    }))
  } else if (Array.isArray(backendData)) {
    return backendData.map((alert: any) => ({
      alert_id: alert.id || alert.alert_id,
      alert_type: alert.alarm_type || alert.alert_type || 'unknown',
      monitor_id: alert.monitor_id,
      monitor_name: alert.location || alert.monitor_name || `监控点${alert.monitor_id}`,
      severity: alert.severity || 'medium',
      confidence: alert.confidence_level || alert.confidence || 0,
      description: alert.description || '暂无描述',
      status: alert.status || 'pending',
      create_time: alert.create_time || alert.timestamp || new Date().toISOString(),
      location: alert.location_info || { x: 0, y: 0 }
    }))
  }
  return []
}

const adaptRecordData = (backendData: any) => {
  if (backendData?.records) {
    return backendData.records.map((record: any) => ({
      record_id: record.id || record.record_id,
      monitor_id: record.monitor_id,
      accident_type: record.accident_type || record.type || 'unknown',
      severity: record.severity || 'medium',
      location: record.location_info || record.location || { x: 0, y: 0 },
      description: record.description || '暂无描述',
      confidence: record.confidence_level || record.confidence || 0,
      status: record.status || 'pending',
      evidence_image: record.evidence_image || record.image_url,
      evidence_video: record.evidence_video || record.video_url,
      create_time: record.create_time || record.timestamp || new Date().toISOString(),
      resolve_time: record.resolve_time || record.resolved_at,
      resolved_by: record.resolved_by || record.resolver,
      notes: record.notes || record.remarks
    }))
  } else if (Array.isArray(backendData)) {
    return backendData.map((record: any) => ({
      record_id: record.id || record.record_id,
      monitor_id: record.monitor_id,
      accident_type: record.accident_type || record.type || 'unknown',
      severity: record.severity || 'medium',
      location: record.location_info || record.location || { x: 0, y: 0 },
      description: record.description || '暂无描述',
      confidence: record.confidence_level || record.confidence || 0,
      status: record.status || 'pending',
      evidence_image: record.evidence_image || record.image_url,
      evidence_video: record.evidence_video || record.video_url,
      create_time: record.create_time || record.timestamp || new Date().toISOString(),
      resolve_time: record.resolve_time || record.resolved_at,
      resolved_by: record.resolved_by || record.resolver,
      notes: record.notes || record.remarks
    }))
  }
  return []
}

// 适配器代码字符串
const alertAdapterCode = `const adaptAlertData = (backendData) => {
  if (backendData?.alerts) {
    // 后端返回 {alerts: [...]} 格式
    return backendData.alerts.map(alert => ({
      alert_id: alert.id || alert.alert_id,
      alert_type: alert.alarm_type || alert.alert_type || 'unknown',
      monitor_id: alert.monitor_id,
      monitor_name: alert.location || alert.monitor_name || \`监控点\${alert.monitor_id}\`,
      severity: alert.severity || 'medium',
      confidence: alert.confidence_level || alert.confidence || 0,
      description: alert.description || '暂无描述',
      status: alert.status || 'pending',
      create_time: alert.create_time || alert.timestamp || new Date().toISOString(),
      location: alert.location_info || { x: 0, y: 0 }
    }))
  } else if (Array.isArray(backendData)) {
    // 后端直接返回数组格式
    return backendData.map(alert => ({ /* 同上映射 */ }))
  }
  return []
}`

const recordAdapterCode = `const adaptRecordData = (backendData) => {
  if (backendData?.records) {
    // 后端返回 {records: [...]} 格式
    return backendData.records.map(record => ({
      record_id: record.id || record.record_id,
      monitor_id: record.monitor_id,
      accident_type: record.accident_type || record.type || 'unknown',
      severity: record.severity || 'medium',
      location: record.location_info || record.location || { x: 0, y: 0 },
      description: record.description || '暂无描述',
      confidence: record.confidence_level || record.confidence || 0,
      status: record.status || 'pending',
      // ... 其他字段映射
    }))
  } else if (Array.isArray(backendData)) {
    // 后端直接返回数组格式
    return backendData.map(record => ({ /* 同上映射 */ }))
  }
  return []
}`

// 测试警报适配器
const testAlertAdapter = () => {
  alertTesting.value = true

  try {
    if (!alertRawData.value.trim()) {
      Message.warning('请先输入原始数据')
      return
    }

    const rawData = JSON.parse(alertRawData.value)
    const adaptedData = adaptAlertData(rawData)

    alertAdaptedData.value = adaptedData

    // 分析适配结果
    const originalFormat = rawData?.alerts ? 'Object with alerts array' :
                          Array.isArray(rawData) ? 'Direct array' : 'Unknown format'

    const issues = []
    if (adaptedData.length === 0 && rawData) {
      issues.push('适配后数据为空，可能是格式不匹配')
    }

    alertAdapterResult.value = {
      originalFormat,
      success: adaptedData.length > 0 || !rawData,
      recordCount: adaptedData.length,
      fieldMappings: adaptedData.length > 0 ? Object.keys(adaptedData[0]).length : 0,
      issues
    }

    Message.success(`警报数据适配完成，共 ${adaptedData.length} 条记录`)

  } catch (error) {
    Message.error('JSON解析失败: ' + error.message)
  } finally {
    alertTesting.value = false
  }
}

// 测试实际警报API
const testAlertApi = async () => {
  alertApiTesting.value = true

  try {
    const response = await axios.get('/api/v1/accident/alerts/realtime', {
      params: { limit: 5 },
      timeout: 5000
    })

    alertRawData.value = JSON.stringify(response.data, null, 2)

    // 自动测试适配器
    setTimeout(() => {
      testAlertAdapter()
    }, 100)

    Message.success('API测试完成，已自动填入原始数据')

  } catch (error) {
    Message.error('API测试失败: ' + error.message)
  } finally {
    alertApiTesting.value = false
  }
}

// 生成模拟警报数据
const generateMockAlertData = () => {
  const mockData = {
    success: true,
    data: {
      alerts: [
        {
          id: 'alert_001',
          alarm_type: 'collision',
          monitor_id: 1,
          location: '监控点A',
          severity: 'high',
          confidence_level: 0.95,
          description: '检测到车辆碰撞',
          status: 'pending',
          timestamp: '2024-12-28 10:30:00'
        },
        {
          id: 'alert_002',
          alarm_type: 'congestion',
          monitor_id: 2,
          location: '监控点B',
          severity: 'medium',
          confidence_level: 0.87,
          description: '交通拥堵',
          status: 'confirmed',
          timestamp: '2024-12-28 10:25:00'
        }
      ],
      total: 2
    }
  }

  alertRawData.value = JSON.stringify(mockData, null, 2)
  Message.success('已生成模拟警报数据')
}

// 测试记录适配器
const testRecordAdapter = () => {
  recordTesting.value = true

  try {
    if (!recordRawData.value.trim()) {
      Message.warning('请先输入原始数据')
      return
    }

    const rawData = JSON.parse(recordRawData.value)
    const adaptedData = adaptRecordData(rawData)

    recordAdaptedData.value = adaptedData

    // 分析适配结果
    const originalFormat = rawData?.records ? 'Object with records array' :
                          Array.isArray(rawData) ? 'Direct array' : 'Unknown format'

    const issues = []
    if (adaptedData.length === 0 && rawData) {
      issues.push('适配后数据为空，可能是格式不匹配')
    }

    recordAdapterResult.value = {
      originalFormat,
      success: adaptedData.length > 0 || !rawData,
      recordCount: adaptedData.length,
      fieldMappings: adaptedData.length > 0 ? Object.keys(adaptedData[0]).length : 0,
      issues
    }

    Message.success(`记录数据适配完成，共 ${adaptedData.length} 条记录`)

  } catch (error) {
    Message.error('JSON解析失败: ' + error.message)
  } finally {
    recordTesting.value = false
  }
}

// 测试实际记录API
const testRecordApi = async () => {
  recordApiTesting.value = true

  try {
    const response = await axios.get('/api/v1/accident/records', {
      params: { page: 1, page_size: 5 },
      timeout: 5000
    })

    recordRawData.value = JSON.stringify(response.data, null, 2)

    // 自动测试适配器
    setTimeout(() => {
      testRecordAdapter()
    }, 100)

    Message.success('API测试完成，已自动填入原始数据')

  } catch (error) {
    Message.error('API测试失败: ' + error.message)
  } finally {
    recordApiTesting.value = false
  }
}

// 生成模拟记录数据
const generateMockRecordData = () => {
  const mockData = {
    success: true,
    data: {
      records: [
        {
          id: 'record_001',
          monitor_id: 1,
          accident_type: 'collision',
          severity: 'high',
          description: '两车追尾事故',
          confidence_level: 0.92,
          status: 'confirmed',
          timestamp: '2024-12-28 09:15:00'
        }
      ],
      total: 1,
      page: 1,
      page_size: 5
    }
  }

  recordRawData.value = JSON.stringify(mockData, null, 2)
  Message.success('已生成模拟记录数据')
}

// 格式化JSON
const formatAlertJson = () => {
  try {
    const parsed = JSON.parse(alertRawData.value)
    alertRawData.value = JSON.stringify(parsed, null, 2)
    Message.success('JSON格式化完成')
  } catch (error) {
    Message.error('JSON格式错误')
  }
}

const formatRecordJson = () => {
  try {
    const parsed = JSON.parse(recordRawData.value)
    recordRawData.value = JSON.stringify(parsed, null, 2)
    Message.success('JSON格式化完成')
  } catch (error) {
    Message.error('JSON格式错误')
  }
}

// 清空数据
const clearAlertData = () => {
  alertRawData.value = ''
  alertAdaptedData.value = null
  alertAdapterResult.value = null
}

const clearRecordData = () => {
  recordRawData.value = ''
  recordAdaptedData.value = null
  recordAdapterResult.value = null
}

// 复制适配器代码
const copyAdapterCode = () => {
  const code = `${alertAdapterCode}\n\n${recordAdapterCode}`
  navigator.clipboard.writeText(code).then(() => {
    Message.success('适配器代码已复制到剪贴板')
  }).catch(() => {
    Message.error('复制失败')
  })
}

// 导出适配报告
const exportAdapterReport = () => {
  const report = {
    timestamp: new Date().toISOString(),
    alert_adapter: {
      result: alertAdapterResult.value,
      sample_data: alertAdaptedData.value
    },
    record_adapter: {
      result: recordAdapterResult.value,
      sample_data: recordAdaptedData.value
    },
    adapter_code: {
      alert_adapter: alertAdapterCode,
      record_adapter: recordAdapterCode
    }
  }

  const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `data-adapter-report-${Date.now()}.json`
  link.click()
  URL.revokeObjectURL(url)

  Message.success('适配报告已导出')
}
</script>

<style scoped>
.data-adapter-test {
  padding: 20px;
}

.adapter-test-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.test-controls {
  margin-bottom: 16px;
}

.adapted-data {
  max-height: 300px;
  overflow-y: auto;
  background: #f7f8fa;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #e5e6eb;
}

.adapted-data pre {
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: 11px;
  white-space: pre-wrap;
  word-break: break-all;
}

.adapter-result {
  margin-top: 24px;
  padding: 16px;
  background: #f0f8ff;
  border-radius: 8px;
  border: 1px solid #91d5ff;
}

.adapter-result h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
}

.adapter-issues {
  margin-top: 12px;
}

.adapter-issues h5 {
  margin: 0 0 8px 0;
  font-size: 12px;
  font-weight: 600;
  color: #f5222d;
}

.adapter-issues ul {
  margin: 0;
  padding-left: 20px;
}

.adapter-issues li {
  margin: 4px 0;
  font-size: 12px;
  color: #f5222d;
}

.adapter-code-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.adapter-code {
  background: #f7f8fa;
  padding: 12px;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  margin: 0;
  border: 1px solid #e5e6eb;
  overflow-x: auto;
}
</style>
