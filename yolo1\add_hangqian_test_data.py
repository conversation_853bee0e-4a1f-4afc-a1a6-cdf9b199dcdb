#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
添加杭千高速完整测试数据
"""

import pymysql
import random
import json
from datetime import datetime, timedelta
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(override=True, dotenv_path='config/end-back.env')

def get_db_config():
    """获取数据库配置"""
    return {
        'host': '127.0.0.1',
        'port': 3306,
        'user': 'root',
        'password': '123456',
        'database': 'yolo',
        'charset': 'utf8mb4',
        'autocommit': True,
        'cursorclass': pymysql.cursors.DictCursor
    }

def add_comprehensive_alarm_data():
    """添加全面的警报数据"""
    print("🚨 添加杭千高速警报数据...")
    
    config = get_db_config()
    connection = pymysql.connect(**config)
    
    try:
        with connection.cursor() as cursor:
            # 获取所有监控点
            cursor.execute("SELECT id, location, highway_section, threshold FROM monitor")
            monitors = cursor.fetchall()
            
            # 清除今日警报数据，重新添加
            cursor.execute("DELETE FROM alarm WHERE DATE(create_time) = CURDATE()")
            
            # 为今天添加50条警报数据
            alarm_types = ['vehicle_count', 'accident', 'congestion', 'speeding', 'illegal_parking']
            severities = ['low', 'medium', 'high']
            statuses = ['pending', 'processing', 'resolved']
            
            for i in range(50):
                monitor = random.choice(monitors)
                alarm_type = random.choice(alarm_types)
                severity = random.choice(severities)
                status = random.choice(statuses)
                
                # 根据类型生成不同的数据
                if alarm_type == 'vehicle_count':
                    vehicle_count = random.randint(monitor['threshold'] + 1, monitor['threshold'] + 15)
                    description = f"车流量超标：检测到{vehicle_count}辆车，超过阈值{monitor['threshold']}"
                    detection_details = json.dumps({
                        "cars": vehicle_count - random.randint(3, 8),
                        "trucks": random.randint(2, 5),
                        "buses": random.randint(1, 3),
                        "confidence_avg": round(random.uniform(0.75, 0.95), 2),
                        "total_vehicles": vehicle_count
                    })
                else:
                    vehicle_count = random.randint(0, 8)
                    description = f"检测到{alarm_type}事件 - {monitor['location']}"
                    detection_details = json.dumps({
                        "confidence": round(random.uniform(0.7, 0.95), 2),
                        "type": alarm_type,
                        "location": monitor['location']
                    })
                
                confidence_level = round(random.uniform(0.7, 0.95), 2)
                
                # 今天的随机时间
                alarm_time = datetime.now().replace(
                    hour=random.randint(0, 23),
                    minute=random.randint(0, 59),
                    second=random.randint(0, 59)
                )
                
                cursor.execute("""
                    INSERT INTO alarm (monitor_id, location, highway_section, alarm_type, 
                                     description, vehicle_count, detection_details, confidence_level, 
                                     threshold, severity, status, create_time)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    monitor['id'], monitor['location'], monitor['highway_section'],
                    alarm_type, description, vehicle_count, detection_details,
                    confidence_level, monitor['threshold'], severity, status, alarm_time
                ))
            
            print(f"   ✅ 成功添加50条今日警报数据")
            return True
            
    except Exception as e:
        print(f"   ❌ 添加警报数据失败: {e}")
        return False
    finally:
        connection.close()

def add_comprehensive_traffic_data():
    """添加全面的交通统计数据"""
    print("🚗 添加杭千高速交通统计数据...")
    
    config = get_db_config()
    connection = pymysql.connect(**config)
    
    try:
        with connection.cursor() as cursor:
            # 获取所有监控点
            cursor.execute("SELECT id FROM monitor")
            monitor_ids = [row['id'] for row in cursor.fetchall()]
            
            # 清除今天的数据
            cursor.execute("DELETE FROM traffic_statistics WHERE stat_date = CURDATE()")
            
            today = datetime.now().date()
            congestion_levels = ['normal', 'light', 'congested', 'heavy']
            
            # 为每个监控点添加24小时的数据
            for monitor_id in monitor_ids:
                for hour in range(24):
                    # 根据时段模拟真实的交通流量
                    if 6 <= hour <= 9:  # 早高峰
                        base_count = random.randint(300, 500)
                        avg_speed = random.randint(45, 75)
                        congestion = random.choice(['congested', 'heavy'])
                    elif 17 <= hour <= 20:  # 晚高峰
                        base_count = random.randint(280, 450)
                        avg_speed = random.randint(50, 80)
                        congestion = random.choice(['light', 'congested'])
                    elif 22 <= hour or hour <= 5:  # 夜间
                        base_count = random.randint(30, 100)
                        avg_speed = random.randint(80, 120)
                        congestion = 'normal'
                    else:  # 平常时段
                        base_count = random.randint(150, 280)
                        avg_speed = random.randint(70, 100)
                        congestion = random.choice(['normal', 'light'])
                    
                    # 计算各类车辆数量
                    car_count = int(base_count * random.uniform(0.75, 0.85))
                    truck_count = int(base_count * random.uniform(0.10, 0.20))
                    bus_count = int(base_count * random.uniform(0.02, 0.08))
                    
                    # 确保总数一致
                    total_calculated = car_count + truck_count + bus_count
                    if total_calculated != base_count:
                        car_count += (base_count - total_calculated)
                    
                    max_speed = avg_speed + random.randint(15, 35)
                    
                    cursor.execute("""
                        INSERT INTO traffic_statistics (
                            monitor_id, stat_date, stat_hour, vehicle_count, 
                            car_count, truck_count, bus_count, avg_speed, 
                            max_speed, congestion_level, create_time
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        monitor_id, today, hour, base_count,
                        car_count, truck_count, bus_count, avg_speed,
                        max_speed, congestion, datetime.now()
                    ))
            
            print(f"   ✅ 成功添加{len(monitor_ids) * 24}条交通统计数据")
            return True
            
    except Exception as e:
        print(f"   ❌ 添加交通统计数据失败: {e}")
        return False
    finally:
        connection.close()

def add_comprehensive_accident_data():
    """添加全面的事故数据"""
    print("🚨 添加杭千高速事故数据...")
    
    config = get_db_config()
    connection = pymysql.connect(**config)
    
    try:
        with connection.cursor() as cursor:
            # 获取监控点
            cursor.execute("SELECT id FROM monitor")
            monitor_ids = [row['id'] for row in cursor.fetchall()]
            
            # 清除最近7天的事故数据
            cursor.execute("DELETE FROM accident_record WHERE create_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)")
            
            accident_types = ['collision', 'rollover', 'breakdown', 'illegal_parking', 'speeding', 'fire']
            severities = ['low', 'medium', 'high']
            statuses = ['detected', 'processing', 'resolved']
            
            # 添加最近7天的事故数据
            for days_ago in range(7):
                date = datetime.now() - timedelta(days=days_ago)
                
                # 每天2-5个事故
                daily_accidents = random.randint(2, 5)
                
                for i in range(daily_accidents):
                    monitor_id = random.choice(monitor_ids)
                    accident_type = random.choice(accident_types)
                    severity = random.choice(severities)
                    status = random.choice(statuses)
                    
                    accident_time = date.replace(
                        hour=random.randint(0, 23),
                        minute=random.randint(0, 59),
                        second=random.randint(0, 59)
                    )
                    
                    record_id = f"ACC{accident_time.strftime('%Y%m%d%H%M%S')}{monitor_id:02d}{i+1:02d}"
                    confidence = round(random.uniform(0.75, 0.95), 2)
                    
                    # 涉及车辆信息
                    vehicle_types = ['car', 'truck', 'bus', 'motorcycle']
                    involved_count = random.randint(1, 3)
                    involved_vehicles = json.dumps({
                        "count": involved_count,
                        "types": random.sample(vehicle_types, min(involved_count, len(vehicle_types)))
                    })
                    
                    # 元数据
                    weather_conditions = ['clear', 'rainy', 'foggy', 'cloudy']
                    metadata = json.dumps({
                        "weather": random.choice(weather_conditions),
                        "visibility": random.choice(['good', 'poor', 'moderate']),
                        "road_condition": random.choice(['dry', 'wet', 'slippery'])
                    })
                    
                    cursor.execute("""
                        INSERT INTO accident_record (
                            record_id, monitor_id, accident_type, severity, 
                            description, confidence, involved_vehicles, status, 
                            detection_time, metadata, create_time
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        record_id, monitor_id, accident_type, severity,
                        f'杭千高速{accident_type}事故，严重程度：{severity}',
                        confidence, involved_vehicles, status,
                        accident_time, metadata, accident_time
                    ))
            
            print(f"   ✅ 成功添加事故数据")
            return True
            
    except Exception as e:
        print(f"   ❌ 添加事故数据失败: {e}")
        return False
    finally:
        connection.close()

def add_comprehensive_detection_tasks():
    """添加全面的检测任务数据"""
    print("🎯 添加杭千高速检测任务数据...")
    
    config = get_db_config()
    connection = pymysql.connect(**config)
    
    try:
        with connection.cursor() as cursor:
            # 获取监控点
            cursor.execute("SELECT id, location FROM monitor")
            monitors = cursor.fetchall()
            
            # 清除旧任务
            cursor.execute("DELETE FROM detection_task")
            
            task_types = ['video', 'image', 'stream', 'tracking', 'accident']
            task_statuses = ['running', 'completed', 'failed', 'pending']
            
            # 为每个监控点添加3-5个任务
            for monitor in monitors:
                task_count = random.randint(3, 5)
                
                for i in range(task_count):
                    task_type = random.choice(task_types)
                    status = random.choice(task_statuses)
                    
                    create_time = datetime.now() - timedelta(
                        days=random.randint(0, 5),
                        hours=random.randint(0, 23),
                        minutes=random.randint(0, 59)
                    )
                    
                    task_id = f"TASK{create_time.strftime('%Y%m%d%H%M%S')}{monitor['id']:02d}{i+1:02d}"
                    task_name = f"{monitor['location']}_{task_type}_任务{i+1}"
                    
                    # 根据状态设置时间和进度
                    if status == 'pending':
                        start_time = None
                        end_time = None
                        progress = 0
                        detection_count = 0
                    elif status == 'running':
                        start_time = create_time + timedelta(minutes=random.randint(1, 10))
                        end_time = None
                        progress = random.randint(10, 90)
                        detection_count = random.randint(50, 200)
                    elif status == 'completed':
                        start_time = create_time + timedelta(minutes=random.randint(1, 10))
                        end_time = start_time + timedelta(minutes=random.randint(10, 60))
                        progress = 100
                        detection_count = random.randint(100, 800)
                    else:  # failed
                        start_time = create_time + timedelta(minutes=random.randint(1, 10))
                        end_time = start_time + timedelta(minutes=random.randint(5, 30))
                        progress = random.randint(5, 50)
                        detection_count = random.randint(0, 100)
                    
                    cursor.execute("""
                        INSERT INTO detection_task (
                            task_id, task_name, task_type, monitor_id, status, 
                            progress, start_time, end_time, detection_count, 
                            user_id, create_time
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        task_id, task_name, task_type, monitor['id'], status,
                        progress, start_time, end_time, detection_count,
                        1, create_time
                    ))
            
            print(f"   ✅ 成功添加检测任务数据")
            return True
            
    except Exception as e:
        print(f"   ❌ 添加检测任务数据失败: {e}")
        return False
    finally:
        connection.close()

def add_tracking_targets_data():
    """添加追踪目标数据"""
    print("🎯 添加追踪目标数据...")

    config = get_db_config()
    connection = pymysql.connect(**config)

    try:
        with connection.cursor() as cursor:
            # 获取监控点
            cursor.execute("SELECT id FROM monitor")
            monitor_ids = [row['id'] for row in cursor.fetchall()]

            # 清除旧数据
            cursor.execute("DELETE FROM tracking_target")

            target_types = ['car', 'truck', 'bus', 'motorcycle']
            statuses = ['active', 'lost', 'completed']

            # 为每个监控点添加追踪目标
            for monitor_id in monitor_ids:
                # 每个监控点8-15个目标
                target_count = random.randint(8, 15)

                for i in range(target_count):
                    target_type = random.choice(target_types)
                    status = random.choice(statuses)

                    # 活跃目标更多
                    if random.random() < 0.6:
                        status = 'active'

                    target_id = f"T{monitor_id:02d}{i+1:03d}"
                    x_position = random.randint(100, 1800)
                    y_position = random.randint(100, 900)
                    speed = random.randint(30, 120)
                    confidence = round(random.uniform(0.7, 0.95), 2)

                    create_time = datetime.now() - timedelta(
                        hours=random.randint(0, 6),
                        minutes=random.randint(0, 59)
                    )

                    cursor.execute("""
                        INSERT INTO tracking_target (
                            target_id, monitor_id, target_type, x_position, y_position,
                            speed, confidence, status, create_time
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        target_id, monitor_id, target_type, x_position, y_position,
                        speed, confidence, status, create_time
                    ))

            print(f"   ✅ 成功添加追踪目标数据")
            return True

    except Exception as e:
        print(f"   ❌ 添加追踪目标数据失败: {e}")
        return False
    finally:
        connection.close()

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 添加杭千高速完整测试数据")
    print("=" * 60)

    success_count = 0
    total_count = 5
    
    # 1. 添加警报数据
    if add_comprehensive_alarm_data():
        success_count += 1
    
    # 2. 添加交通统计数据
    if add_comprehensive_traffic_data():
        success_count += 1
    
    # 3. 添加事故数据
    if add_comprehensive_accident_data():
        success_count += 1
    
    # 4. 添加检测任务数据
    if add_comprehensive_detection_tasks():
        success_count += 1

    # 5. 添加追踪目标数据
    if add_tracking_targets_data():
        success_count += 1

    print("\n" + "=" * 60)
    print(f"📋 数据添加完成: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("🎉 杭千高速测试数据添加成功!")
        print("💡 数据包括:")
        print("   - 50条今日警报数据")
        print("   - 完整的24小时交通统计")
        print("   - 最近7天的事故记录")
        print("   - 丰富的检测任务数据")
        print("   - 活跃的追踪目标数据")
        print("🔄 现在重新启动后端: python new_backend.py")
    else:
        print("⚠️ 部分数据添加失败，请检查错误信息")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
