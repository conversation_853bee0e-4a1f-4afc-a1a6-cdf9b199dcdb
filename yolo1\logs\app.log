2025-06-21 16:37:07,916 - app_enhanced - INFO - 启动基于Yolov8与ByteTrack的高速公路智慧监控平台后端服务
2025-06-21 16:37:07,917 - app_enhanced - INFO - 服务地址: http://127.0.0.1:5500
2025-06-21 16:37:07,917 - app_enhanced - INFO - 调试模式: False
2025-06-21 16:37:07,936 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5500
2025-06-21 16:37:07,936 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-28 00:41:24,909 - app_enhanced - INFO - 启动基于Yolov8与ByteTrack的高速公路智慧监控平台后端服务
2025-06-28 00:41:24,919 - app_enhanced - INFO - 服务地址: http://127.0.0.1:5500
2025-06-28 00:41:24,919 - app_enhanced - INFO - 调试模式: False
2025-06-28 00:41:24,934 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5500
2025-06-28 00:41:24,934 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-28 00:44:13,207 - app_enhanced - INFO - 启动基于Yolov8与ByteTrack的高速公路智慧监控平台后端服务
2025-06-28 00:44:13,207 - app_enhanced - INFO - 服务地址: http://127.0.0.1:5500
2025-06-28 00:44:13,207 - app_enhanced - INFO - 调试模式: False
2025-06-28 00:44:13,218 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5500
2025-06-28 00:44:13,218 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-28 00:49:27,556 - app_enhanced - INFO - 启动基于Yolov8与ByteTrack的高速公路智慧监控平台后端服务
2025-06-28 00:49:27,556 - app_enhanced - INFO - 服务地址: http://127.0.0.1:5500
2025-06-28 00:49:27,556 - app_enhanced - INFO - 调试模式: False
2025-06-28 00:49:27,566 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5500
2025-06-28 00:49:27,566 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-28 00:51:29,693 - app_enhanced - INFO - 启动基于Yolov8与ByteTrack的高速公路智慧监控平台后端服务
2025-06-28 00:51:29,693 - app_enhanced - INFO - 服务地址: http://127.0.0.1:5500
2025-06-28 00:51:29,693 - app_enhanced - INFO - 调试模式: False
2025-06-28 00:51:29,711 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5500
2025-06-28 00:51:29,712 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-28 00:55:20,994 - app_enhanced - INFO - 启动基于Yolov8与ByteTrack的高速公路智慧监控平台后端服务
2025-06-28 00:55:20,994 - app_enhanced - INFO - 服务地址: http://127.0.0.1:5500
2025-06-28 00:55:20,997 - app_enhanced - INFO - 调试模式: False
2025-06-28 00:55:21,011 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5500
2025-06-28 00:55:21,011 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-28 00:56:08,176 - app_enhanced - INFO - 启动基于Yolov8与ByteTrack的高速公路智慧监控平台后端服务
2025-06-28 00:56:08,176 - app_enhanced - INFO - 服务地址: http://127.0.0.1:5500
2025-06-28 00:56:08,176 - app_enhanced - INFO - 调试模式: False
2025-06-28 00:56:08,188 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5500
2025-06-28 00:56:08,188 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-28 00:59:15,562 - app_enhanced - INFO - 启动基于Yolov8与ByteTrack的高速公路智慧监控平台后端服务
2025-06-28 00:59:15,562 - app_enhanced - INFO - 服务地址: http://127.0.0.1:5500
2025-06-28 00:59:15,562 - app_enhanced - INFO - 调试模式: False
2025-06-28 00:59:15,571 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5500
2025-06-28 00:59:15,571 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-28 01:03:03,927 - app_enhanced - INFO - 启动基于Yolov8与ByteTrack的高速公路智慧监控平台后端服务
2025-06-28 01:03:03,927 - app_enhanced - INFO - 服务地址: http://127.0.0.1:5500
2025-06-28 01:03:03,927 - app_enhanced - INFO - 调试模式: False
2025-06-28 01:03:03,937 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5500
2025-06-28 01:03:03,937 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-28 01:03:31,414 - app_enhanced - INFO - 启动基于Yolov8与ByteTrack的高速公路智慧监控平台后端服务
2025-06-28 01:03:31,414 - app_enhanced - INFO - 服务地址: http://127.0.0.1:5500
2025-06-28 01:03:31,414 - app_enhanced - INFO - 调试模式: False
2025-06-28 01:03:31,425 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5500
2025-06-28 01:03:31,425 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-28 01:08:38,500 - app_enhanced - INFO - 启动基于Yolov8与ByteTrack的高速公路智慧监控平台后端服务
2025-06-28 01:08:38,500 - app_enhanced - INFO - 服务地址: http://127.0.0.1:5500
2025-06-28 01:08:38,501 - app_enhanced - INFO - 调试模式: False
2025-06-28 01:08:38,511 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5500
2025-06-28 01:08:38,512 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-28 01:15:25,039 - app_enhanced - INFO - 启动基于Yolov8与ByteTrack的高速公路智慧监控平台后端服务
2025-06-28 01:15:25,041 - app_enhanced - INFO - 服务地址: http://127.0.0.1:5500
2025-06-28 01:15:25,041 - app_enhanced - INFO - 调试模式: False
2025-06-28 01:15:25,054 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5500
2025-06-28 01:15:25,054 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-28 01:19:21,488 - app_enhanced - INFO - 启动基于Yolov8与ByteTrack的高速公路智慧监控平台后端服务
2025-06-28 01:19:21,489 - app_enhanced - INFO - 服务地址: http://127.0.0.1:5500
2025-06-28 01:19:21,489 - app_enhanced - INFO - 调试模式: False
2025-06-28 01:19:21,509 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5500
2025-06-28 01:19:21,509 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-28 01:24:14,684 - app_enhanced - INFO - 启动基于Yolov8与ByteTrack的高速公路智慧监控平台后端服务
2025-06-28 01:24:14,684 - app_enhanced - INFO - 服务地址: http://127.0.0.1:5500
2025-06-28 01:24:14,684 - app_enhanced - INFO - 调试模式: False
2025-06-28 01:24:14,700 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5500
2025-06-28 01:24:14,700 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-28 01:32:01,066 - app_enhanced - INFO - 启动基于Yolov8与ByteTrack的高速公路智慧监控平台后端服务
2025-06-28 01:32:01,066 - app_enhanced - INFO - 服务地址: http://127.0.0.1:5500
2025-06-28 01:32:01,066 - app_enhanced - INFO - 调试模式: False
2025-06-28 01:32:01,085 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5500
2025-06-28 01:32:01,085 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-28 01:32:13,098 - app_enhanced - INFO - GET http://127.0.0.1:5500/ - 127.0.0.1
2025-06-28 01:32:13,115 - app_enhanced - INFO - GET http://127.0.0.1:5500/ - 200
2025-06-28 01:32:13,180 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 01:32:13] "GET / HTTP/1.1" 200 -
2025-06-28 01:32:13,199 - app_enhanced - INFO - GET http://127.0.0.1:5500/api/v1/docs - 127.0.0.1
2025-06-28 01:32:13,202 - app_enhanced - INFO - GET http://127.0.0.1:5500/api/v1/docs - 200
2025-06-28 01:32:13,203 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 01:32:13] "GET /api/v1/docs HTTP/1.1" 200 -
2025-06-28 01:32:13,211 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 01:32:13,245 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 01:32:13,246 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 01:32:13] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 01:35:42,933 - app_enhanced - INFO - GET http://127.0.0.1:5500/ - 127.0.0.1
2025-06-28 01:35:42,936 - app_enhanced - INFO - GET http://127.0.0.1:5500/ - 200
2025-06-28 01:35:42,937 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 01:35:42] "GET / HTTP/1.1" 200 -
2025-06-28 01:35:42,953 - app_enhanced - INFO - GET http://127.0.0.1:5500/api/v1/docs - 127.0.0.1
2025-06-28 01:35:42,955 - app_enhanced - INFO - GET http://127.0.0.1:5500/api/v1/docs - 200
2025-06-28 01:35:42,956 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 01:35:42] "GET /api/v1/docs HTTP/1.1" 200 -
2025-06-28 01:35:42,964 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 01:35:42,980 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 01:35:42,981 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 01:35:42] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 01:38:01,283 - app_enhanced - INFO - 启动基于Yolov8与ByteTrack的高速公路智慧监控平台后端服务
2025-06-28 01:38:01,284 - app_enhanced - INFO - 服务地址: http://127.0.0.1:5500
2025-06-28 01:38:01,284 - app_enhanced - INFO - 调试模式: False
2025-06-28 01:38:01,295 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5500
2025-06-28 01:38:01,295 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-28 01:38:09,423 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 01:38:09,443 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 01:38:09,444 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 01:38:09] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 01:38:09,457 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 01:38:09,460 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 01:38:09,461 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 01:38:09] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 01:38:09,478 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 01:38:09,480 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 01:38:09,481 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 01:38:09] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 01:38:09,495 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 01:38:09,498 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 01:38:09,499 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 01:38:09] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 01:41:31,046 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 01:41:31,055 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 01:41:31,067 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 01:41:31] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 01:41:31,867 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 01:41:31,869 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 01:41:31,870 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 01:41:31] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 01:41:34,894 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 01:41:34,907 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 01:41:34,907 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 01:41:34] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 01:41:35,450 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 01:41:35,453 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 01:41:35,453 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 01:41:35] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 01:41:40,290 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 01:41:40,315 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 01:41:40,317 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 01:41:40] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 01:42:20,384 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 01:42:20,388 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 01:42:20,388 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 01:42:20] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 01:42:24,571 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 01:42:24,573 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 01:42:24,574 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 01:42:24] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 01:42:29,484 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 01:42:29,509 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 01:42:29,510 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 01:42:29] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 01:42:34,785 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 01:42:34,809 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 01:42:34,809 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 01:42:34] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 01:45:33,460 - app_enhanced - INFO - GET http://127.0.0.1:5500/api-test%20◦ - 127.0.0.1
2025-06-28 01:45:33,469 - app_enhanced - INFO - GET http://127.0.0.1:5500/api-test%20◦ - 200
2025-06-28 01:45:33,469 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 01:45:33] "GET /api-test%20◦ HTTP/1.1" 200 -
2025-06-28 01:45:45,310 - app_enhanced - INFO - GET http://127.0.0.1:5500/api-test%20◦ - 127.0.0.1
2025-06-28 01:45:45,311 - app_enhanced - INFO - GET http://127.0.0.1:5500/api-test%20◦ - 200
2025-06-28 01:45:45,312 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 01:45:45] "GET /api-test%20◦ HTTP/1.1" 200 -
2025-06-28 01:46:06,502 - app_enhanced - INFO - GET http://127.0.0.1:5500/api-test - 127.0.0.1
2025-06-28 01:46:06,502 - app_enhanced - INFO - GET http://127.0.0.1:5500/api-test - 200
2025-06-28 01:46:06,502 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 01:46:06] "GET /api-test HTTP/1.1" 200 -
2025-06-28 01:46:14,825 - app_enhanced - INFO - GET http://127.0.0.1:5500/api-test - 127.0.0.1
2025-06-28 01:46:14,826 - app_enhanced - INFO - GET http://127.0.0.1:5500/api-test - 200
2025-06-28 01:46:14,826 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 01:46:14] "GET /api-test HTTP/1.1" 200 -
2025-06-28 01:47:02,354 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 01:47:02,371 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 01:47:02,375 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 01:47:02] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 01:47:03,093 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 01:47:03,109 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 01:47:03,109 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 01:47:03] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 01:47:03,349 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 01:47:03,353 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 01:47:03,353 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 01:47:03] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 01:47:03,533 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 01:47:03,536 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 01:47:03,536 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 01:47:03] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 01:47:03,709 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 01:47:03,712 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 01:47:03,713 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 01:47:03] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 01:47:03,884 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 01:47:03,901 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 01:47:03,901 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 01:47:03] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 01:47:09,990 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 01:47:09,993 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 01:47:09,994 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 01:47:09] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 01:47:35,349 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 01:47:35,354 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 01:47:35,355 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 01:47:35] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 01:47:40,190 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 01:47:40,192 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 01:47:40,192 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 01:47:40] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 01:47:51,686 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 01:47:51,689 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 01:47:51,690 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 01:47:51] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 01:48:00,382 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 01:48:00,397 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 01:48:00,397 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 01:48:00] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 01:48:04,787 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 01:48:04,790 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 01:48:04,791 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 01:48:04] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 01:56:14,957 - app_enhanced - INFO - GET http://127.0.0.1:5500/api-test - 127.0.0.1
2025-06-28 01:56:14,961 - app_enhanced - INFO - GET http://127.0.0.1:5500/api-test - 200
2025-06-28 01:56:14,961 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 01:56:14] "GET /api-test HTTP/1.1" 200 -
2025-06-28 01:56:34,946 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 01:56:34,960 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 01:56:34,961 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 01:56:34] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 01:56:37,012 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 01:56:37,015 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 01:56:37,016 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 01:56:37] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 01:56:38,073 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 01:56:38,078 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 01:56:38,078 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 01:56:38] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 01:56:39,214 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 01:56:39,217 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 01:56:39,218 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 01:56:39] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 01:56:49,243 - app_enhanced - INFO - GET http://127.0.0.1:5500/api-test - 127.0.0.1
2025-06-28 01:56:49,244 - app_enhanced - INFO - GET http://127.0.0.1:5500/api-test - 200
2025-06-28 01:56:49,244 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 01:56:49] "GET /api-test HTTP/1.1" 200 -
2025-06-28 01:57:20,682 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 01:57:20,700 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 01:57:20,704 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 01:57:20] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 02:04:44,540 - app_enhanced - INFO - GET http://127.0.0.1:5500/api-path-diagnostic - 127.0.0.1
2025-06-28 02:04:44,546 - app_enhanced - INFO - GET http://127.0.0.1:5500/api-path-diagnostic - 200
2025-06-28 02:04:44,547 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:04:44] "GET /api-path-diagnostic HTTP/1.1" 200 -
2025-06-28 02:05:11,364 - app_enhanced - INFO - GET http://127.0.0.1:5500/api-path-diagnostic - 127.0.0.1
2025-06-28 02:05:11,364 - app_enhanced - INFO - GET http://127.0.0.1:5500/api-path-diagnostic - 200
2025-06-28 02:05:11,364 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:05:11] "GET /api-path-diagnostic HTTP/1.1" 200 -
2025-06-28 02:10:45,488 - app_enhanced - INFO - GET http://127.0.0.1:5500/ - 127.0.0.1
2025-06-28 02:10:45,492 - app_enhanced - INFO - GET http://127.0.0.1:5500/ - 200
2025-06-28 02:10:45,494 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:10:45] "GET / HTTP/1.1" 200 -
2025-06-28 02:10:45,502 - app_enhanced - INFO - GET http://127.0.0.1:5500/health - 127.0.0.1
2025-06-28 02:10:45,585 - app_enhanced - INFO - GET http://127.0.0.1:5500/health - 200
2025-06-28 02:10:45,585 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:10:45] "GET /health HTTP/1.1" 200 -
2025-06-28 02:10:45,618 - app_enhanced - INFO - GET http://127.0.0.1:5500/docs - 127.0.0.1
2025-06-28 02:10:45,619 - app_enhanced - INFO - GET http://127.0.0.1:5500/docs - 200
2025-06-28 02:10:45,620 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:10:45] "GET /docs HTTP/1.1" 200 -
2025-06-28 02:10:45,641 - app_enhanced - INFO - GET http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 02:10:45,643 - app_enhanced - INFO - GET http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 02:10:45,643 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:10:45] "GET /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 02:10:45,649 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 02:10:45,656 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 02:10:45,657 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:10:45] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 02:10:45,662 - app_enhanced - INFO - GET http://127.0.0.1:5500/api/v1/system/health-check - 127.0.0.1
2025-06-28 02:10:46,728 - app_enhanced - INFO - GET http://127.0.0.1:5500/api/v1/system/health-check - 200
2025-06-28 02:10:46,728 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:10:46] "GET /api/v1/system/health-check HTTP/1.1" 200 -
2025-06-28 02:10:46,744 - app_enhanced - INFO - GET http://127.0.0.1:5500/api/v1/system/info - 127.0.0.1
2025-06-28 02:10:46,747 - app_enhanced - INFO - GET http://127.0.0.1:5500/api/v1/system/info - 200
2025-06-28 02:10:46,748 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:10:46] "GET /api/v1/system/info HTTP/1.1" 200 -
2025-06-28 02:10:46,763 - app_enhanced - INFO - GET http://127.0.0.1:5500/api/v1/monitor/list - 127.0.0.1
2025-06-28 02:10:46,763 - app_enhanced - INFO - GET http://127.0.0.1:5500/api/v1/monitor/list - 200
2025-06-28 02:10:46,764 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:10:46] "GET /api/v1/monitor/list HTTP/1.1" 200 -
2025-06-28 02:10:46,769 - app_enhanced - INFO - GET http://127.0.0.1:5500/api/v1/analysis/statistics/overview - 127.0.0.1
2025-06-28 02:10:46,769 - app_enhanced - INFO - GET http://127.0.0.1:5500/api/v1/analysis/statistics/overview - 200
2025-06-28 02:10:46,769 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:10:46] "GET /api/v1/analysis/statistics/overview HTTP/1.1" 200 -
2025-06-28 02:11:21,981 - app_enhanced - INFO - GET http://127.0.0.1:5500/ - 127.0.0.1
2025-06-28 02:11:21,981 - app_enhanced - INFO - GET http://127.0.0.1:5500/ - 200
2025-06-28 02:11:21,981 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:11:21] "GET / HTTP/1.1" 200 -
2025-06-28 02:11:22,022 - app_enhanced - INFO - GET http://127.0.0.1:5500/health - 127.0.0.1
2025-06-28 02:11:22,055 - app_enhanced - INFO - GET http://127.0.0.1:5500/health - 200
2025-06-28 02:11:22,055 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:11:22] "GET /health HTTP/1.1" 200 -
2025-06-28 02:11:22,097 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 02:11:22,102 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 02:11:22,104 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:11:22] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 02:11:45,671 - app_enhanced - INFO - GET http://127.0.0.1:5500/ - 127.0.0.1
2025-06-28 02:11:45,682 - app_enhanced - INFO - GET http://127.0.0.1:5500/ - 200
2025-06-28 02:11:45,682 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:11:45] "GET / HTTP/1.1" 200 -
2025-06-28 02:12:28,767 - app_enhanced - INFO - GET http://127.0.0.1:5500/health - 127.0.0.1
2025-06-28 02:12:28,795 - app_enhanced - INFO - GET http://127.0.0.1:5500/health - 200
2025-06-28 02:12:28,796 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:12:28] "GET /health HTTP/1.1" 200 -
2025-06-28 02:12:52,690 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 02:12:52,711 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 02:12:52,711 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:12:52] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 02:13:48,654 - app_enhanced - INFO - GET http://127.0.0.1:5500/ - 127.0.0.1
2025-06-28 02:13:48,654 - app_enhanced - INFO - GET http://127.0.0.1:5500/ - 200
2025-06-28 02:13:48,655 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:13:48] "GET / HTTP/1.1" 200 -
2025-06-28 02:13:48,679 - app_enhanced - INFO - GET http://127.0.0.1:5500/health - 127.0.0.1
2025-06-28 02:13:48,697 - app_enhanced - INFO - GET http://127.0.0.1:5500/health - 200
2025-06-28 02:13:48,698 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:13:48] "GET /health HTTP/1.1" 200 -
2025-06-28 02:13:48,712 - app_enhanced - INFO - GET http://127.0.0.1:5500/docs - 127.0.0.1
2025-06-28 02:13:48,713 - app_enhanced - INFO - GET http://127.0.0.1:5500/docs - 200
2025-06-28 02:13:48,713 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:13:48] "GET /docs HTTP/1.1" 200 -
2025-06-28 02:13:48,725 - app_enhanced - INFO - GET http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 02:13:48,726 - app_enhanced - INFO - GET http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 02:13:48,726 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:13:48] "GET /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 02:13:48,745 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 02:13:48,769 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 02:13:48,769 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:13:48] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 02:13:48,782 - app_enhanced - INFO - GET http://127.0.0.1:5500/api/v1/system/health-check - 127.0.0.1
2025-06-28 02:13:49,815 - app_enhanced - INFO - GET http://127.0.0.1:5500/api/v1/system/health-check - 200
2025-06-28 02:13:49,815 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:13:49] "GET /api/v1/system/health-check HTTP/1.1" 200 -
2025-06-28 02:13:49,831 - app_enhanced - INFO - GET http://127.0.0.1:5500/api/v1/system/info - 127.0.0.1
2025-06-28 02:13:49,831 - app_enhanced - INFO - GET http://127.0.0.1:5500/api/v1/system/info - 200
2025-06-28 02:13:49,832 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:13:49] "GET /api/v1/system/info HTTP/1.1" 200 -
2025-06-28 02:13:49,849 - app_enhanced - INFO - GET http://127.0.0.1:5500/api/v1/monitor/list - 127.0.0.1
2025-06-28 02:13:49,850 - app_enhanced - INFO - GET http://127.0.0.1:5500/api/v1/monitor/list - 200
2025-06-28 02:13:49,850 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:13:49] "GET /api/v1/monitor/list HTTP/1.1" 200 -
2025-06-28 02:13:49,870 - app_enhanced - INFO - GET http://127.0.0.1:5500/api/v1/analysis/statistics/overview - 127.0.0.1
2025-06-28 02:13:49,871 - app_enhanced - INFO - GET http://127.0.0.1:5500/api/v1/analysis/statistics/overview - 200
2025-06-28 02:13:49,871 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:13:49] "GET /api/v1/analysis/statistics/overview HTTP/1.1" 200 -
2025-06-28 02:20:43,154 - app_enhanced - INFO - 启动基于Yolov8与ByteTrack的高速公路智慧监控平台后端服务
2025-06-28 02:20:43,154 - app_enhanced - INFO - 服务地址: http://127.0.0.1:5500
2025-06-28 02:20:43,154 - app_enhanced - INFO - 调试模式: False
2025-06-28 02:20:43,169 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5500
2025-06-28 02:20:43,169 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-28 02:21:55,366 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 02:21:55,375 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 02:21:55,376 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:21:55] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 02:32:47,453 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 02:32:47,551 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 02:32:47,557 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:32:47] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 02:37:27,474 - app_enhanced - INFO - 启动基于Yolov8与ByteTrack的高速公路智慧监控平台后端服务
2025-06-28 02:37:27,475 - app_enhanced - INFO - 服务地址: http://127.0.0.1:5500
2025-06-28 02:37:27,475 - app_enhanced - INFO - 调试模式: False
2025-06-28 02:37:27,486 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5500
2025-06-28 02:37:27,486 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-28 02:37:34,488 - app_enhanced - INFO - GET http://127.0.0.1:5500/ - 127.0.0.1
2025-06-28 02:37:34,488 - app_enhanced - INFO - GET http://127.0.0.1:5500/ - 200
2025-06-28 02:37:34,488 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:37:34] "GET / HTTP/1.1" 200 -
2025-06-28 02:37:34,497 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 02:37:34,522 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 02:37:34,523 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:37:34] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 02:37:46,301 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 02:37:46,305 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 02:37:46,306 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:37:46] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 02:39:42,919 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 02:39:42,921 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 02:39:42,922 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:39:42] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 02:39:42,945 - app_enhanced - INFO - GET http://127.0.0.1:5500/ - 127.0.0.1
2025-06-28 02:39:42,946 - app_enhanced - INFO - GET http://127.0.0.1:5500/ - 200
2025-06-28 02:39:42,946 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:39:42] "GET / HTTP/1.1" 200 -
2025-06-28 02:39:42,965 - app_enhanced - INFO - GET http://127.0.0.1:5500/health - 127.0.0.1
2025-06-28 02:39:42,983 - app_enhanced - INFO - GET http://127.0.0.1:5500/health - 200
2025-06-28 02:39:42,983 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:39:42] "GET /health HTTP/1.1" 200 -
2025-06-28 02:42:06,217 - app_enhanced - INFO - 启动基于Yolov8与ByteTrack的高速公路智慧监控平台后端服务
2025-06-28 02:42:06,217 - app_enhanced - INFO - 服务地址: http://127.0.0.1:5500
2025-06-28 02:42:06,217 - app_enhanced - INFO - 调试模式: False
2025-06-28 02:42:06,229 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5500
2025-06-28 02:42:06,229 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-28 02:42:22,572 - app_enhanced - INFO - GET http://127.0.0.1:5500/ - 127.0.0.1
2025-06-28 02:42:22,574 - app_enhanced - INFO - GET http://127.0.0.1:5500/ - 200
2025-06-28 02:42:22,575 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:42:22] "GET / HTTP/1.1" 200 -
2025-06-28 02:42:22,591 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 02:42:22,608 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 02:42:22,609 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:42:22] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 02:46:54,026 - app_enhanced - INFO - 启动基于Yolov8与ByteTrack的高速公路智慧监控平台后端服务
2025-06-28 02:46:54,028 - app_enhanced - INFO - 服务地址: http://127.0.0.1:5500
2025-06-28 02:46:54,028 - app_enhanced - INFO - 调试模式: False
2025-06-28 02:46:54,039 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5500
2025-06-28 02:46:54,039 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-28 02:47:05,768 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 02:47:05,782 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 02:47:05,782 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:47:05] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 02:47:17,188 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 02:47:17,189 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 02:47:17,193 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:47:17] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 02:49:26,516 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/logout - 127.0.0.1
2025-06-28 02:49:26,521 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/logout - 200
2025-06-28 02:49:26,523 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:49:26] "POST /api/v1/auth/logout HTTP/1.1" 200 -
2025-06-28 02:49:36,656 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 02:49:36,672 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 02:49:36,673 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:49:36] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 02:54:11,621 - app_enhanced - INFO - 启动基于Yolov8与ByteTrack的高速公路智慧监控平台后端服务
2025-06-28 02:54:11,621 - app_enhanced - INFO - 服务地址: http://127.0.0.1:5500
2025-06-28 02:54:11,621 - app_enhanced - INFO - 调试模式: False
2025-06-28 02:54:11,632 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5500
2025-06-28 02:54:11,632 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-28 02:54:29,595 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 02:54:29,616 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 02:54:29,616 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:54:29] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 02:55:05,282 - app_enhanced - INFO - 启动基于Yolov8与ByteTrack的高速公路智慧监控平台后端服务
2025-06-28 02:55:05,283 - app_enhanced - INFO - 服务地址: http://127.0.0.1:5500
2025-06-28 02:55:05,283 - app_enhanced - INFO - 调试模式: False
2025-06-28 02:55:05,291 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5500
2025-06-28 02:55:05,292 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-28 02:55:10,487 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 02:55:10,506 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 02:55:10,507 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:55:10] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 02:55:21,592 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 02:55:21,594 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 02:55:21,596 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:55:21] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 02:55:22,498 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 02:55:22,500 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 02:55:22,501 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:55:22] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 02:55:33,908 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 02:55:33,930 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 02:55:33,930 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:55:33] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 02:57:20,924 - app_enhanced - INFO - 启动基于Yolov8与ByteTrack的高速公路智慧监控平台后端服务
2025-06-28 02:57:20,926 - app_enhanced - INFO - 服务地址: http://127.0.0.1:5500
2025-06-28 02:57:20,926 - app_enhanced - INFO - 调试模式: False
2025-06-28 02:57:20,935 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5500
2025-06-28 02:57:20,935 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-28 02:57:25,264 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 02:57:25,269 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 02:57:25,270 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 02:57:25] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 03:02:15,761 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 03:02:15,761 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 03:02:15,761 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 03:02:15] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 03:02:22,905 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 03:02:22,938 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 03:02:22,938 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 03:02:22] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 03:07:58,737 - app_enhanced - INFO - 启动基于Yolov8与ByteTrack的高速公路智慧监控平台后端服务
2025-06-28 03:07:58,737 - app_enhanced - INFO - 服务地址: http://127.0.0.1:5500
2025-06-28 03:07:58,737 - app_enhanced - INFO - 调试模式: False
2025-06-28 03:07:58,745 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5500
2025-06-28 03:07:58,745 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-28 03:08:08,387 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 03:08:08,397 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 03:08:08,398 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 03:08:08] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 03:08:16,860 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 03:08:16,873 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 03:08:16,874 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 03:08:16] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 03:11:45,099 - app_enhanced - INFO - 启动基于Yolov8与ByteTrack的高速公路智慧监控平台后端服务
2025-06-28 03:11:45,099 - app_enhanced - INFO - 服务地址: http://127.0.0.1:5500
2025-06-28 03:11:45,099 - app_enhanced - INFO - 调试模式: False
2025-06-28 03:11:45,109 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5500
2025-06-28 03:11:45,113 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-28 03:11:54,439 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 03:11:54,454 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 03:11:54,454 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 03:11:54] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 03:12:02,473 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 03:12:02,498 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 03:12:02,498 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 03:12:02] "POST /api/v1/auth/login HTTP/1.1" 200 -
2025-06-28 03:15:45,192 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 127.0.0.1
2025-06-28 03:15:45,205 - app_enhanced - INFO - POST http://127.0.0.1:5500/api/v1/auth/login - 200
2025-06-28 03:15:45,205 - werkzeug - INFO - 127.0.0.1 - - [28/Jun/2025 03:15:45] "POST /api/v1/auth/login HTTP/1.1" 200 -
