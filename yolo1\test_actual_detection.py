#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试yolo1项目实际检测性能
直接使用classes/yolo.py中的检测代码
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import torch
import numpy as np
import time
import supervision as sv
from ultralytics import <PERSON>OL<PERSON>

def test_actual_detection_performance():
    print("=== yolo1项目实际检测性能测试 ===")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA版本: {torch.version.cuda}")
        print(f"GPU设备: {torch.cuda.get_device_name(0)}")
    
    # 检查设备选择
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"选择的设备: {device}")
    
    # 加载模型
    print("\n加载YOLO模型...")
    model = YOLO('yolov8n.pt')
    
    # 创建测试图像
    test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
    
    print("\n=== 使用yolo1项目实际检测代码 ===")
    try:
        # GPU预热
        print("GPU预热...")
        for _ in range(3):
            results = model.track(test_image, device=device, verbose=False)
        
        # 性能测试
        test_times = []
        num_tests = 10
        
        for i in range(num_tests):
            start_time = time.time()
            
            # 使用yolo1项目中实际的检测代码
            results = model.track(test_image, device=device, verbose=False)
            result = results[0]
            
            if result.boxes is not None and len(result.boxes) > 0:
                # 直接使用sv.Detections.from_yolov8(result) - 这是yolo1项目实际使用的代码
                detections = sv.Detections.from_yolov8(result)
                detection_count = len(detections)
            else:
                detections = sv.Detections.empty()
                detection_count = 0
            
            end_time = time.time()
            elapsed_time = end_time - start_time
            test_times.append(elapsed_time)
            
            if i == 0:
                print(f"首次检测: {elapsed_time:.4f}秒, 检测到{detection_count}个目标")
                if result.boxes is not None:
                    print(f"检测结果设备: {result.boxes.xyxy.device}")
        
        # 计算统计信息
        avg_time = np.mean(test_times)
        min_time = np.min(test_times)
        max_time = np.max(test_times)
        fps = 1.0 / avg_time
        
        print(f"\n=== 性能统计 ({num_tests}次测试) ===")
        print(f"平均耗时: {avg_time:.4f}秒")
        print(f"最小耗时: {min_time:.4f}秒")
        print(f"最大耗时: {max_time:.4f}秒")
        print(f"平均FPS: {fps:.2f}")
        
        # 性能评估
        if fps > 60:
            performance_level = "优秀"
        elif fps > 30:
            performance_level = "良好"
        elif fps > 15:
            performance_level = "一般"
        else:
            performance_level = "较差"
        
        print(f"性能评估: {performance_level}")
        
        # 与目标性能对比
        target_fps = 60
        if fps >= target_fps * 0.9:
            print(f"✅ 性能达标！(目标: {target_fps}FPS, 实际: {fps:.2f}FPS)")
        else:
            print(f"❌ 性能未达标 (目标: {target_fps}FPS, 实际: {fps:.2f}FPS)")
            print(f"性能差距: {target_fps - fps:.2f}FPS")
        
        return True
        
    except Exception as e:
        print(f"检测过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_actual_detection_performance()
    if success:
        print("\n✅ yolo1项目检测性能测试完成")
    else:
        print("\n❌ yolo1项目检测性能测试失败")