#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
行人检测对话框 - 简洁功能版
根据算法功能模块重新设计，删除多余样式
"""

from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *
import time

class SimplePedestrianDialog(QDialog):
    """行人检测对话框 - 简洁版"""
    
    # 定义信号
    detectionStarted = Signal(dict)
    detectionStopped = Signal()
    pedestrianDetected = Signal(int)  # 检测到的行人数量
    
    def __init__(self, parent=None, yolo_predictor=None):
        super().__init__(parent)
        self.yolo_predictor = yolo_predictor
        self.is_detecting = False
        self.pedestrian_count = 0
        self.total_detected = 0
        self.drag_pos = QPoint()
        
        self.init_ui()
        self.setup_timer()
        
    def init_ui(self):
        """初始化UI - 简洁设计"""
        self.setWindowTitle("行人检测系统")
        self.setFixedSize(600, 450)
        self.setModal(True)

        # 设置窗口无边框
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.Dialog)
        self.setAttribute(Qt.WA_TranslucentBackground)

        # 主容器
        main_container = QFrame(self)
        main_container.setGeometry(0, 0, 600, 450)
        main_container.setStyleSheet("""
            QFrame {
                background: rgba(248, 248, 248, 0.95);
                border: 1px solid rgba(0, 0, 0, 0.1);
                border-radius: 12px;
                backdrop-filter: blur(20px);
            }
        """)
        
        # 主布局
        layout = QVBoxLayout(main_container)
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(15)
        
        # 标题栏
        self.create_header(layout)
        
        # 检测配置
        self.create_config(layout)
        
        # 检测统计
        self.create_stats(layout)
        
        # 控制按钮
        self.create_controls(layout)
        
    def create_header(self, parent_layout):
        """创建标题栏"""
        header_layout = QHBoxLayout()
        
        # 标题
        title_label = QLabel("行人检测系统")
        title_label.setStyleSheet("""
            QLabel {
                color: #1d1d1f;
                font-size: 20px;
                font-weight: 600;
                background: transparent;
                padding: 8px;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }
        """)
        
        # 关闭按钮
        close_btn = QPushButton("×")
        close_btn.setFixedSize(28, 28)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff5f57;
                color: white;
                border: none;
                border-radius: 14px;
                font-size: 16px;
                font-weight: 500;
            }
            QPushButton:hover {
                background-color: #ff3b30;
                transform: scale(1.05);
            }
        """)
        close_btn.clicked.connect(self.close)
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addWidget(close_btn)
        
        parent_layout.addLayout(header_layout)
        
    def create_config(self, parent_layout):
        """创建检测配置"""
        config_group = QGroupBox("检测配置")
        config_group.setStyleSheet("""
            QGroupBox {
                font-weight: 600;
                font-size: 14px;
                color: #1d1d1f;
                border: 1px solid rgba(0, 0, 0, 0.1);
                border-radius: 8px;
                margin-top: 12px;
                padding-top: 16px;
                background-color: rgba(255, 255, 255, 0.6);
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 12px;
                padding: 0 8px 0 8px;
            }
        """)
        
        config_layout = QHBoxLayout(config_group)
        
        # 置信度设置
        conf_label = QLabel("置信度:")
        conf_label.setStyleSheet("font-size: 13px; color: #1d1d1f; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;")
        self.confidence_spinbox = QDoubleSpinBox()
        self.confidence_spinbox.setRange(0.1, 1.0)
        self.confidence_spinbox.setSingleStep(0.1)
        self.confidence_spinbox.setValue(0.5)
        self.confidence_spinbox.setFixedWidth(80)
        self.confidence_spinbox.setStyleSheet("""
            QDoubleSpinBox {
                font-size: 13px;
                padding: 6px;
                border: 1px solid rgba(0, 0, 0, 0.2);
                border-radius: 6px;
                background-color: rgba(255, 255, 255, 0.8);
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }
            QDoubleSpinBox:focus {
                border: 2px solid #007aff;
            }
        """)
        
        config_layout.addWidget(conf_label)
        config_layout.addWidget(self.confidence_spinbox)
        config_layout.addStretch()
        
        parent_layout.addWidget(config_group)
        
    def create_stats(self, parent_layout):
        """创建检测统计"""
        stats_group = QGroupBox("检测统计")
        stats_group.setStyleSheet("""
            QGroupBox {
                font-weight: 600;
                font-size: 14px;
                color: #1d1d1f;
                border: 1px solid rgba(0, 0, 0, 0.1);
                border-radius: 8px;
                margin-top: 12px;
                padding-top: 16px;
                background-color: rgba(255, 255, 255, 0.6);
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 12px;
                padding: 0 8px 0 8px;
            }
        """)
        
        stats_layout = QGridLayout(stats_group)
        
        # 当前检测数
        current_label = QLabel("当前行人:")
        current_label.setStyleSheet("font-size: 13px; color: #1d1d1f; padding: 3px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;")
        self.current_count_label = QLabel("0")
        self.current_count_label.setStyleSheet("""
            QLabel {
                color: #34c759;
                font-size: 18px;
                font-weight: 600;
                background: rgba(52, 199, 89, 0.1);
                border-radius: 6px;
                padding: 6px;
                min-width: 40px;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }
        """)
        self.current_count_label.setAlignment(Qt.AlignCenter)
        
        # 累计检测数
        total_label = QLabel("累计检测:")
        total_label.setStyleSheet("font-size: 13px; color: #1d1d1f; padding: 3px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;")
        self.total_count_label = QLabel("0")
        self.total_count_label.setStyleSheet("""
            QLabel {
                color: #007aff;
                font-size: 18px;
                font-weight: 600;
                background: rgba(0, 122, 255, 0.1);
                border-radius: 6px;
                padding: 6px;
                min-width: 40px;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }
        """)
        self.total_count_label.setAlignment(Qt.AlignCenter)
        
        stats_layout.addWidget(current_label, 0, 0)
        stats_layout.addWidget(self.current_count_label, 0, 1)
        stats_layout.addWidget(total_label, 1, 0)
        stats_layout.addWidget(self.total_count_label, 1, 1)
        
        parent_layout.addWidget(stats_group)
        
    def create_controls(self, parent_layout):
        """创建控制按钮"""
        button_layout = QHBoxLayout()
        
        # 开始检测按钮
        self.start_btn = QPushButton("开始检测")
        self.start_btn.setFixedHeight(36)
        self.start_btn.setStyleSheet("""
            QPushButton {
                background-color: #007aff;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: 500;
                font-size: 14px;
                padding: 8px 16px;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }
            QPushButton:hover {
                background-color: #0056cc;
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                background-color: #004499;
                transform: translateY(0px);
            }
            QPushButton:disabled {
                background-color: #d1d1d6;
                color: #8e8e93;
            }
        """)
        self.start_btn.clicked.connect(self.toggle_detection)
        
        # 停止检测按钮
        self.stop_btn = QPushButton("停止检测")
        self.stop_btn.setFixedHeight(36)
        self.stop_btn.setEnabled(False)
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff3b30;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: 500;
                font-size: 14px;
                padding: 8px 16px;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }
            QPushButton:hover {
                background-color: #d70015;
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                background-color: #a20000;
                transform: translateY(0px);
            }
            QPushButton:disabled {
                background-color: #d1d1d6;
                color: #8e8e93;
            }
        """)
        self.stop_btn.clicked.connect(self.toggle_detection)
        
        # 清除统计按钮
        clear_btn = QPushButton("清除统计")
        clear_btn.setFixedHeight(36)
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #8e8e93;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: 500;
                font-size: 14px;
                padding: 8px 16px;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }
            QPushButton:hover {
                background-color: #6d6d70;
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                background-color: #48484a;
                transform: translateY(0px);
            }
        """)
        clear_btn.clicked.connect(self.clear_statistics)
        
        button_layout.addWidget(self.start_btn)
        button_layout.addWidget(self.stop_btn)
        button_layout.addWidget(clear_btn)
        
        parent_layout.addLayout(button_layout)
        
    def setup_timer(self):
        """设置定时器"""
        self.detection_timer = QTimer()
        self.detection_timer.timeout.connect(self.update_detection_display)
        
    def toggle_detection(self):
        """切换检测状态"""
        if not self.is_detecting:
            self.start_detection()
        else:
            self.stop_detection()
            
    def start_detection(self):
        """启动行人检测"""
        if not self.yolo_predictor:
            QMessageBox.warning(self, "错误", "YOLO预测器未初始化，无法启动检测")
            return

        # 获取配置
        detection_config = {
            "confidence": self.confidence_spinbox.value(),
            "target_class": "person"  # 只检测人
        }

        # 启用YOLO检测器的行人检测模式
        self.yolo_predictor.set_pedestrian_detection_mode(True, self.handle_pedestrian_detection)
        
        # 设置置信度阈值
        self.yolo_predictor.conf_thres = self.confidence_spinbox.value()

        self.is_detecting = True
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        
        # 启动定时器
        self.detection_timer.start(100)  # 100ms更新一次
        
        # 发送信号
        self.detectionStarted.emit(detection_config)
        
    def stop_detection(self):
        """停止行人检测"""
        # 关闭YOLO检测器的行人检测模式
        if self.yolo_predictor:
            self.yolo_predictor.set_pedestrian_detection_mode(False)
        
        self.is_detecting = False
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        
        # 停止定时器
        self.detection_timer.stop()
        
        # 发送信号
        self.detectionStopped.emit()
        
    def handle_pedestrian_detection(self, detections, frame):
        """处理行人检测结果的回调函数"""
        if detections and self.is_detecting:
            # 统计当前帧中的行人数量
            current_count = len(detections)
            self.update_pedestrian_count(current_count)
    
    def update_detection_display(self):
        """更新检测显示"""
        if self.is_detecting:
            # 实际检测结果由handle_pedestrian_detection回调处理
            # 这里只需要保持UI更新
            pass
                
    def update_pedestrian_count(self, count):
        """更新行人数量"""
        self.pedestrian_count = count
        self.total_detected += count
        
        # 更新显示
        self.current_count_label.setText(str(self.pedestrian_count))
        self.total_count_label.setText(str(self.total_detected))
        
        # 发送信号
        self.pedestrianDetected.emit(count)
        
    def clear_statistics(self):
        """清除统计数据"""
        self.pedestrian_count = 0
        self.total_detected = 0
        self.current_count_label.setText("0")
        self.total_count_label.setText("0")
        
    def process_pedestrian_detection(self, detections, frame):
        """处理行人检测结果"""
        if not self.is_detecting:
            return
            
        # 统计行人数量
        pedestrian_count = 0
        if hasattr(detections, 'class_id'):
            # 假设class_id为0表示人
            pedestrian_count = sum(1 for class_id in detections.class_id if class_id == 0)
        
        if pedestrian_count > 0:
            self.update_pedestrian_count(pedestrian_count)
            
    def mousePressEvent(self, event):
        """鼠标按下事件 - 用于拖拽窗口"""
        if event.button() == Qt.LeftButton:
            self.drag_pos = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()
            
    def mouseMoveEvent(self, event):
        """鼠标移动事件 - 用于拖拽窗口"""
        if event.buttons() == Qt.LeftButton and self.drag_pos:
            self.move(event.globalPosition().toPoint() - self.drag_pos)
            event.accept()
            
    def closeEvent(self, event):
        """关闭事件"""
        if self.is_detecting:
            self.stop_detection()
        event.accept()
