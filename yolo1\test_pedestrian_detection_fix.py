#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的行人检测系统
验证YOLO检测器的行人检测模式是否正常工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

from ui.dialog.simple_pedestrian_dialog import SimplePedestrianDialog
from classes.yolo import YoloPredictor

class TestPedestrianDetectionWindow(QMainWindow):
    """测试行人检测修复的主窗口"""
    
    def __init__(self):
        super().__init__()
        self.yolo_predictor = None
        self.init_ui()
        self.init_yolo()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("行人检测系统修复测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 布局
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(50, 50, 50, 50)
        
        # 标题
        title_label = QLabel("行人检测系统修复测试")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                padding: 20px;
                background: #ecf0f1;
                border-radius: 10px;
                border: 2px solid #bdc3c7;
            }
        """)
        layout.addWidget(title_label)
        
        # 说明文本
        info_label = QLabel("""
        <h3>修复内容：</h3>
        <ul>
            <li><b>添加行人检测模式标志：</b>在YOLO检测器中添加pedestrian_detection_mode属性</li>
            <li><b>修复对象记录逻辑：</b>update_detected_objects方法现在根据检测模式过滤对象</li>
            <li><b>修复显示过滤逻辑：</b>绘制检测框时根据检测模式显示相应类别</li>
            <li><b>添加模式切换方法：</b>set_pedestrian_detection_mode方法用于启用/关闭行人检测</li>
            <li><b>更新中文名称映射：</b>添加'person': '行人'的中文名称支持</li>
            <li><b>集成检测回调：</b>行人检测对话框现在能正确调用YOLO检测器</li>
        </ul>
        
        <h3>测试说明：</h3>
        <p>点击下方按钮打开行人检测对话框，启动检测后系统将只检测和显示行人，不再显示车辆。</p>
        """)
        info_label.setWordWrap(True)
        info_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #34495e;
                background: #f8f9fa;
                padding: 20px;
                border-radius: 8px;
                border: 1px solid #dee2e6;
                line-height: 1.6;
            }
        """)
        layout.addWidget(info_label)
        
        # 测试按钮
        test_btn = QPushButton("打开行人检测对话框")
        test_btn.setFixedHeight(50)
        test_btn.setStyleSheet("""
            QPushButton {
                font-size: 16px;
                font-weight: bold;
                color: white;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                border: none;
                border-radius: 25px;
                padding: 10px 30px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5dade2, stop:1 #3498db);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2980b9, stop:1 #21618c);
            }
        """)
        test_btn.clicked.connect(self.open_pedestrian_dialog)
        layout.addWidget(test_btn)
        
        # 状态标签
        self.status_label = QLabel("YOLO检测器初始化中...")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #7f8c8d;
                padding: 10px;
                background: #ffffff;
                border-radius: 5px;
                border: 1px solid #bdc3c7;
            }
        """)
        layout.addWidget(self.status_label)
        
    def init_yolo(self):
        """初始化YOLO检测器"""
        try:
            self.yolo_predictor = YoloPredictor()
            self.status_label.setText("YOLO检测器初始化完成，可以开始测试")
            self.status_label.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    color: #27ae60;
                    padding: 10px;
                    background: #d5f4e6;
                    border-radius: 5px;
                    border: 1px solid #27ae60;
                }
            """)
        except Exception as e:
            self.status_label.setText(f"YOLO检测器初始化失败: {str(e)}")
            self.status_label.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    color: #e74c3c;
                    padding: 10px;
                    background: #fadbd8;
                    border-radius: 5px;
                    border: 1px solid #e74c3c;
                }
            """)
            
    def open_pedestrian_dialog(self):
        """打开行人检测对话框"""
        if not self.yolo_predictor:
            QMessageBox.warning(self, "错误", "YOLO检测器未初始化")
            return
            
        dialog = SimplePedestrianDialog(self, self.yolo_predictor)
        
        # 连接信号
        dialog.detectionStarted.connect(self.on_detection_started)
        dialog.detectionStopped.connect(self.on_detection_stopped)
        dialog.pedestrianDetected.connect(self.on_pedestrian_detected)
        
        dialog.exec()
        
    def on_detection_started(self, config):
        """检测开始时的处理"""
        print(f"行人检测已启动，配置: {config}")
        self.status_label.setText("行人检测模式已启动")
        
    def on_detection_stopped(self):
        """检测停止时的处理"""
        print("行人检测已停止")
        self.status_label.setText("行人检测模式已停止")
        
    def on_pedestrian_detected(self, count):
        """检测到行人时的处理"""
        print(f"检测到 {count} 个行人")
        self.status_label.setText(f"当前检测到 {count} 个行人")

def main():
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle('Fusion')
    
    # 打印修复说明
    print("=" * 60)
    print("行人检测系统修复测试")
    print("=" * 60)
    print("修复内容:")
    print("1. 添加行人检测模式标志 (pedestrian_detection_mode)")
    print("2. 修复update_detected_objects方法的对象过滤逻辑")
    print("3. 修复绘制检测框时的类别过滤逻辑")
    print("4. 添加set_pedestrian_detection_mode方法")
    print("5. 更新中文名称映射，添加行人支持")
    print("6. 集成行人检测对话框与YOLO检测器")
    print("=" * 60)
    print("现在系统应该能够正确区分行人检测和车辆检测模式")
    print("=" * 60)
    
    window = TestPedestrianDetectionWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == '__main__':
    main()