#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API token验证
"""

import requests
import json

def test_login_and_apis():
    """测试登录和API调用"""
    base_url = "http://127.0.0.1:5500"
    
    print("🔐 测试登录...")
    
    # 1. 登录获取token
    login_data = {"username": "admin", "password": "123456"}
    try:
        response = requests.post(f"{base_url}/api/v1/auth/login", json=login_data, timeout=10)
        print(f"登录响应状态码: {response.status_code}")
        print(f"登录响应内容: {response.text}")
        
        if response.status_code == 200:
            login_result = response.json()
            if login_result.get('success'):
                token = login_result['data']['token']
                print(f"✅ 登录成功，获取到token: {token[:50]}...")
                
                # 2. 测试需要token的API
                headers = {"Authorization": f"Bearer {token}"}
                
                test_apis = [
                    ("/api/v1/analysis/statistics/overview", "概览统计"),
                    ("/api/v1/analysis/alarms", "警报统计"),
                    ("/api/v1/analysis/traffic-flow", "交通流量"),
                    ("/api/v1/system/users", "系统用户"),
                    ("/api/v1/detection/tasks", "检测任务")
                ]
                
                for endpoint, desc in test_apis:
                    try:
                        print(f"\n🧪 测试 {desc} API...")
                        api_response = requests.get(f"{base_url}{endpoint}", headers=headers, timeout=10)
                        print(f"状态码: {api_response.status_code}")
                        
                        if api_response.status_code == 200:
                            api_result = api_response.json()
                            if api_result.get('success'):
                                print(f"✅ {desc} API 成功")
                                # 显示数据结构
                                data = api_result.get('data', {})
                                if isinstance(data, dict):
                                    print(f"   数据字段: {list(data.keys())}")
                                elif isinstance(data, list):
                                    print(f"   数据数量: {len(data)}")
                            else:
                                print(f"❌ {desc} API 失败: {api_result.get('message')}")
                        else:
                            print(f"❌ {desc} API HTTP错误: {api_response.status_code}")
                            print(f"   响应: {api_response.text}")
                    except Exception as e:
                        print(f"❌ {desc} API 请求异常: {e}")
                
            else:
                print(f"❌ 登录失败: {login_result.get('message')}")
        else:
            print(f"❌ 登录HTTP错误: {response.status_code}")
            print(f"响应: {response.text}")
            
    except Exception as e:
        print(f"❌ 登录请求异常: {e}")

def test_token_format():
    """测试token格式"""
    print("\n🔍 测试token格式...")
    
    base_url = "http://127.0.0.1:5500"
    
    # 测试不同的token格式
    test_cases = [
        ("", "空token"),
        ("invalid_token", "无效token"),
        ("Bearer invalid_token", "Bearer + 无效token")
    ]
    
    for token, desc in test_cases:
        print(f"\n测试 {desc}...")
        headers = {"Authorization": token} if token else {}
        
        try:
            response = requests.get(f"{base_url}/api/v1/analysis/statistics/overview", headers=headers, timeout=5)
            print(f"状态码: {response.status_code}")
            if response.status_code != 200:
                result = response.json()
                print(f"错误信息: {result.get('message')}")
        except Exception as e:
            print(f"请求异常: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 API Token 测试")
    print("=" * 60)
    
    test_login_and_apis()
    test_token_format()
    
    print("\n" + "=" * 60)
    print("💡 如果所有API都返回401错误，可能的原因:")
    print("   1. JWT SECRET_KEY 配置问题")
    print("   2. token生成或验证逻辑错误")
    print("   3. 请求头格式问题")
    print("   4. 后端服务未正常启动")
    print("=" * 60)

if __name__ == "__main__":
    main()
