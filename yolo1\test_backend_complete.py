#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的后端测试脚本
检查所有后端问题：数据库、CORS、API路径、响应格式、健康检查
"""

import os
import sys
import requests
import json
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(override=True, dotenv_path='config/end-back.env')

# 添加backend路径
sys.path.append('backend')

def test_database_tables():
    """测试数据库表检查"""
    print("🔍 测试数据库表检查...")
    
    try:
        from utils.database import get_db_connection, init_database
        
        # 测试init_database函数
        print("测试init_database函数...")
        success = init_database()
        
        if success:
            print("✅ init_database函数测试成功")
        else:
            print("❌ init_database函数测试失败")
            
        # 手动检查所有表
        print("\n手动检查数据库表...")
        with get_db_connection() as db:
            db.execute(
                "SELECT table_name FROM information_schema.tables "
                "WHERE table_schema = DATABASE() ORDER BY table_name"
            )
            tables = db.fetchall()
            table_names = [table['table_name'] for table in tables]
            
            print(f"✅ 数据库表总数: {len(table_names)}")
            print(f"✅ 表列表: {', '.join(table_names)}")
            
            # 检查每个表的记录数
            for table_name in table_names:
                try:
                    db.execute(f"SELECT COUNT(*) as count FROM {table_name}")
                    result = db.fetchone()
                    count = result['count'] if result else 0
                    print(f"   {table_name}: {count} 条记录")
                except Exception as e:
                    print(f"   {table_name}: 查询失败 - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库表检查失败: {e}")
        return False

def test_backend_apis():
    """测试后端API接口"""
    print("\n🔍 测试后端API接口...")
    
    base_url = "http://127.0.0.1:5500"
    
    # 1. 测试基础连接
    print("1. 测试基础连接...")
    try:
        response = requests.get(base_url, timeout=5)
        if response.status_code == 200:
            print("✅ 基础连接成功")
            data = response.json()
            print(f"   状态: {data.get('status')}")
            print(f"   版本: {data.get('version')}")
        else:
            print(f"❌ 基础连接失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 基础连接异常: {e}")
        return False
    
    # 2. 测试API文档
    print("\n2. 测试API文档...")
    try:
        response = requests.get(f"{base_url}/api/v1/docs", timeout=5)
        if response.status_code == 200:
            print("✅ API文档接口正常")
            data = response.json()
            endpoints = data.get('data', {}).get('endpoints', {})
            print(f"   可用模块: {list(endpoints.keys())}")
        else:
            print(f"❌ API文档接口失败: {response.status_code}")
    except Exception as e:
        print(f"❌ API文档接口异常: {e}")
    
    # 3. 测试健康检查（无需认证）
    print("\n3. 测试健康检查...")
    try:
        response = requests.get(f"{base_url}/api/v1/system/health-check", timeout=5)
        print(f"   健康检查响应状态: {response.status_code}")
        if response.status_code == 200:
            print("✅ 健康检查接口正常")
            data = response.json()
            print(f"   响应格式: {type(data)}")
            print(f"   数据库状态: {data.get('data', {}).get('database', 'N/A')}")
        else:
            print(f"❌ 健康检查失败: {response.text}")
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
    
    # 4. 测试登录接口
    print("\n4. 测试登录接口...")
    try:
        login_data = {"username": "admin", "password": "123456"}
        response = requests.post(f"{base_url}/api/v1/auth/login", json=login_data, timeout=5)
        print(f"   登录响应状态: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 登录接口正常")
            data = response.json()
            token = data.get('data', {}).get('token')
            if token:
                print(f"   获取Token: {token[:30]}...")
                return test_protected_apis(base_url, token)
            else:
                print("⚠️ 登录成功但未获取到Token")
        else:
            print(f"❌ 登录失败: {response.text}")
    except Exception as e:
        print(f"❌ 登录接口异常: {e}")
    
    return False

def test_protected_apis(base_url, token):
    """测试需要认证的API"""
    print("\n5. 测试需要认证的API...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # 测试用户信息
    try:
        response = requests.get(f"{base_url}/api/v1/auth/profile", headers=headers, timeout=5)
        print(f"   用户信息响应状态: {response.status_code}")
        if response.status_code == 200:
            print("✅ 用户信息接口正常")
            data = response.json()
            user = data.get('data', {})
            print(f"   用户: {user.get('username')}")
            print(f"   角色: {user.get('grade')}")
        else:
            print(f"❌ 用户信息接口失败: {response.text}")
    except Exception as e:
        print(f"❌ 用户信息接口异常: {e}")
    
    # 测试监控点列表
    try:
        response = requests.get(f"{base_url}/api/v1/monitor/list", headers=headers, timeout=5)
        print(f"   监控点列表响应状态: {response.status_code}")
        if response.status_code == 200:
            print("✅ 监控点列表接口正常")
            data = response.json()
            monitors = data.get('data', {}).get('monitors', [])
            print(f"   监控点数量: {len(monitors)}")
        else:
            print(f"❌ 监控点列表接口失败: {response.text}")
    except Exception as e:
        print(f"❌ 监控点列表接口异常: {e}")
    
    # 测试概览统计
    try:
        response = requests.get(f"{base_url}/api/v1/analysis/statistics/overview", headers=headers, timeout=5)
        print(f"   概览统计响应状态: {response.status_code}")
        if response.status_code == 200:
            print("✅ 概览统计接口正常")
            data = response.json()
            stats = data.get('data', {})
            print(f"   统计数据: {list(stats.keys())}")
        else:
            print(f"❌ 概览统计接口失败: {response.text}")
    except Exception as e:
        print(f"❌ 概览统计接口异常: {e}")
    
    return True

def test_cors_headers():
    """测试CORS头"""
    print("\n🔍 测试CORS配置...")
    
    try:
        response = requests.options("http://127.0.0.1:5500/api/v1/docs", 
                                  headers={
                                      'Origin': 'http://localhost:3000',
                                      'Access-Control-Request-Method': 'GET',
                                      'Access-Control-Request-Headers': 'Content-Type'
                                  }, timeout=5)
        
        print(f"   CORS预检响应状态: {response.status_code}")
        
        cors_headers = {
            'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
            'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
            'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
            'Access-Control-Allow-Credentials': response.headers.get('Access-Control-Allow-Credentials')
        }
        
        print("   CORS头信息:")
        for header, value in cors_headers.items():
            if value:
                print(f"     {header}: {value}")
        
        if cors_headers['Access-Control-Allow-Origin']:
            print("✅ CORS配置正常")
        else:
            print("⚠️ CORS配置可能有问题")
            
    except Exception as e:
        print(f"❌ CORS测试异常: {e}")

def main():
    """主函数"""
    print("=" * 80)
    print("🚀 高速公路智能监控系统 - 完整后端测试")
    print("=" * 80)
    
    # 1. 测试数据库表
    db_success = test_database_tables()
    
    # 2. 测试CORS配置
    test_cors_headers()
    
    # 3. 测试API接口
    api_success = test_backend_apis()
    
    print("\n" + "=" * 80)
    print("📋 测试结果总结:")
    print(f"   数据库测试: {'✅ 通过' if db_success else '❌ 失败'}")
    print(f"   API接口测试: {'✅ 通过' if api_success else '❌ 失败'}")
    
    if db_success and api_success:
        print("\n🎉 所有测试通过！后端服务运行正常")
        print("✅ 前端可以正常连接后端API")
        print("✅ 数据库连接和表结构正常")
        print("✅ CORS配置正确")
        print("✅ API路径和响应格式正确")
    else:
        print("\n⚠️ 部分测试失败，请根据上述信息排查问题")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
