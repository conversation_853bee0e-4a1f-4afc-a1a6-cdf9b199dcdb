# -*- coding: utf-8 -*-
# @Description : 实时数据处理服务
# @Date : 2025年6月20日

import json
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from utils.database import get_db_connection

class RealtimeService:
    """实时数据处理服务类"""
    
    def __init__(self):
        self.monitor_data_cache = {}  # 监控点实时数据缓存
        self.system_status_cache = {}  # 系统状态缓存
        self.cache_lock = threading.Lock()
        self.cache_expire_time = 300  # 缓存过期时间（秒）
        
        # 启动缓存清理线程
        self._start_cache_cleanup()
    
    def get_all_active_streams(self):
        """获取所有活动的视频流"""
        with get_db_connection() as db:
            monitors = db.get_list(
                """SELECT id, location, url, rtsp_format 
                   FROM monitor 
                   WHERE connection_status='connected' AND is_alarm='开启'"""
            )
        return monitors
    
    def _start_cache_cleanup(self):
        """启动缓存清理线程"""
        def cleanup_cache():
            while True:
                try:
                    current_time = time.time()
                    with self.cache_lock:
                        # 清理过期的监控点数据
                        expired_keys = []
                        for key, data in self.monitor_data_cache.items():
                            if current_time - data.get('timestamp', 0) > self.cache_expire_time:
                                expired_keys.append(key)
                        
                        for key in expired_keys:
                            del self.monitor_data_cache[key]
                    
                    time.sleep(60)  # 每分钟清理一次
                except Exception as e:
                    print(f"缓存清理失败: {str(e)}")
                    time.sleep(60)
        
        cleanup_thread = threading.Thread(target=cleanup_cache)
        cleanup_thread.daemon = True
        cleanup_thread.start()
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取实时系统状态"""
        try:
            with get_db_connection() as db:
                # 监控点状态统计
                monitor_stats = db.get_one("""
                    SELECT 
                        COUNT(*) as total_monitors,
                        SUM(CASE WHEN is_alarm = '开启' THEN 1 ELSE 0 END) as active_monitors,
                        SUM(CASE WHEN connection_status = 'connected' THEN 1 ELSE 0 END) as connected_monitors
                    FROM monitor
                """)
                
                # 今日警报统计
                today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
                today_alarms = db.get_one("""
                    SELECT COUNT(*) as count
                    FROM alarm
                    WHERE create_time >= %s
                """, (today_start,))
                
                # 最近1小时警报
                hour_ago = datetime.now() - timedelta(hours=1)
                recent_alarms = db.get_one("""
                    SELECT COUNT(*) as count
                    FROM alarm
                    WHERE create_time >= %s
                """, (hour_ago,))
                
                # 系统负载（简化版）
                system_load = self._get_system_load()
            
            status = {
                'timestamp': datetime.now().isoformat(),
                'monitors': {
                    'total': monitor_stats['total_monitors'],
                    'active': monitor_stats['active_monitors'],
                    'connected': monitor_stats['connected_monitors'],
                    'offline': monitor_stats['total_monitors'] - monitor_stats['connected_monitors']
                },
                'alarms': {
                    'today_total': today_alarms['count'],
                    'last_hour': recent_alarms['count']
                },
                'system': system_load,
                'status': 'healthy' if monitor_stats['connected_monitors'] > 0 else 'warning'
            }
            
            # 缓存系统状态
            with self.cache_lock:
                self.system_status_cache = {
                    'data': status,
                    'timestamp': time.time()
                }
            
            return status
        except Exception as e:
            return {
                'timestamp': datetime.now().isoformat(),
                'status': 'error',
                'error': str(e)
            }
    
    def _get_system_load(self) -> Dict[str, Any]:
        """获取系统负载信息"""
        try:
            import psutil
            
            return {
                'cpu_percent': psutil.cpu_percent(interval=1),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_percent': psutil.disk_usage('/').percent,
                'load_average': psutil.getloadavg() if hasattr(psutil, 'getloadavg') else [0, 0, 0]
            }
        except ImportError:
            return {
                'cpu_percent': 0,
                'memory_percent': 0,
                'disk_percent': 0,
                'load_average': [0, 0, 0]
            }
    
    def get_monitor_realtime_data(self, monitor_id: int) -> Dict[str, Any]:
        """获取监控点实时数据"""
        try:
            # 先检查缓存
            cache_key = f"monitor_{monitor_id}"
            with self.cache_lock:
                if cache_key in self.monitor_data_cache:
                    cached_data = self.monitor_data_cache[cache_key]
                    if time.time() - cached_data['timestamp'] < 30:  # 30秒内的数据认为是实时的
                        return cached_data['data']
            
            # 从数据库获取最新数据
            with get_db_connection() as db:
                # 监控点基本信息
                monitor = db.get_one("""
                    SELECT id, location, highway_section, connection_status, 
                           is_alarm, threshold, conf_threshold
                    FROM monitor 
                    WHERE id = %s
                """, (monitor_id,))
                
                if not monitor:
                    return {'error': '监控点不存在'}
                
                # 最近的警报
                recent_alarm = db.get_one("""
                    SELECT vehicle_count, confidence_level, create_time, detection_details
                    FROM alarm
                    WHERE pid = %s
                    ORDER BY create_time DESC
                    LIMIT 1
                """, (monitor_id,))
                
                # 今日统计
                today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
                today_stats = db.get_one("""
                    SELECT COUNT(*) as alarm_count,
                           AVG(vehicle_count) as avg_vehicles,
                           MAX(vehicle_count) as max_vehicles
                    FROM alarm
                    WHERE pid = %s AND create_time >= %s
                """, (monitor_id, today_start))
            
            # 构建实时数据
            realtime_data = {
                'monitor_id': monitor_id,
                'monitor_info': monitor,
                'current_status': {
                    'is_online': monitor['connection_status'] == 'connected',
                    'is_active': monitor['is_alarm'] == '开启',
                    'last_update': datetime.now().isoformat()
                },
                'latest_detection': {
                    'vehicle_count': recent_alarm['vehicle_count'] if recent_alarm else 0,
                    'confidence': recent_alarm['confidence_level'] if recent_alarm else 0,
                    'timestamp': recent_alarm['create_time'].isoformat() if recent_alarm else None,
                    'details': json.loads(recent_alarm['detection_details']) if recent_alarm and recent_alarm['detection_details'] else {}
                },
                'today_statistics': {
                    'total_alarms': today_stats['alarm_count'],
                    'avg_vehicles': float(today_stats['avg_vehicles']) if today_stats['avg_vehicles'] else 0,
                    'max_vehicles': today_stats['max_vehicles'] if today_stats['max_vehicles'] else 0
                },
                'timestamp': datetime.now().isoformat()
            }
            
            # 缓存数据
            with self.cache_lock:
                self.monitor_data_cache[cache_key] = {
                    'data': realtime_data,
                    'timestamp': time.time()
                }
            
            return realtime_data
        except Exception as e:
            return {
                'monitor_id': monitor_id,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def update_monitor_data(self, monitor_id: int, detection_data: Dict[str, Any]):
        """更新监控点实时数据"""
        try:
            cache_key = f"monitor_{monitor_id}"
            
            # 获取当前缓存数据
            current_data = self.get_monitor_realtime_data(monitor_id)
            
            # 更新检测数据
            current_data['latest_detection'] = {
                'vehicle_count': detection_data.get('vehicle_count', 0),
                'confidence': detection_data.get('confidence', 0),
                'timestamp': datetime.now().isoformat(),
                'details': detection_data.get('details', {})
            }
            current_data['timestamp'] = datetime.now().isoformat()
            
            # 更新缓存
            with self.cache_lock:
                self.monitor_data_cache[cache_key] = {
                    'data': current_data,
                    'timestamp': time.time()
                }
            
            return True
        except Exception as e:
            print(f"更新监控点数据失败: {str(e)}")
            return False
    
    def get_stream_info(self, monitor_id: int) -> Dict[str, Any]:
        """获取监控点流信息"""
        try:
            with get_db_connection() as db:
                monitor = db.get_one("""
                    SELECT url, rtsp_format, connection_status
                    FROM monitor
                    WHERE id = %s
                """, (monitor_id,))
                
                if not monitor:
                    return {'error': '监控点不存在'}
            
            # 检测流状态（简化版）
            stream_status = self._check_stream_status(monitor['url'])
            
            return {
                'rtsp_url': monitor['url'],
                'format': monitor['rtsp_format'],
                'connection_status': monitor['connection_status'],
                'stream_status': stream_status,
                'last_check': datetime.now().isoformat()
            }
        except Exception as e:
            return {'error': str(e)}
    
    def _check_stream_status(self, rtsp_url: str) -> Dict[str, Any]:
        """检查流状态"""
        try:
            import cv2
            
            cap = cv2.VideoCapture(rtsp_url)
            if cap.isOpened():
                ret, frame = cap.read()
                cap.release()
                
                if ret and frame is not None:
                    return {
                        'status': 'active',
                        'frame_size': frame.shape,
                        'message': '流正常'
                    }
                else:
                    return {
                        'status': 'no_signal',
                        'message': '无信号'
                    }
            else:
                return {
                    'status': 'disconnected',
                    'message': '连接失败'
                }
        except Exception as e:
            return {
                'status': 'error',
                'message': str(e)
            }
    
    def get_latest_detection(self, monitor_id: int) -> Dict[str, Any]:
        """获取最新检测结果"""
        try:
            with get_db_connection() as db:
                detection = db.get_one("""
                    SELECT vehicle_count, confidence_level, create_time, 
                           detection_details, photo
                    FROM alarm
                    WHERE pid = %s
                    ORDER BY create_time DESC
                    LIMIT 1
                """, (monitor_id,))
                
                if not detection:
                    return {'message': '暂无检测数据'}
                
                return {
                    'vehicle_count': detection['vehicle_count'],
                    'confidence': detection['confidence_level'],
                    'timestamp': detection['create_time'].isoformat(),
                    'details': json.loads(detection['detection_details']) if detection['detection_details'] else {},
                    'image_url': detection['photo'],
                    'monitor_id': monitor_id
                }
        except Exception as e:
            return {'error': str(e)}
    
    def get_live_statistics(self) -> Dict[str, Any]:
        """获取实时统计数据"""
        try:
            with get_db_connection() as db:
                # 最近5分钟的统计
                five_min_ago = datetime.now() - timedelta(minutes=5)
                recent_stats = db.get_one("""
                    SELECT COUNT(*) as recent_alarms,
                           AVG(vehicle_count) as avg_vehicles,
                           MAX(vehicle_count) as max_vehicles
                    FROM alarm
                    WHERE create_time >= %s
                """, (five_min_ago,))
                
                # 当前活跃监控点
                active_monitors = db.get_list("""
                    SELECT id, location, highway_section
                    FROM monitor
                    WHERE is_alarm = '开启' AND connection_status = 'connected'
                """)
                
                # 系统负载
                system_load = self._get_system_load()
            
            return {
                'recent_activity': {
                    'alarms_last_5min': recent_stats['recent_alarms'],
                    'avg_vehicles': float(recent_stats['avg_vehicles']) if recent_stats['avg_vehicles'] else 0,
                    'max_vehicles': recent_stats['max_vehicles'] if recent_stats['max_vehicles'] else 0
                },
                'active_monitors': len(active_monitors),
                'monitor_list': active_monitors,
                'system_load': system_load,
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            return {
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def should_trigger_alert(self, monitor_id: int, detection_data: Dict[str, Any]) -> bool:
        """判断是否应该触发警报"""
        try:
            with get_db_connection() as db:
                monitor = db.get_one("""
                    SELECT threshold, is_alarm
                    FROM monitor
                    WHERE id = %s
                """, (monitor_id,))
                
                if not monitor or monitor['is_alarm'] != '开启':
                    return False
                
                vehicle_count = detection_data.get('vehicle_count', 0)
                threshold = monitor['threshold']
                
                return vehicle_count > threshold
        except Exception as e:
            print(f"判断警报触发失败: {str(e)}")
            return False
    
    def create_alert(self, monitor_id: int, detection_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建警报数据"""
        try:
            with get_db_connection() as db:
                monitor = db.get_one("""
                    SELECT location, highway_section, threshold, person
                    FROM monitor
                    WHERE id = %s
                """, (monitor_id,))
                
                if not monitor:
                    raise Exception("监控点不存在")
            
            vehicle_count = detection_data.get('vehicle_count', 0)
            confidence = detection_data.get('confidence', 0)
            
            alert_data = {
                'monitor_id': monitor_id,
                'location': monitor['location'],
                'highway_section': monitor['highway_section'],
                'description': f'车流量超标:{vehicle_count}辆',
                'vehicle_count': vehicle_count,
                'confidence_level': confidence,
                'threshold': monitor['threshold'],
                'detection_details': detection_data.get('details', {}),
                'photo': detection_data.get('image_url', ''),
                'timestamp': datetime.now().isoformat(),
                'monitor_person': monitor['person']
            }
            
            return alert_data
        except Exception as e:
            return {
                'error': str(e),
                'monitor_id': monitor_id,
                'timestamp': datetime.now().isoformat()
            }
