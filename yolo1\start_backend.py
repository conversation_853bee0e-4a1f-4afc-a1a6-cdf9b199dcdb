# -*- coding: utf-8 -*-
# @Description : 启动后端服务
# @Date : 2025年6月21日

import os
import sys
import time
import subprocess
import threading

def start_basic_api():
    """启动基础API服务"""
    print("🚀 启动基础API服务 (端口5500)...")
    try:
        from frontend_api_server import create_app
        app = create_app()
        app.run(host='127.0.0.1', port=5500, debug=False)
    except Exception as e:
        print(f"❌ 基础API启动失败: {e}")

def start_detection_api():
    """启动检测API服务"""
    print("🚀 启动检测API服务 (端口5501)...")
    try:
        from core_detection_server import create_detection_app, detection_engine, stream_manager
        from websocket_server import start_websocket_server
        
        print("🌐 启动WebSocket服务器...")
        start_websocket_server()
        time.sleep(2)
        
        print("🎯 启动检测服务...")
        app = create_detection_app()
        app.run(host='127.0.0.1', port=5501, debug=False)
    except Exception as e:
        print(f"❌ 检测API启动失败: {e}")

def main():
    """主函数"""
    print("🎯 基于Yolov8与ByteTrack的高速公路智慧监控平台 - 后端启动")
    print("="*60)
    
    # 启动基础API服务
    api_thread = threading.Thread(target=start_basic_api, daemon=True)
    api_thread.start()
    
    time.sleep(3)
    
    # 启动检测API服务
    detection_thread = threading.Thread(target=start_detection_api, daemon=True)
    detection_thread.start()
    
    print("\n🎉 后端服务启动完成!")
    print("📋 服务地址:")
    print("   🌐 基础API: http://127.0.0.1:5500")
    print("   🎯 检测API: http://127.0.0.1:5501")
    print("   🌐 WebSocket: ws://127.0.0.1:5502")
    
    print("\n🔧 新增功能:")
    print("   ✅ YOLO目标检测")
    print("   ✅ 多目标追踪")
    print("   ✅ 违规检测 (违停、逆行)")
    print("   ✅ 事故检测 (碰撞、急停、拥堵)")
    print("   ✅ 交通分析统计")
    print("   ✅ WebSocket实时推送")
    
    print("\n⌨️ 按 Ctrl+C 退出")
    
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n👋 后端服务已关闭")

if __name__ == "__main__":
    main()
