-- =====================================================
-- 高速公路YOLOv8与ByteTrack智能监控系统 - MySQL 8.0最终版
-- 严格遵循后端API功能设计，支持前端开发计划
-- 创建时间: 2024-12-24
-- =====================================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 强制删除现有数据库并重新创建
DROP DATABASE IF EXISTS `yolo`;
CREATE DATABASE `yolo` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `yolo`;

-- =====================================================
-- 1. 用户管理表 (支持admin/operator两个角色)
-- =====================================================
CREATE TABLE `user` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `avatar` varchar(500) DEFAULT NULL COMMENT '头像URL',
  `grade` varchar(20) NOT NULL DEFAULT 'operator' COMMENT '用户级别(admin/operator)',
  `status` tinyint(1) DEFAULT 1 COMMENT '用户状态(0:禁用,1:启用)',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  KEY `idx_grade` (`grade`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- =====================================================
-- 2. 监控点管理表 (支持RTSP流和多目标追踪)
-- =====================================================
CREATE TABLE `monitor` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '监控点ID',
  `name` varchar(100) NOT NULL COMMENT '监控点名称',
  `location` varchar(100) NOT NULL COMMENT '监控地点',
  `highway_section` varchar(100) DEFAULT NULL COMMENT '高速公路路段',
  `camera_position` varchar(200) DEFAULT NULL COMMENT '摄像头位置说明',
  `latitude` decimal(10,7) DEFAULT NULL COMMENT '纬度',
  `longitude` decimal(10,7) DEFAULT NULL COMMENT '经度',
  `threshold` int NOT NULL DEFAULT 15 COMMENT '警报阈值',
  `conf_threshold` float DEFAULT 0.4 COMMENT '检测置信度阈值',
  `iou_threshold` float DEFAULT 0.5 COMMENT 'IOU阈值',
  `person` varchar(50) NOT NULL COMMENT '负责人',
  `video` varchar(500) DEFAULT NULL COMMENT '视频文件路径',
  `url` varchar(500) DEFAULT NULL COMMENT 'RTSP流地址',
  `rtsp_format` varchar(50) DEFAULT 'rtsp' COMMENT 'RTSP格式',
  `connection_status` varchar(20) DEFAULT 'unknown' COMMENT '连接状态(online/offline/error/unknown)',
  `is_alarm` varchar(10) DEFAULT '开启' COMMENT '警报状态(开启/关闭)',
  `mode` varchar(20) DEFAULT 'detection' COMMENT '工作模式(detection/tracking)',
  `show_labels` tinyint(1) DEFAULT 1 COMMENT '是否显示标签',
  `enable_tracking` tinyint(1) DEFAULT 0 COMMENT '是否启用多目标追踪',
  `tracker_type` varchar(20) DEFAULT 'bytetrack' COMMENT '追踪器类型(bytetrack/botsort)',
  `status` tinyint(1) DEFAULT 1 COMMENT '监控点状态(0:禁用,1:启用)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_location` (`location`),
  KEY `idx_highway_section` (`highway_section`),
  KEY `idx_connection_status` (`connection_status`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='监控点表';

-- =====================================================
-- 3. 统一数据记录表 (整合所有功能数据)
-- =====================================================
CREATE TABLE `unified_record` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `record_id` varchar(100) NOT NULL COMMENT '记录唯一标识',
  `record_type` varchar(50) NOT NULL COMMENT '记录类型(detection/tracking/accident/alarm/traffic)',
  `monitor_id` int NOT NULL COMMENT '监控点ID',
  `task_id` varchar(100) DEFAULT NULL COMMENT '任务ID(检测任务)',
  `track_id` int DEFAULT NULL COMMENT '追踪ID(追踪目标)',
  `object_type` varchar(50) DEFAULT NULL COMMENT '目标类型(car/truck/bus/person)',
  `confidence` float DEFAULT NULL COMMENT '置信度',
  `bbox_data` json DEFAULT NULL COMMENT '边界框数据(JSON格式)',
  `location_data` json DEFAULT NULL COMMENT '位置数据(JSON格式)',
  `detection_data` json DEFAULT NULL COMMENT '检测数据(JSON格式)',
  `tracking_data` json DEFAULT NULL COMMENT '追踪数据(JSON格式)',
  `accident_data` json DEFAULT NULL COMMENT '事故数据(JSON格式)',
  `alarm_data` json DEFAULT NULL COMMENT '警报数据(JSON格式)',
  `traffic_data` json DEFAULT NULL COMMENT '交通数据(JSON格式)',
  `severity` varchar(20) DEFAULT 'medium' COMMENT '严重程度(low/medium/high/critical)',
  `status` varchar(20) DEFAULT 'active' COMMENT '状态(active/pending/resolved/dismissed)',
  `description` text COMMENT '描述信息',
  `evidence_image` varchar(500) DEFAULT NULL COMMENT '证据图片路径',
  `evidence_video` varchar(500) DEFAULT NULL COMMENT '证据视频路径',
  `handled_by` varchar(50) DEFAULT NULL COMMENT '处理人',
  `handled_time` datetime DEFAULT NULL COMMENT '处理时间',
  `metadata` json DEFAULT NULL COMMENT '额外元数据',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_record_id` (`record_id`),
  KEY `idx_record_type` (`record_type`),
  KEY `idx_monitor_id` (`monitor_id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_track_id` (`track_id`),
  KEY `idx_object_type` (`object_type`),
  KEY `idx_severity` (`severity`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_unified_monitor` FOREIGN KEY (`monitor_id`) REFERENCES `monitor` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='统一数据记录表';

-- =====================================================
-- 4. 系统配置表
-- =====================================================
CREATE TABLE `system_config` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text COMMENT '配置值',
  `config_type` varchar(20) DEFAULT 'string' COMMENT '配置类型(string/int/float/bool/json)',
  `description` varchar(500) DEFAULT NULL COMMENT '配置描述',
  `category` varchar(50) DEFAULT 'general' COMMENT '配置分类',
  `is_editable` tinyint(1) DEFAULT 1 COMMENT '是否可编辑',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`),
  KEY `idx_category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- =====================================================
-- 5. 系统日志表
-- =====================================================
CREATE TABLE `system_log` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `log_level` varchar(10) NOT NULL COMMENT '日志级别(DEBUG/INFO/WARN/ERROR)',
  `module` varchar(50) DEFAULT NULL COMMENT '模块名称',
  `action` varchar(100) DEFAULT NULL COMMENT '操作动作',
  `message` text COMMENT '日志消息',
  `user_id` int DEFAULT NULL COMMENT '用户ID',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `request_data` text COMMENT '请求数据',
  `response_data` text COMMENT '响应数据',
  `execution_time` float DEFAULT NULL COMMENT '执行时间(毫秒)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_log_level` (`log_level`),
  KEY `idx_module` (`module`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统日志表';

-- =====================================================
-- 6. 插入测试数据
-- =====================================================

-- 插入用户数据 (明文密码，后端会处理)
INSERT INTO `user` (`username`, `password`, `email`, `grade`, `status`, `create_by`, `remark`) VALUES
('admin', '123456', '<EMAIL>', 'admin', 1, 'system', '系统管理员账号'),
('operator', '123456', '<EMAIL>', 'operator', 1, 'admin', '监控员账号');

-- 插入监控点数据 (杭州-千岛湖高速全程)
INSERT INTO `monitor` (`name`, `location`, `highway_section`, `camera_position`, `latitude`, `longitude`, `threshold`, `person`, `url`, `connection_status`, `is_alarm`, `mode`, `enable_tracking`, `tracker_type`, `create_by`, `remark`) VALUES
('杭州收费站监控点', '杭州收费站', 'K0+000', '收费站出入口', 30.2741, 120.1551, 15, 'admin', 'rtsp://admin:123456@192.168.1.101:554/stream1', 'online', '开启', 'tracking', 1, 'bytetrack', 'admin', '杭州收费站主要监控点'),
('杭州主线监控点', '杭州主线', 'K5+200', '主线车道', 30.2891, 120.1721, 20, 'admin', 'rtsp://admin:123456@192.168.1.102:554/stream1', 'online', '开启', 'tracking', 1, 'bytetrack', 'admin', '杭州主线交通监控'),
('富阳互通监控点', '富阳互通', 'K18+600', '互通匝道', 30.0498, 119.9528, 18, 'operator', 'rtsp://admin:123456@192.168.1.103:554/stream1', 'online', '开启', 'detection', 0, 'bytetrack', 'admin', '富阳互通交通监控'),
('桐庐服务区监控点', '桐庐服务区', 'K35+800', '服务区入口', 29.7971, 119.6811, 12, 'operator', 'rtsp://admin:123456@192.168.1.104:554/stream1', 'online', '开启', 'tracking', 1, 'bytetrack', 'admin', '桐庐服务区监控'),
('建德互通监控点', '建德互通', 'K58+900', '互通主线', 29.4765, 119.2876, 16, 'operator', 'rtsp://admin:123456@*************:554/stream1', 'online', '开启', 'detection', 0, 'bytetrack', 'admin', '建德互通监控'),
('千岛湖收费站监控点', '千岛湖收费站', 'K95+000', '收费站广场', 29.5678, 118.8765, 18, 'admin', 'rtsp://admin:123456@*************:554/stream1', 'online', '开启', 'tracking', 1, 'bytetrack', 'admin', '千岛湖收费站监控');

-- 插入系统配置数据
INSERT INTO `system_config` (`config_key`, `config_value`, `config_type`, `description`, `category`) VALUES
('system.name', '高速公路YOLOv8智能监控系统', 'string', '系统名称', 'general'),
('system.version', '2.0.0', 'string', '系统版本', 'general'),
('detection.default_conf_threshold', '0.4', 'float', '默认检测置信度阈值', 'detection'),
('detection.default_iou_threshold', '0.5', 'float', '默认IOU阈值', 'detection'),
('tracking.default_algorithm', 'bytetrack', 'string', '默认追踪算法', 'tracking'),
('accident.detection_enabled', 'true', 'bool', '是否启用事故检测', 'accident'),
('websocket.enabled', 'true', 'bool', '是否启用WebSocket实时推送', 'realtime'),
('video.max_streams', '6', 'int', '最大同时视频流数量', 'video');

-- 插入统一记录数据 (警报记录)
INSERT INTO `unified_record` (`record_id`, `record_type`, `monitor_id`, `object_type`, `confidence`, `alarm_data`, `severity`, `status`, `description`, `create_time`) VALUES
('ALARM_20241224_001', 'alarm', 1, 'vehicle_count', 0.85, '{"total_vehicles":17,"cars":12,"trucks":3,"buses":2,"threshold":15,"exceeded_by":2}', 'medium', 'resolved', '杭州收费站车流量超标：检测到17辆车，超过阈值15', '2024-12-24 08:15:30'),
('ALARM_20241224_002', 'alarm', 3, 'vehicle_count', 0.78, '{"total_vehicles":20,"cars":15,"trucks":3,"buses":2,"threshold":18,"exceeded_by":2}', 'medium', 'pending', '富阳互通车流量超标：检测到20辆车，超过阈值18', '2024-12-24 09:12:15'),
('ALARM_20241224_003', 'alarm', 6, 'accident', 0.92, '{"accident_type":"collision","confidence":0.92,"location":{"x":320,"y":240}}', 'high', 'processing', '千岛湖收费站检测到疑似交通事故', '2024-12-24 14:30:00');

-- 插入统一记录数据 (检测记录)
INSERT INTO `unified_record` (`record_id`, `record_type`, `monitor_id`, `task_id`, `object_type`, `confidence`, `detection_data`, `status`, `description`, `create_time`) VALUES
('DET_20241224_001', 'detection', 1, 'TASK_001', 'car', 0.89, '{"bbox":[100,100,200,200],"class_id":2,"class_name":"car"}', 'active', '杭州收费站检测到车辆', '2024-12-24 08:30:00'),
('DET_20241224_002', 'detection', 2, 'TASK_002', 'truck', 0.76, '{"bbox":[150,120,250,220],"class_id":7,"class_name":"truck"}', 'active', '杭州主线检测到卡车', '2024-12-24 09:45:00');

-- 插入统一记录数据 (追踪记录)
INSERT INTO `unified_record` (`record_id`, `record_type`, `monitor_id`, `track_id`, `object_type`, `confidence`, `tracking_data`, `status`, `description`, `create_time`) VALUES
('TRACK_20241224_001', 'tracking', 1, 1001, 'car', 0.91, '{"velocity":{"x":2.5,"y":0.1},"speed":65.2,"direction":90,"track_length":45}', 'active', '杭州收费站追踪目标1001', '2024-12-24 08:35:00'),
('TRACK_20241224_002', 'tracking', 4, 1002, 'bus', 0.87, '{"velocity":{"x":1.8,"y":-0.2},"speed":45.8,"direction":85,"track_length":32}', 'active', '桐庐服务区追踪目标1002', '2024-12-24 10:20:00');

-- 插入统一记录数据 (事故记录)
INSERT INTO `unified_record` (`record_id`, `record_type`, `monitor_id`, `object_type`, `confidence`, `accident_data`, `severity`, `status`, `description`, `evidence_image`, `create_time`) VALUES
('ACC_20241224_001', 'accident', 3, 'collision', 0.89, '{"accident_type":"collision","involved_vehicles":2,"location":{"x":320,"y":240},"estimated_damage":"minor"}', 'high', 'resolved', '富阳互通两车追尾事故，无人员伤亡', '/evidence/accident_001.jpg', '2024-12-24 09:15:23'),
('ACC_20241224_002', 'accident', 6, 'sudden_stop', 0.76, '{"accident_type":"sudden_stop","involved_vehicles":1,"location":{"x":450,"y":180},"cause":"emergency_braking"}', 'medium', 'resolved', '千岛湖收费站车辆紧急制动', '/evidence/accident_002.jpg', '2024-12-24 16:20:15');

-- 插入统一记录数据 (交通统计)
INSERT INTO `unified_record` (`record_id`, `record_type`, `monitor_id`, `traffic_data`, `status`, `description`, `create_time`) VALUES
('TRAFFIC_20241224_001', 'traffic', 1, '{"hour":8,"vehicle_count":245,"car_count":198,"truck_count":35,"bus_count":12,"avg_speed":85.5,"congestion_level":"normal"}', 'active', '杭州收费站8点交通统计', '2024-12-24 08:00:00'),
('TRAFFIC_20241224_002', 'traffic', 6, '{"hour":14,"vehicle_count":456,"car_count":365,"truck_count":67,"bus_count":24,"avg_speed":65.2,"congestion_level":"severe"}', 'active', '千岛湖收费站14点交通统计', '2024-12-24 14:00:00');

-- =====================================================
-- 7. 创建视图
-- =====================================================

-- 监控点状态视图
CREATE VIEW `v_monitor_status` AS
SELECT
    m.id,
    m.name,
    m.location,
    m.highway_section,
    m.connection_status,
    m.enable_tracking,
    m.threshold,
    COUNT(DISTINCT CASE WHEN ur.record_type = 'detection' AND ur.status = 'active' THEN ur.id END) as active_detections,
    COUNT(DISTINCT CASE WHEN ur.record_type = 'tracking' AND ur.status = 'active' THEN ur.id END) as active_targets,
    COUNT(DISTINCT CASE WHEN ur.record_type = 'accident' AND DATE(ur.create_time) = CURDATE() THEN ur.id END) as today_accidents,
    COUNT(DISTINCT CASE WHEN ur.record_type = 'alarm' AND ur.status IN ('pending', 'processing') THEN ur.id END) as active_alarms
FROM monitor m
LEFT JOIN unified_record ur ON m.id = ur.monitor_id
GROUP BY m.id;

-- 系统概览视图
CREATE VIEW `v_system_overview` AS
SELECT
    (SELECT COUNT(*) FROM monitor WHERE connection_status = 'online') as online_monitors,
    (SELECT COUNT(*) FROM monitor) as total_monitors,
    (SELECT COUNT(*) FROM unified_record WHERE record_type = 'detection' AND status = 'active') as active_detections,
    (SELECT COUNT(*) FROM unified_record WHERE record_type = 'tracking' AND status = 'active') as active_targets,
    (SELECT COUNT(*) FROM unified_record WHERE record_type = 'accident' AND DATE(create_time) = CURDATE()) as today_accidents,
    (SELECT COUNT(*) FROM unified_record WHERE record_type = 'alarm' AND status IN ('pending', 'processing')) as active_alarms,
    (SELECT COUNT(*) FROM user WHERE status = 1) as active_users;

-- 警报统计视图
CREATE VIEW `v_alarm_statistics` AS
SELECT
    DATE(ur.create_time) as stat_date,
    ur.severity,
    COUNT(*) as alarm_count,
    COUNT(CASE WHEN ur.status = 'resolved' THEN 1 END) as resolved_count,
    COUNT(CASE WHEN ur.status = 'pending' THEN 1 END) as pending_count
FROM unified_record ur
WHERE ur.record_type = 'alarm'
GROUP BY DATE(ur.create_time), ur.severity;

SET FOREIGN_KEY_CHECKS = 1;
