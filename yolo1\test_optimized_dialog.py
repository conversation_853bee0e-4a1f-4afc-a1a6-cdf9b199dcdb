#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化版多目标追踪对话框
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication
from ui.dialog.optimized_multi_tracking_dialog import OptimizedMultiTrackingDialog

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("YOLO多目标追踪系统")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("AI Vision Lab")
    
    # 创建并显示对话框
    dialog = OptimizedMultiTrackingDialog()
    
    # 连接信号处理
    dialog.tracking_started.connect(lambda targets: print(f"开始追踪目标: {targets}"))
    dialog.tracking_stopped.connect(lambda: print("停止追踪"))
    dialog.target_added.connect(lambda target_id: print(f"添加目标: {target_id}"))
    dialog.target_removed.connect(lambda target_id: print(f"移除目标: {target_id}"))
    
    # 显示对话框
    dialog.show()
    
    # 运行应用程序
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
