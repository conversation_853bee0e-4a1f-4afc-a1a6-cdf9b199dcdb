# -*- coding: utf-8 -*-
# @Description : 测试V1 API接口
# @Date : 2025年6月20日

import requests
import json

def test_v1_api():
    """测试V1 API接口"""
    base_url = "http://127.0.0.1:5500"
    
    print("🧪 测试V1 API接口")
    print("="*50)
    
    # 测试V1接口
    v1_tests = [
        ('GET', '/api/v1/docs', None, 'V1 API文档'),
        ('GET', '/api/v1/system/health-check', None, 'V1 健康检查'),
        ('POST', '/api/v1/auth/login', {'username': 'admin', 'password': '123456'}, 'V1 登录'),
        ('GET', '/api/v1/monitor/list', None, 'V1 监控点列表'),
        ('GET', '/api/v1/analysis/alarms', None, 'V1 警报列表'),
    ]
    
    for method, endpoint, data, description in v1_tests:
        url = f"{base_url}{endpoint}"
        
        try:
            if method == 'GET':
                response = requests.get(url, timeout=5)
            elif method == 'POST':
                response = requests.post(url, json=data, timeout=5)
            
            print(f"\n📋 {description}")
            print(f"🔗 {method} {endpoint}")
            print(f"📊 状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    response_data = response.json()
                    print("✅ 接口正常")
                    if 'data' in response_data:
                        print(f"📝 数据: {type(response_data['data'])}")
                except:
                    print("✅ 接口正常 (非JSON响应)")
            else:
                print("❌ 接口异常")
                print(f"📄 响应: {response.text[:200]}...")
                
        except Exception as e:
            print(f"\n📋 {description}")
            print(f"🔗 {method} {endpoint}")
            print(f"❌ 请求失败: {e}")

if __name__ == "__main__":
    test_v1_api()
