#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终登录测试 - 验证修复结果
"""

import requests
import json
import time

def test_login_final():
    """最终登录测试"""
    print("🚀 最终登录测试")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:5500"
    
    # 1. 测试服务器连接
    print("1. 测试服务器连接...")
    try:
        response = requests.get(base_url, timeout=5)
        if response.status_code == 200:
            print("   ✅ 服务器连接正常")
        else:
            print(f"   ❌ 服务器连接失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 服务器连接异常: {e}")
        return False
    
    # 2. 测试登录API
    print("\n2. 测试登录API...")
    login_data = {"username": "admin", "password": "123456"}
    
    try:
        response = requests.post(
            f"{base_url}/api/v1/auth/login", 
            json=login_data, 
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"   状态码: {response.status_code}")
        print(f"   响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"   响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                
                if data.get('success'):
                    print("   ✅ 登录成功！")
                    
                    # 获取token
                    token = data.get('data', {}).get('token')
                    if token:
                        print(f"   🔑 Token: {token[:50]}...")
                        
                        # 测试使用token访问受保护的API
                        return test_protected_api(base_url, token)
                    else:
                        print("   ⚠️ 未获取到Token")
                        return False
                else:
                    print(f"   ❌ 登录失败: {data.get('message')}")
                    return False
                    
            except json.JSONDecodeError as e:
                print(f"   ❌ JSON解析失败: {e}")
                print(f"   原始响应: {response.text}")
                return False
        else:
            print(f"   ❌ HTTP错误: {response.status_code}")
            print(f"   响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
        return False

def test_protected_api(base_url, token):
    """测试受保护的API"""
    print("\n3. 测试受保护的API...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # 测试用户信息API
    try:
        response = requests.get(f"{base_url}/api/v1/auth/profile", headers=headers, timeout=5)
        print(f"   用户信息API状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                user = data.get('data', {})
                print(f"   ✅ 用户信息: {user.get('username')} ({user.get('grade')})")
            else:
                print(f"   ❌ 用户信息获取失败: {data.get('message')}")
        else:
            print(f"   ⚠️ 用户信息API错误: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 用户信息API异常: {e}")
    
    # 测试概览统计API
    try:
        response = requests.get(f"{base_url}/api/v1/analysis/statistics/overview", headers=headers, timeout=5)
        print(f"   概览统计API状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                stats = data.get('data', {})
                print(f"   ✅ 概览统计: {list(stats.keys())}")
            else:
                print(f"   ❌ 概览统计获取失败: {data.get('message')}")
        else:
            print(f"   ⚠️ 概览统计API错误: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 概览统计API异常: {e}")
    
    return True

def test_frontend_connection():
    """测试前端连接"""
    print("\n4. 模拟前端连接测试...")
    
    base_url = "http://127.0.0.1:5500"
    
    # 模拟前端的完整登录流程
    print("   模拟前端登录流程...")
    
    # 1. 登录获取token
    login_data = {"username": "admin", "password": "123456"}
    response = requests.post(f"{base_url}/api/v1/auth/login", json=login_data)
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            token = data.get('data', {}).get('token')
            print(f"   ✅ 前端登录成功，Token: {token[:30]}...")
            
            # 2. 使用token访问各个API
            headers = {"Authorization": f"Bearer {token}"}
            
            apis_to_test = [
                ("/api/v1/auth/profile", "用户信息"),
                ("/api/v1/monitor/list", "监控点列表"),
                ("/api/v1/analysis/statistics/overview", "概览统计")
            ]
            
            success_count = 0
            for api_path, api_name in apis_to_test:
                try:
                    response = requests.get(f"{base_url}{api_path}", headers=headers, timeout=5)
                    if response.status_code == 200:
                        data = response.json()
                        if data.get('success'):
                            print(f"   ✅ {api_name}: 成功")
                            success_count += 1
                        else:
                            print(f"   ❌ {api_name}: {data.get('message')}")
                    else:
                        print(f"   ❌ {api_name}: HTTP {response.status_code}")
                except Exception as e:
                    print(f"   ❌ {api_name}: 异常 - {e}")
            
            print(f"   📊 API测试结果: {success_count}/{len(apis_to_test)} 成功")
            return success_count == len(apis_to_test)
        else:
            print(f"   ❌ 前端登录失败: {data.get('message')}")
            return False
    else:
        print(f"   ❌ 前端登录HTTP错误: {response.status_code}")
        return False

def main():
    """主函数"""
    print("=" * 80)
    print("🎯 高速公路智能监控系统 - 最终登录测试")
    print("=" * 80)
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    time.sleep(2)
    
    # 执行测试
    login_success = test_login_final()
    
    if login_success:
        frontend_success = test_frontend_connection()
        
        print("\n" + "=" * 80)
        if frontend_success:
            print("🎉 所有测试通过！")
            print("✅ 后端服务正常运行")
            print("✅ 登录功能正常工作")
            print("✅ 前端可以正常连接后端")
            print("✅ 所有API接口正常")
            print("\n💡 现在可以在前端测试页面进行测试:")
            print("   http://localhost:3000/business-test")
        else:
            print("⚠️ 登录成功但部分API有问题")
    else:
        print("\n" + "=" * 80)
        print("❌ 登录测试失败")
        print("💡 请检查:")
        print("   1. 后端服务是否正常启动")
        print("   2. 数据库连接是否正常")
        print("   3. 用户数据是否存在")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
