<template>
  <div class="detection-main">
    <div class="header">
      <h2>检测中心</h2>
      <a-button @click="refresh">刷新</a-button>
    </div>

    <a-tabs v-model:active-key="tab" type="card">
      <a-tab-pane key="image" title="图像检测">
        <a-card title="图像检测">
          <a-row :gutter="24">
            <a-col :span="12">
              <div class="upload">
                <a-upload
                  :custom-request="upload"
                  :show-file-list="false"
                  accept="image/*"
                >
                  <template #upload-button>
                    <div class="zone">
                      <p>点击上传图片</p>
                    </div>
                  </template>
                </a-upload>
              </div>
            </a-col>
            <a-col :span="12">
              <div class="result">
                <h4>检测结果</h4>
                <div v-if="loading">
                  <a-spin />
                  <p>检测中...</p>
                </div>
                <div v-else-if="data">
                  <img :src="data.img" class="img" />
                  <p>检测到 {{ data.count }} 个对象</p>
                </div>
                <a-empty v-else />
              </div>
            </a-col>
          </a-row>
        </a-card>
      </a-tab-pane>

      <a-tab-pane key="video" title="视频检测">
        <a-card title="视频检测">
          <a-row :gutter="24">
            <a-col :span="12">
              <div class="upload">
                <a-upload
                  :custom-request="uploadVideo"
                  :show-file-list="false"
                  accept="video/*"
                  :disabled="videoLoading"
                >
                  <template #upload-button>
                    <div class="zone">
                      <p>点击上传视频</p>
                      <p class="hint">支持 MP4、AVI 格式</p>
                    </div>
                  </template>
                </a-upload>

                <div class="params">
                  <h4>检测参数</h4>
                  <a-form layout="vertical">
                    <a-row :gutter="16">
                      <a-col :span="12">
                        <a-form-item label="置信度阈值">
                          <a-slider v-model="videoParams.confidence" :min="0.1" :max="1" :step="0.1" />
                        </a-form-item>
                      </a-col>
                      <a-col :span="12">
                        <a-form-item label="IOU阈值">
                          <a-slider v-model="videoParams.iou" :min="0.1" :max="1" :step="0.1" />
                        </a-form-item>
                      </a-col>
                    </a-row>
                  </a-form>
                </div>
              </div>
            </a-col>
            <a-col :span="12">
              <div class="result">
                <h4>检测结果</h4>
                <div v-if="videoLoading">
                  <a-spin />
                  <p>视频检测中...</p>
                </div>
                <div v-else-if="videoResult">
                  <video :src="videoResult.video" controls class="video" />
                  <div class="stats">
                    <a-statistic title="检测帧数" :value="videoResult.frames" suffix="帧" />
                    <a-statistic title="检测对象" :value="videoResult.count" suffix="个" />
                  </div>
                </div>
                <a-empty v-else description="请上传视频进行检测" />
              </div>
            </a-col>
          </a-row>
        </a-card>
      </a-tab-pane>

      <a-tab-pane key="rtsp" title="RTSP检测">
        <a-card title="RTSP检测">
          <!-- 监控点状态 -->
          <div class="monitor-status">
            <h4>监控点状态</h4>
            <div class="monitor-grid">
              <div
                v-for="monitor in monitors"
                :key="monitor.id"
                class="monitor-item"
                :class="{ 'online': monitor.connection_status === 'online', 'offline': monitor.connection_status === 'offline' }"
              >
                <div class="monitor-name">{{ monitor.name }}</div>
                <div class="monitor-location">{{ monitor.location }}</div>
                <div class="monitor-status-badge">
                  <a-tag :color="monitor.connection_status === 'online' ? 'green' : 'red'">
                    {{ monitor.connection_status === 'online' ? '在线' : '离线' }}
                  </a-tag>
                </div>
              </div>
            </div>
          </div>

          <a-divider />

          <!-- RTSP检测控制 -->
          <a-row :gutter="24">
            <a-col :span="12">
              <div class="rtsp-control">
                <h4>检测控制</h4>
                <a-form layout="vertical">
                  <a-form-item label="选择监控点">
                    <a-select
                      v-model="selectedMonitor"
                      placeholder="请选择监控点"
                      :loading="monitorLoading"
                    >
                      <a-option
                        v-for="monitor in onlineMonitors"
                        :key="monitor.id"
                        :value="monitor.id"
                      >
                        {{ monitor.name }} - {{ monitor.location }}
                      </a-option>
                    </a-select>
                  </a-form-item>

                  <a-row :gutter="16">
                    <a-col :span="12">
                      <a-form-item label="置信度阈值">
                        <a-slider v-model="rtspParams.confidence" :min="0.1" :max="1" :step="0.1" />
                      </a-form-item>
                    </a-col>
                    <a-col :span="12">
                      <a-form-item label="IOU阈值">
                        <a-slider v-model="rtspParams.iou" :min="0.1" :max="1" :step="0.1" />
                      </a-form-item>
                    </a-col>
                  </a-row>

                  <a-form-item>
                    <a-checkbox v-model="rtspParams.enableTracking">启用目标追踪</a-checkbox>
                  </a-form-item>

                  <a-form-item>
                    <a-space>
                      <a-button
                        type="primary"
                        @click="startRTSP"
                        :loading="rtspLoading"
                        :disabled="!selectedMonitor"
                      >
                        开始检测
                      </a-button>
                      <a-button @click="stopRTSP" :disabled="!rtspActive">
                        停止检测
                      </a-button>
                    </a-space>
                  </a-form-item>
                </a-form>
              </div>
            </a-col>
            <a-col :span="12">
              <div class="rtsp-display">
                <h4>实时检测</h4>
                <div v-if="rtspActive" class="rtsp-active">
                  <div class="rtsp-info">
                    <a-tag color="green">检测中</a-tag>
                    <span>{{ selectedMonitorName }}</span>
                  </div>
                  <div class="rtsp-placeholder">
                    <p>实时检测画面</p>
                    <p>WebSocket连接: {{ rtspActive ? '已连接' : '未连接' }}</p>
                  </div>
                </div>
                <a-empty v-else description="请选择监控点并开始检测" />
              </div>
            </a-col>
          </a-row>
        </a-card>
      </a-tab-pane>

      <a-tab-pane key="tasks" title="任务管理">
        <a-card title="任务管理">
          <div class="task-controls">
            <a-space>
              <a-button @click="refreshTasks" :loading="taskLoading">
                <template #icon><icon-refresh /></template>
                刷新任务
              </a-button>
              <a-select v-model="taskFilter" placeholder="筛选任务类型" style="width: 120px">
                <a-option value="">全部</a-option>
                <a-option value="image">图像检测</a-option>
                <a-option value="video">视频检测</a-option>
                <a-option value="rtsp">RTSP检测</a-option>
              </a-select>
              <a-select v-model="statusFilter" placeholder="筛选状态" style="width: 120px">
                <a-option value="">全部状态</a-option>
                <a-option value="running">运行中</a-option>
                <a-option value="completed">已完成</a-option>
                <a-option value="failed">失败</a-option>
              </a-select>
            </a-space>
          </div>

          <a-table
            :columns="taskColumns"
            :data="filteredTasks"
            :loading="taskLoading"
            :pagination="{ pageSize: 10 }"
          >
            <template #type="{ record }">
              <a-tag :color="getTaskTypeColor(record.type)">
                {{ getTaskTypeName(record.type) }}
              </a-tag>
            </template>

            <template #status="{ record }">
              <a-tag :color="getTaskStatusColor(record.status)">
                {{ getTaskStatusName(record.status) }}
              </a-tag>
            </template>

            <template #progress="{ record }">
              <a-progress
                :percent="record.progress"
                :status="record.status === 'failed' ? 'danger' : 'normal'"
                size="small"
              />
            </template>

            <template #actions="{ record }">
              <a-space>
                <a-button
                  v-if="record.status === 'running'"
                  type="text"
                  size="small"
                  @click="stopTask(record.task_id)"
                >
                  停止
                </a-button>
                <a-button
                  v-if="record.status === 'completed'"
                  type="text"
                  size="small"
                  @click="viewResult(record)"
                >
                  查看结果
                </a-button>
              </a-space>
            </template>
          </a-table>
        </a-card>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import {
  IconRefresh,
  IconCloudUpload
} from '@arco-design/web-vue/es/icon'
import { monitorApi, type Monitor } from '@/api/monitor'

// 响应式数据
const tab = ref('image')
const loading = ref(false)
const data = ref<any>(null)

// 视频检测
const videoLoading = ref(false)
const videoResult = ref<any>(null)

// RTSP检测
const rtspLoading = ref(false)
const rtspActive = ref(false)
const selectedMonitor = ref<number | null>(null)
const monitorLoading = ref(false)
const monitors = ref<Monitor[]>([])

// 任务管理
const taskLoading = ref(false)
const tasks = ref<any[]>([])
const taskFilter = ref('')
const statusFilter = ref('')

// 检测参数
const params = reactive({
  confidence: 0.5,
  iou: 0.4
})

const videoParams = reactive({
  confidence: 0.4,
  iou: 0.5
})

const rtspParams = reactive({
  confidence: 0.5,
  iou: 0.4,
  enableTracking: true
})

// 计算属性
const onlineMonitors = computed(() =>
  monitors.value.filter(m => m.connection_status === 'online')
)

const selectedMonitorName = computed(() => {
  const monitor = monitors.value.find(m => m.id === selectedMonitor.value)
  return monitor ? `${monitor.name} - ${monitor.location}` : ''
})

const filteredTasks = computed(() => {
  let filtered = tasks.value
  if (taskFilter.value) {
    filtered = filtered.filter(task => task.type === taskFilter.value)
  }
  if (statusFilter.value) {
    filtered = filtered.filter(task => task.status === statusFilter.value)
  }
  return filtered
})

// 任务表格列定义
const taskColumns = [
  { title: '任务ID', dataIndex: 'task_id', width: 150 },
  { title: '类型', dataIndex: 'type', slotName: 'type', width: 100 },
  { title: '状态', dataIndex: 'status', slotName: 'status', width: 100 },
  { title: '进度', dataIndex: 'progress', slotName: 'progress', width: 120 },
  { title: '开始时间', dataIndex: 'start_time', width: 160 },
  { title: '操作', slotName: 'actions', width: 120 }
]

// 方法
const refresh = () => {
  Message.success('刷新成功')
}

// 图像上传
const upload = async (option: any) => {
  const { file } = option

  if (!file.type.startsWith('image/')) {
    Message.error('请上传图片')
    return
  }

  loading.value = true

  setTimeout(() => {
    data.value = {
      img: URL.createObjectURL(file),
      count: Math.floor(Math.random() * 5) + 1
    }
    loading.value = false
    Message.success('检测完成')
  }, 1500)
}

// 视频上传
const uploadVideo = async (option: any) => {
  const { file } = option

  if (!file.type.startsWith('video/')) {
    Message.error('请上传视频文件')
    return
  }

  if (file.size > 100 * 1024 * 1024) {
    Message.error('视频文件大小不能超过 100MB')
    return
  }

  videoLoading.value = true

  setTimeout(() => {
    videoResult.value = {
      video: URL.createObjectURL(file),
      frames: Math.floor(Math.random() * 500) + 100,
      count: Math.floor(Math.random() * 20) + 5
    }
    videoLoading.value = false
    Message.success('视频检测完成')
  }, 3000)
}

// 获取监控点列表
const fetchMonitors = async () => {
  monitorLoading.value = true
  try {
    const response = await monitorApi.getMonitorList({ page: 1, size: 100 })
    if (response.success) {
      monitors.value = response.data.monitors || []
      Message.success(`加载了 ${monitors.value.length} 个监控点`)
    } else {
      Message.error('获取监控点失败: ' + response.message)
    }
  } catch (error: any) {
    Message.error('获取监控点失败: ' + (error.message || '网络错误'))
    // 使用模拟数据
    monitors.value = [
      {
        id: 1,
        name: '杭州收费站监控点0',
        location: '杭州收费站',
        highway_section: 'K0+000',
        camera_position: '收费站出入口',
        latitude: 30.2741,
        longitude: 120.1551,
        threshold: 15,
        person: 'admin',
        url: 'rtsp://admin:123456@192.168.1.101:554/stream1',
        connection_status: 'online',
        is_alarm: '开启',
        mode: 'tracking',
        enable_tracking: true,
        tracker_type: 'bytetrack',
        status: 1,
        create_time: '2024-01-01T00:00:00'
      },
      {
        id: 2,
        name: '富阳互通监控点1',
        location: '富阳互通',
        highway_section: 'K15+200',
        camera_position: '互通匝道',
        latitude: 30.0498,
        longitude: 119.9528,
        threshold: 20,
        person: 'operator',
        url: 'rtsp://admin:123456@192.168.1.102:554/stream1',
        connection_status: 'online',
        is_alarm: '开启',
        mode: 'detection',
        enable_tracking: false,
        tracker_type: 'bytetrack',
        status: 1,
        create_time: '2024-01-01T00:00:00'
      },
      {
        id: 3,
        name: '桐庐服务区监控点2',
        location: '桐庐服务区',
        highway_section: 'K32+500',
        camera_position: '服务区入口',
        latitude: 29.7977,
        longitude: 119.6811,
        threshold: 18,
        person: 'admin',
        url: 'rtsp://admin:123456@192.168.1.103:554/stream1',
        connection_status: 'offline',
        is_alarm: '关闭',
        mode: 'tracking',
        enable_tracking: true,
        tracker_type: 'botsort',
        status: 0,
        create_time: '2024-01-01T00:00:00'
      },
      {
        id: 4,
        name: '临安出口监控点3',
        location: '临安出口',
        highway_section: 'K45+800',
        camera_position: '出口匝道',
        latitude: 30.2341,
        longitude: 119.7245,
        threshold: 16,
        person: 'operator',
        url: 'rtsp://admin:123456@192.168.1.104:554/stream1',
        connection_status: 'online',
        is_alarm: '开启',
        mode: 'detection',
        enable_tracking: true,
        tracker_type: 'bytetrack',
        status: 1,
        create_time: '2024-01-01T00:00:00'
      },
      {
        id: 5,
        name: '建德服务区监控点4',
        location: '建德服务区',
        highway_section: 'K58+300',
        camera_position: '服务区出口',
        latitude: 29.4765,
        longitude: 119.2812,
        threshold: 17,
        person: 'admin',
        url: 'rtsp://admin:123456@192.168.1.105:554/stream1',
        connection_status: 'online',
        is_alarm: '开启',
        mode: 'tracking',
        enable_tracking: true,
        tracker_type: 'botsort',
        status: 1,
        create_time: '2024-01-01T00:00:00'
      },
      {
        id: 6,
        name: '千岛湖收费站监控点5',
        location: '千岛湖收费站',
        highway_section: 'K72+100',
        camera_position: '收费站入口',
        latitude: 29.6057,
        longitude: 118.9512,
        threshold: 19,
        person: 'operator',
        url: 'rtsp://admin:123456@192.168.1.106:554/stream1',
        connection_status: 'online',
        is_alarm: '开启',
        mode: 'detection',
        enable_tracking: false,
        tracker_type: 'bytetrack',
        status: 1,
        create_time: '2024-01-01T00:00:00'
      }
    ]
  } finally {
    monitorLoading.value = false
  }
}

// RTSP检测控制
const startRTSP = async () => {
  if (!selectedMonitor.value) {
    Message.warning('请选择监控点')
    return
  }

  rtspLoading.value = true
  try {
    // 模拟启动RTSP检测
    await new Promise(resolve => setTimeout(resolve, 1000))
    rtspActive.value = true
    Message.success('RTSP检测已开始')
  } catch (error) {
    Message.error('启动RTSP检测失败')
  } finally {
    rtspLoading.value = false
  }
}

const stopRTSP = () => {
  rtspActive.value = false
  Message.success('RTSP检测已停止')
}

// 任务管理
const refreshTasks = () => {
  taskLoading.value = true
  // 模拟任务数据
  setTimeout(() => {
    tasks.value = [
      {
        task_id: 'TASK_001',
        type: 'image',
        status: 'completed',
        progress: 100,
        start_time: '2024-12-24 14:30:00'
      },
      {
        task_id: 'TASK_002',
        type: 'video',
        status: 'running',
        progress: 65,
        start_time: '2024-12-24 15:00:00'
      },
      {
        task_id: 'TASK_003',
        type: 'rtsp',
        status: 'running',
        progress: 80,
        start_time: '2024-12-24 15:15:00'
      }
    ]
    taskLoading.value = false
    Message.success('任务列表已刷新')
  }, 1000)
}

const stopTask = (taskId: string) => {
  const task = tasks.value.find(t => t.task_id === taskId)
  if (task) {
    task.status = 'failed'
    task.progress = 0
    Message.success('任务已停止')
  }
}

const viewResult = (task: any) => {
  Message.info(`查看任务 ${task.task_id} 的结果`)
}

// 工具方法
const getTaskTypeColor = (type: string) => {
  switch (type) {
    case 'image': return 'blue'
    case 'video': return 'green'
    case 'rtsp': return 'orange'
    default: return 'gray'
  }
}

const getTaskTypeName = (type: string) => {
  switch (type) {
    case 'image': return '图像检测'
    case 'video': return '视频检测'
    case 'rtsp': return 'RTSP检测'
    default: return '未知'
  }
}

const getTaskStatusColor = (status: string) => {
  switch (status) {
    case 'running': return 'blue'
    case 'completed': return 'green'
    case 'failed': return 'red'
    default: return 'gray'
  }
}

const getTaskStatusName = (status: string) => {
  switch (status) {
    case 'running': return '运行中'
    case 'completed': return '已完成'
    case 'failed': return '失败'
    default: return '未知'
  }
}

// 生命周期
onMounted(() => {
  fetchMonitors()
  refreshTasks()
})
</script>

<style scoped>
.detection-main {
  padding: 0;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.header h2 {
  margin: 0;
}

.zone {
  padding: 40px;
  border: 2px dashed #ccc;
  text-align: center;
  cursor: pointer;
}

.zone:hover {
  border-color: #1890ff;
}

.img {
  max-width: 100%;
  max-height: 200px;
}

.result {
  text-align: center;
}

.hint {
  font-size: 12px !important;
  color: #86909c !important;
}

.params {
  margin-top: 16px;
  padding: 16px;
  background: #f7f8fa;
  border-radius: 8px;
}

.params h4 {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
}

.video {
  max-width: 100%;
  max-height: 200px;
  border-radius: 8px;
}

.stats {
  display: flex;
  justify-content: space-around;
  margin-top: 16px;
}

/* 监控点状态样式 */
.monitor-status h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.monitor-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
  margin-bottom: 16px;
}

.monitor-item {
  padding: 12px;
  border: 1px solid #e5e6eb;
  border-radius: 8px;
  background: white;
  transition: all 0.3s ease;
}

.monitor-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.monitor-item.online {
  border-color: #00b42a;
  background: #f6ffed;
}

.monitor-item.offline {
  border-color: #f53f3f;
  background: #fff1f0;
}

.monitor-name {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 4px;
  color: #1d2129;
}

.monitor-location {
  font-size: 12px;
  color: #86909c;
  margin-bottom: 8px;
}

.monitor-status-badge {
  text-align: right;
}

/* RTSP控制样式 */
.rtsp-control h4 {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
}

.rtsp-display h4 {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
}

.rtsp-active {
  text-align: center;
}

.rtsp-info {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.rtsp-placeholder {
  padding: 40px 20px;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  background: #fafafa;
}

.rtsp-placeholder p {
  margin: 4px 0;
  color: #86909c;
}

/* 任务管理样式 */
.task-controls {
  margin-bottom: 16px;
}
</style>
