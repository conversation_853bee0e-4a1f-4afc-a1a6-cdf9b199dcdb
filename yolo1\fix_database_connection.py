#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复数据库连接问题
"""

import os
import sys
import pymysql
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(override=True, dotenv_path='config/end-back.env')

def test_direct_connection():
    """测试直接数据库连接"""
    print("🔍 测试直接数据库连接")
    print("=" * 50)
    
    config = {
        'host': os.getenv('MYSQL_HOST', '127.0.0.1'),
        'port': int(os.getenv('MYSQL_PORT', 3306)),
        'user': os.getenv('MYSQL_USER', os.getenv('MYSQL_user', 'root')),
        'password': os.getenv('MYSQL_PASSWORD', os.getenv('MYSQL_password', '123456')),
        'database': os.getenv('MYSQL_DATABASE', os.getenv('MYSQL_db', 'yolo')),
        'charset': 'utf8mb4',
        'autocommit': True,
        'cursorclass': pymysql.cursors.DictCursor
    }
    
    print(f"数据库配置: {config}")
    
    try:
        # 测试直接连接
        connection = pymysql.connect(**config)
        print("✅ 直接连接成功")
        
        with connection.cursor() as cursor:
            # 测试查询
            cursor.execute("SELECT 1 as test")
            result = cursor.fetchone()
            print(f"✅ 查询测试: {result}")
            
            # 测试用户表
            cursor.execute("SELECT COUNT(*) as count FROM user")
            count = cursor.fetchone()
            print(f"✅ 用户表记录数: {count}")
            
            # 测试用户查询
            cursor.execute("SELECT * FROM user WHERE username=%s AND password=%s", ("admin", "123456"))
            user = cursor.fetchone()
            print(f"✅ 用户查询结果: {user}")
            
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 直接连接失败: {e}")
        return False

def test_backend_connection():
    """测试后端数据库连接"""
    print("\n🔍 测试后端数据库连接")
    print("=" * 50)
    
    try:
        # 添加backend路径
        sys.path.append('backend')
        
        from utils.database import DatabaseManager, get_db_connection
        
        print("✅ 数据库模块导入成功")
        
        # 测试数据库管理器
        db_manager = DatabaseManager()
        print(f"数据库管理器配置: {db_manager.config}")
        
        # 测试管理器连接
        try:
            connection = db_manager.get_connection()
            print(f"管理器连接结果: {connection}")
            print(f"连接类型: {type(connection)}")
            
            if connection:
                print("✅ 数据库管理器连接成功")
                connection.close()
            else:
                print("❌ 数据库管理器返回None")
                return False
                
        except Exception as e:
            print(f"❌ 数据库管理器连接失败: {e}")
            return False
        
        # 测试上下文管理器
        try:
            print("\n测试上下文管理器...")
            with get_db_connection() as db:
                print(f"上下文管理器返回: {db}")
                print(f"返回类型: {type(db)}")
                
                if db is None:
                    print("❌ 上下文管理器返回None")
                    return False
                
                # 测试方法
                if hasattr(db, 'get_one'):
                    print("✅ 有get_one方法")
                    result = db.get_one("SELECT 1 as test")
                    print(f"✅ 查询成功: {result}")
                else:
                    print("❌ 没有get_one方法")
                    return False
                    
        except Exception as e:
            print(f"❌ 上下文管理器失败: {e}")
            import traceback
            traceback.print_exc()
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ 后端模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_simple_database_wrapper():
    """创建简单的数据库包装器"""
    print("\n🔧 创建简单数据库包装器")
    print("=" * 50)
    
    wrapper_code = '''# -*- coding: utf-8 -*-
"""
简单数据库包装器 - 修复版本
"""

import os
import pymysql
from contextlib import contextmanager
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(override=True, dotenv_path='config/end-back.env')

class SimpleDatabaseConnection:
    """简单数据库连接类"""
    
    def __init__(self, connection):
        if connection is None:
            raise Exception("数据库连接不能为None")
        self.connection = connection
        self.cursor = connection.cursor()
    
    def get_one(self, sql, params=None):
        """查询单条记录"""
        self.cursor.execute(sql, params)
        return self.cursor.fetchone()
    
    def get_list(self, sql, params=None):
        """查询多条记录"""
        self.cursor.execute(sql, params)
        return self.cursor.fetchall()
    
    def execute(self, sql, params=None):
        """执行SQL"""
        return self.cursor.execute(sql, params)
    
    def fetchone(self):
        """获取单条记录"""
        return self.cursor.fetchone()
    
    def fetchall(self):
        """获取所有记录"""
        return self.cursor.fetchall()
    
    def create(self, sql, params=None):
        """插入数据"""
        self.cursor.execute(sql, params)
        self.connection.commit()
        return self.cursor.lastrowid
    
    def modify(self, sql, params=None):
        """修改数据"""
        self.cursor.execute(sql, params)
        self.connection.commit()
        return self.cursor.rowcount
    
    def close(self):
        """关闭连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()

@contextmanager
def get_simple_db_connection():
    """获取简单数据库连接"""
    config = {
        'host': os.getenv('MYSQL_HOST', '127.0.0.1'),
        'port': int(os.getenv('MYSQL_PORT', 3306)),
        'user': os.getenv('MYSQL_USER', os.getenv('MYSQL_user', 'root')),
        'password': os.getenv('MYSQL_PASSWORD', os.getenv('MYSQL_password', '123456')),
        'database': os.getenv('MYSQL_DATABASE', os.getenv('MYSQL_db', 'yolo')),
        'charset': 'utf8mb4',
        'autocommit': True,
        'cursorclass': pymysql.cursors.DictCursor
    }
    
    connection = None
    db = None
    try:
        connection = pymysql.connect(**config)
        db = SimpleDatabaseConnection(connection)
        yield db
    except Exception as e:
        raise Exception(f"数据库连接失败: {str(e)}")
    finally:
        if db:
            db.close()
        elif connection:
            connection.close()
'''
    
    # 写入文件
    with open('backend/utils/simple_database.py', 'w', encoding='utf-8') as f:
        f.write(wrapper_code)
    
    print("✅ 简单数据库包装器已创建: backend/utils/simple_database.py")

def main():
    """主函数"""
    print("=" * 80)
    print("🚀 数据库连接问题修复")
    print("=" * 80)
    
    # 1. 测试直接连接
    if not test_direct_connection():
        print("\n❌ 直接数据库连接失败，请检查数据库配置")
        return
    
    # 2. 测试后端连接
    if not test_backend_connection():
        print("\n❌ 后端数据库连接失败，创建简单包装器")
        create_simple_database_wrapper()
    else:
        print("\n✅ 后端数据库连接正常")
    
    print("\n" + "=" * 80)
    print("📋 修复完成")
    print("=" * 80)

if __name__ == "__main__":
    main()
