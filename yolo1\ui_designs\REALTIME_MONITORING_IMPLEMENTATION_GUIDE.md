# 实时监控界面 - UI设计实现指南

## 🎨 设计概览

本文档详细说明了如何将实时监控界面的UI设计转换为实际的前端代码实现。该界面专注于实时视频流监控、目标检测结果展示和系统控制功能。

### 设计特点
- **实时视频流展示**：大屏幕视频播放区域，支持多种控制操作
- **智能检测结果**：实时显示检测到的车辆、行人等目标信息
- **动态性能指标**：实时更新的FPS、延迟、CPU/GPU使用率
- **直观控制面板**：一键式操作按钮和参数调节滑块
- **状态指示系统**：清晰的在线状态和系统运行状态显示
- **多目标追踪集成**：无缝切换至多目标追踪界面，实现跨摄像头追踪。

## 🔗 关联系统

- **[多目标追踪系统](./MULTI_TARGET_TRACKING_UI_GUIDE.md)**：点击查看详细的多目标追踪系统UI设计指南。

## 🎯 核心功能模块

### 1. 视频流控制模块
- **实时视频播放**：支持RTSP/HTTP流媒体播放
- **检测框渲染**：实时绘制目标检测框和标签
- **轨迹追踪显示**：动态显示目标移动轨迹
- **视频控制操作**：播放、暂停、停止、截图、录制

### 2. 检测结果展示模块
- **目标统计**：实时统计各类目标数量
- **性能监控**：显示检测置信度和处理速度
- **趋势分析**：小型图表显示数据变化趋势
- **状态指示**：安全状态和异常情况提醒

### 3. 参数控制模块
- **阈值调节**：置信度和IOU阈值滑块控制
- **显示选项**：标签、轨迹、结果保存开关
- **算法选择**：支持多种检测算法切换
- **监控点管理**：多监控点选择和状态监控

## 🏗️ 组件架构设计

### 1. 主容器组件 (`RealtimeMonitoring.vue`)

```vue
<template>
  <div class="realtime-monitoring">
    <!-- Header -->
    <MonitoringHeader 
      :current-monitor="currentMonitor"
      :system-status="systemStatus"
      :performance-metrics="performanceMetrics"
      @monitor-change="handleMonitorChange"
      @algorithm-change="handleAlgorithmChange"
    />
    
    <!-- Main Content -->
    <div class="monitoring-content">
      <!-- Video Stream Section -->
      <VideoStreamPanel 
        :stream-url="streamUrl"
        :detection-results="detectionResults"
        :show-labels="showLabels"
        :show-tracks="showTracks"
        :is-recording="isRecording"
        @play="handlePlay"
        @pause="handlePause"
        @stop="handleStop"
        @screenshot="handleScreenshot"
        @record="handleRecord"
      />
      
      <!-- Detection Results Panel -->
      <DetectionResultsPanel 
        :detection-stats="detectionStats"
        :performance-data="performanceData"
        :confidence-score="confidenceScore"
        :processing-speed="processingSpeed"
      />
    </div>
    
    <!-- Control Panel -->
    <ControlPanel 
      :is-detecting="isDetecting"
      :confidence-threshold="confidenceThreshold"
      :iou-threshold="iouThreshold"
      :display-options="displayOptions"
      @start-detection="startDetection"
      @pause-detection="pauseDetection"
      @stop-detection="stopDetection"
      @threshold-change="handleThresholdChange"
      @option-change="handleOptionChange"
    />
    
    <!-- Status Bar -->
    <StatusBar 
      :system-status="systemStatus"
      :connection-status="connectionStatus"
      :last-update="lastUpdate"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useWebSocket } from '@/composables/useWebSocket'
import { useVideoStream } from '@/composables/useVideoStream'
import { useDetection } from '@/composables/useDetection'

// 组件引入
import MonitoringHeader from './components/MonitoringHeader.vue'
import VideoStreamPanel from './components/VideoStreamPanel.vue'
import DetectionResultsPanel from './components/DetectionResultsPanel.vue'
import ControlPanel from './components/ControlPanel.vue'
import StatusBar from './components/StatusBar.vue'

// 响应式数据
const currentMonitor = ref('监控点001')
const systemStatus = ref('正常运行')
const isDetecting = ref(false)
const isRecording = ref(false)
const streamUrl = ref('')

// 检测参数
const confidenceThreshold = ref(0.5)
const iouThreshold = ref(0.4)
const displayOptions = reactive({
  showLabels: true,
  showTracks: true,
  saveResults: true
})

// 性能指标
const performanceMetrics = reactive({
  fps: 25,
  latency: 45,
  cpu: 35,
  gpu: 68
})

// 检测结果
const detectionResults = ref([])
const detectionStats = reactive({
  cars: 15,
  trucks: 3,
  pedestrians: 0
})

// WebSocket连接
const { connect, disconnect, sendMessage } = useWebSocket()

// 视频流管理
const { initStream, playStream, pauseStream, stopStream } = useVideoStream()

// 检测功能
const { startDetection, pauseDetection, stopDetection } = useDetection()

// 生命周期
onMounted(() => {
  initializeMonitoring()
})

onUnmounted(() => {
  cleanup()
})

// 方法实现
const initializeMonitoring = async () => {
  await connect()
  await initStream(currentMonitor.value)
}

const cleanup = () => {
  disconnect()
  stopStream()
}
</script>

<style scoped>
.realtime-monitoring {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f7fafc;
}

.monitoring-content {
  flex: 1;
  display: flex;
  gap: 20px;
  padding: 20px;
}
</style>
```

### 2. 视频流组件 (`VideoStreamPanel.vue`)

```vue
<template>
  <div class="video-stream-panel">
    <div class="video-header">
      <h3 class="video-title">
        <span class="live-indicator"></span>
        实时视频流
        <span class="live-badge">LIVE</span>
      </h3>
      <div class="video-info">
        <span>1920x1080 @ 30fps</span>
      </div>
    </div>
    
    <div class="video-container">
      <video 
        ref="videoElement"
        class="video-stream"
        :src="streamUrl"
        autoplay
        muted
        @loadedmetadata="onVideoLoaded"
        @error="onVideoError"
      ></video>
      
      <!-- Detection Overlay -->
      <canvas 
        ref="detectionCanvas"
        class="detection-overlay"
        :width="videoWidth"
        :height="videoHeight"
      ></canvas>
      
      <!-- Video Controls -->
      <div class="video-controls">
        <button 
          class="control-btn play-btn"
          :class="{ active: isPlaying }"
          @click="$emit('play')"
        >
          ▶️
        </button>
        <button 
          class="control-btn pause-btn"
          @click="$emit('pause')"
        >
          ⏸️
        </button>
        <button 
          class="control-btn stop-btn"
          @click="$emit('stop')"
        >
          ⏹️
        </button>
        <button 
          class="control-btn screenshot-btn"
          @click="$emit('screenshot')"
        >
          📸
        </button>
        <button 
          class="control-btn record-btn"
          :class="{ recording: isRecording }"
          @click="$emit('record')"
        >
          🎥
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, nextTick } from 'vue'

const props = defineProps({
  streamUrl: String,
  detectionResults: Array,
  showLabels: Boolean,
  showTracks: Boolean,
  isRecording: Boolean
})

const emit = defineEmits(['play', 'pause', 'stop', 'screenshot', 'record'])

const videoElement = ref(null)
const detectionCanvas = ref(null)
const videoWidth = ref(860)
const videoHeight = ref(400)
const isPlaying = ref(false)

// 绘制检测结果
const drawDetections = () => {
  if (!detectionCanvas.value || !props.detectionResults) return
  
  const ctx = detectionCanvas.value.getContext('2d')
  ctx.clearRect(0, 0, videoWidth.value, videoHeight.value)
  
  props.detectionResults.forEach(detection => {
    // 绘制检测框
    ctx.strokeStyle = getDetectionColor(detection.class)
    ctx.lineWidth = 3
    ctx.strokeRect(detection.x, detection.y, detection.width, detection.height)
    
    if (props.showLabels) {
      // 绘制标签
      ctx.fillStyle = getDetectionColor(detection.class)
      ctx.fillRect(detection.x, detection.y - 25, detection.width, 20)
      
      ctx.fillStyle = '#ffffff'
      ctx.font = '12px Arial'
      ctx.fillText(
        `${detection.class} ${detection.confidence.toFixed(2)}`,
        detection.x + 5,
        detection.y - 10
      )
    }
    
    if (props.showTracks && detection.track) {
      // 绘制轨迹
      ctx.strokeStyle = getDetectionColor(detection.class)
      ctx.lineWidth = 2
      ctx.setLineDash([5, 5])
      
      ctx.beginPath()
      detection.track.forEach((point, index) => {
        if (index === 0) {
          ctx.moveTo(point.x, point.y)
        } else {
          ctx.lineTo(point.x, point.y)
        }
      })
      ctx.stroke()
      ctx.setLineDash([])
    }
  })
}

const getDetectionColor = (className) => {
  const colors = {
    car: '#4299e1',
    truck: '#ed8936',
    person: '#48bb78',
    motorcycle: '#9f7aea'
  }
  return colors[className] || '#718096'
}

const onVideoLoaded = () => {
  isPlaying.value = true
  videoWidth.value = videoElement.value.videoWidth
  videoHeight.value = videoElement.value.videoHeight
}

const onVideoError = (error) => {
  console.error('Video stream error:', error)
  isPlaying.value = false
}

// 监听检测结果变化
watch(() => props.detectionResults, drawDetections, { deep: true })
watch(() => props.showLabels, drawDetections)
watch(() => props.showTracks, drawDetections)
</script>

<style scoped>
.video-stream-panel {
  flex: 1;
  background: linear-gradient(to bottom, #ffffff, #f7fafc);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.video-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.video-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 16px;
  font-weight: bold;
  color: #2d3748;
  margin: 0;
}

.live-indicator {
  width: 8px;
  height: 8px;
  background: #f56565;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.live-badge {
  background: #f56565;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: bold;
}

.video-info {
  font-size: 12px;
  color: #a0aec0;
}

.video-container {
  position: relative;
  background: linear-gradient(to bottom, #1a202c, #2d3748);
  border-radius: 12px;
  overflow: hidden;
  border: 2px solid #4a5568;
}

.video-stream {
  width: 100%;
  height: 400px;
  object-fit: cover;
}

.detection-overlay {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
}

.video-controls {
  position: absolute;
  bottom: 10px;
  right: 10px;
  display: flex;
  gap: 8px;
  background: rgba(0, 0, 0, 0.7);
  padding: 8px;
  border-radius: 8px;
}

.control-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.control-btn.active {
  background: #48bb78;
}

.control-btn.recording {
  background: #f56565;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}
</style>
```

### 3. 检测结果面板组件 (`DetectionResultsPanel.vue`)

```vue
<template>
  <div class="detection-results-panel">
    <div class="panel-header">
      <h3 class="panel-title">
        检测结果
        <span class="update-indicator">实时更新</span>
      </h3>
    </div>
    
    <div class="detection-stats">
      <!-- Vehicle Statistics -->
      <div class="stat-card car-stat">
        <div class="stat-icon">🚗</div>
        <div class="stat-content">
          <div class="stat-label">汽车:</div>
          <div class="stat-value">{{ detectionStats.cars }}</div>
          <div class="stat-unit">辆</div>
          <div class="stat-change positive">↗ +2 较上分钟</div>
        </div>
        <div class="stat-chart">
          <MiniChart :data="carTrendData" color="#4299e1" />
        </div>
      </div>
      
      <div class="stat-card truck-stat">
        <div class="stat-icon">🚛</div>
        <div class="stat-content">
          <div class="stat-label">卡车:</div>
          <div class="stat-value">{{ detectionStats.trucks }}</div>
          <div class="stat-unit">辆</div>
          <div class="stat-change neutral">→ 无变化</div>
        </div>
        <div class="stat-chart">
          <MiniChart :data="truckTrendData" color="#ed8936" />
        </div>
      </div>
      
      <div class="stat-card pedestrian-stat">
        <div class="stat-icon">🚶</div>
        <div class="stat-content">
          <div class="stat-label">行人:</div>
          <div class="stat-value">{{ detectionStats.pedestrians }}</div>
          <div class="stat-unit">人</div>
          <div class="stat-status safe">安全状态</div>
        </div>
        <div class="stat-indicator">
          <div class="safety-check">✓</div>
        </div>
      </div>
      
      <!-- Performance Metrics -->
      <div class="performance-metrics">
        <h4 class="metrics-title">性能指标</h4>
        
        <div class="metric-item">
          <span class="metric-label">置信度:</span>
          <span class="metric-value confidence">{{ confidenceScore }}</span>
          <div class="metric-bar">
            <div 
              class="metric-fill confidence-fill"
              :style="{ width: `${confidenceScore * 100}%` }"
            ></div>
          </div>
        </div>
        
        <div class="metric-item">
          <span class="metric-label">处理速度:</span>
          <span class="metric-value speed">{{ processingSpeed }}</span>
          <span class="metric-unit">FPS</span>
          <div class="speed-gauge">
            <CircularProgress :value="processingSpeed" :max="30" color="#4299e1" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import MiniChart from './MiniChart.vue'
import CircularProgress from './CircularProgress.vue'

const props = defineProps({
  detectionStats: Object,
  performanceData: Object,
  confidenceScore: Number,
  processingSpeed: Number
})

// 模拟趋势数据
const carTrendData = computed(() => [10, 12, 15, 13, 15])
const truckTrendData = computed(() => [3, 3, 3, 3, 3])
</script>

<style scoped>
.detection-results-panel {
  width: 400px;
  background: linear-gradient(to bottom, #ffffff, #f7fafc);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.panel-header {
  margin-bottom: 20px;
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 18px;
  font-weight: bold;
  color: #2d3748;
  margin: 0;
}

.update-indicator {
  font-size: 12px;
  color: #48bb78;
  font-weight: normal;
}

.update-indicator::before {
  content: '';
  display: inline-block;
  width: 6px;
  height: 6px;
  background: #48bb78;
  border-radius: 50%;
  margin-right: 5px;
  animation: pulse 2s infinite;
}

.detection-stats {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.car-stat .stat-icon {
  background: rgba(66, 153, 225, 0.1);
}

.truck-stat .stat-icon {
  background: rgba(237, 137, 54, 0.1);
}

.pedestrian-stat .stat-icon {
  background: rgba(72, 187, 120, 0.1);
}

.stat-content {
  flex: 1;
}

.stat-label {
  font-size: 14px;
  color: #718096;
  margin-bottom: 5px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #2d3748;
  display: inline;
}

.stat-unit {
  font-size: 12px;
  color: #718096;
  margin-left: 5px;
}

.stat-change {
  font-size: 12px;
  margin-top: 5px;
}

.stat-change.positive {
  color: #48bb78;
}

.stat-change.neutral {
  color: #ed8936;
}

.stat-status.safe {
  color: #718096;
  font-size: 12px;
  margin-top: 5px;
}

.stat-chart {
  width: 80px;
  height: 30px;
}

.stat-indicator {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.safety-check {
  width: 24px;
  height: 24px;
  background: rgba(72, 187, 120, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #48bb78;
  font-size: 12px;
  font-weight: bold;
}

.performance-metrics {
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
}

.metrics-title {
  font-size: 14px;
  font-weight: bold;
  color: #2d3748;
  margin: 0 0 15px 0;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.metric-item:last-child {
  margin-bottom: 0;
}

.metric-label {
  font-size: 12px;
  color: #718096;
  min-width: 60px;
}

.metric-value {
  font-size: 16px;
  font-weight: bold;
}

.metric-value.confidence {
  color: #48bb78;
}

.metric-value.speed {
  color: #4299e1;
}

.metric-unit {
  font-size: 12px;
  color: #718096;
}

.metric-bar {
  flex: 1;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
}

.metric-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.confidence-fill {
  background: #48bb78;
}

.speed-gauge {
  width: 40px;
  height: 40px;
}
</style>
```


## 🔧 技术实现要点

### 1. WebSocket实时通信
```javascript
// composables/useWebSocket.js
import { ref } from 'vue'

export function useWebSocket() {
  const socket = ref(null)
  const isConnected = ref(false)
  
  const connect = () => {
    socket.value = new WebSocket('ws://localhost:8000/ws/monitoring')
    
    socket.value.onopen = () => {
      isConnected.value = true
      console.log('WebSocket connected')
    }
    
    socket.value.onmessage = (event) => {
      const data = JSON.parse(event.data)
      handleMessage(data)
    }
    
    socket.value.onclose = () => {
      isConnected.value = false
      console.log('WebSocket disconnected')
    }
  }
  
  const sendMessage = (message) => {
    if (socket.value && isConnected.value) {
      socket.value.send(JSON.stringify(message))
    }
  }
  
  const disconnect = () => {
    if (socket.value) {
      socket.value.close()
    }
  }
  
  return {
    connect,
    disconnect,
    sendMessage,
    isConnected
  }
}
```

### 2. 视频流处理
```javascript
// composables/useVideoStream.js
import { ref } from 'vue'

export function useVideoStream() {
  const streamUrl = ref('')
  const isPlaying = ref(false)
  
  const initStream = async (monitorId) => {
    try {
      const response = await fetch(`/api/monitor/${monitorId}/stream`)
      const data = await response.json()
      streamUrl.value = data.streamUrl
    } catch (error) {
      console.error('Failed to initialize stream:', error)
    }
  }
  
  const playStream = () => {
    isPlaying.value = true
  }
  
  const pauseStream = () => {
    isPlaying.value = false
  }
  
  const stopStream = () => {
    isPlaying.value = false
    streamUrl.value = ''
  }
  
  return {
    streamUrl,
    isPlaying,
    initStream,
    playStream,
    pauseStream,
    stopStream
  }
}
```

### 3. 检测结果处理
```javascript
// composables/useDetection.js
import { ref, reactive } from 'vue'

export function useDetection() {
  const isDetecting = ref(false)
  const detectionResults = ref([])
  const detectionStats = reactive({
    cars: 0,
    trucks: 0,
    pedestrians: 0
  })
  
  const startDetection = async (config) => {
    try {
      const response = await fetch('/api/detection/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
      })
      
      if (response.ok) {
        isDetecting.value = true
      }
    } catch (error) {
      console.error('Failed to start detection:', error)
    }
  }
  
  const pauseDetection = async () => {
    try {
      await fetch('/api/detection/pause', { method: 'POST' })
      isDetecting.value = false
    } catch (error) {
      console.error('Failed to pause detection:', error)
    }
  }
  
  const stopDetection = async () => {
    try {
      await fetch('/api/detection/stop', { method: 'POST' })
      isDetecting.value = false
      detectionResults.value = []
    } catch (error) {
      console.error('Failed to stop detection:', error)
    }
  }
  
  const updateDetectionResults = (results) => {
    detectionResults.value = results
    
    // 更新统计数据
    detectionStats.cars = results.filter(r => r.class === 'car').length
    detectionStats.trucks = results.filter(r => r.class === 'truck').length
    detectionStats.pedestrians = results.filter(r => r.class === 'person').length
  }
  
  return {
    isDetecting,
    detectionResults,
    detectionStats,
    startDetection,
    pauseDetection,
    stopDetection,
    updateDetectionResults
  }
}
```

## 🚀 部署和优化建议

### 1. 性能优化
- **视频流优化**：使用WebRTC或HLS协议减少延迟
- **Canvas渲染优化**：使用requestAnimationFrame优化绘制性能
- **数据更新优化**：使用防抖和节流控制更新频率
- **内存管理**：及时清理不需要的检测结果数据

### 2. 用户体验优化
- **加载状态**：提供视频加载和连接状态指示
- **错误处理**：友好的错误提示和重连机制
- **快捷键支持**：支持空格键播放/暂停等快捷操作
- **全屏模式**：支持视频全屏查看

### 3. 安全性考虑
- **权限控制**：验证用户访问监控点的权限
- **数据加密**：视频流和控制指令的加密传输
- **会话管理**：合理的会话超时和重新认证机制

---

通过遵循本实现指南，您可以构建一个功能完整、性能优秀的实时监控界面，为用户提供专业的视频监控和智能检测体验。