# -*- coding: utf-8 -*-
# @Description : 数据分析服务
# @Date : 2025年6月20日

import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from utils.database import get_db_connection

class AnalysisService:
    """数据分析服务类"""
    
    def __init__(self):
        pass
    
    def get_traffic_flow_stats(self, days: int = 7, highway_section: str = '') -> Dict[str, Any]:
        """获取交通流量统计"""
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            with get_db_connection() as db:
                # 构建查询条件
                where_conditions = ["a.create_time >= %s", "a.create_time <= %s"]
                params = [start_date, end_date]
                
                if highway_section:
                    where_conditions.append("a.highway_section LIKE %s")
                    params.append(f"%{highway_section}%")
                
                where_clause = " AND ".join(where_conditions)
                
                # 按日期分组的流量统计
                daily_stats = db.get_list(f"""
                    SELECT DATE(a.create_time) as date,
                           COUNT(*) as alarm_count,
                           AVG(a.vehicle_count) as avg_vehicles,
                           MAX(a.vehicle_count) as max_vehicles,
                           SUM(a.vehicle_count) as total_vehicles
                    FROM alarm a
                    WHERE {where_clause}
                    GROUP BY DATE(a.create_time)
                    ORDER BY date
                """, params)
                
                # 按小时分组的流量统计
                hourly_stats = db.get_list(f"""
                    SELECT HOUR(a.create_time) as hour,
                           COUNT(*) as alarm_count,
                           AVG(a.vehicle_count) as avg_vehicles,
                           MAX(a.vehicle_count) as max_vehicles
                    FROM alarm a
                    WHERE {where_clause}
                    GROUP BY HOUR(a.create_time)
                    ORDER BY hour
                """, params)
                
                # 按路段分组的统计
                section_stats = db.get_list(f"""
                    SELECT a.highway_section,
                           a.location,
                           COUNT(*) as alarm_count,
                           AVG(a.vehicle_count) as avg_vehicles,
                           MAX(a.vehicle_count) as max_vehicles,
                           SUM(a.vehicle_count) as total_vehicles
                    FROM alarm a
                    WHERE {where_clause}
                    GROUP BY a.highway_section, a.location
                    ORDER BY alarm_count DESC
                """, params)
            
            return {
                'daily_stats': daily_stats,
                'hourly_stats': hourly_stats,
                'section_stats': section_stats,
                'time_range': {
                    'start_date': start_date.strftime('%Y-%m-%d'),
                    'end_date': end_date.strftime('%Y-%m-%d'),
                    'days': days
                }
            }
        except Exception as e:
            raise Exception(f"获取交通流量统计失败: {str(e)}")
    
    def get_hourly_distribution(self, days: int = 7) -> Dict[str, Any]:
        """获取小时分布统计"""
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            with get_db_connection() as db:
                hourly_data = db.get_list("""
                    SELECT HOUR(create_time) as hour,
                           COUNT(*) as count,
                           AVG(vehicle_count) as avg_vehicles,
                           AVG(confidence_level) as avg_confidence
                    FROM alarm
                    WHERE create_time >= %s AND create_time <= %s
                    GROUP BY HOUR(create_time)
                    ORDER BY hour
                """, (start_date, end_date))
                
                # 填充缺失的小时数据
                hour_dict = {item['hour']: item for item in hourly_data}
                complete_hourly_data = []
                
                for hour in range(24):
                    if hour in hour_dict:
                        complete_hourly_data.append(hour_dict[hour])
                    else:
                        complete_hourly_data.append({
                            'hour': hour,
                            'count': 0,
                            'avg_vehicles': 0,
                            'avg_confidence': 0
                        })
            
            return {
                'hourly_distribution': complete_hourly_data,
                'peak_hours': self._find_peak_hours(complete_hourly_data),
                'time_range': f'最近{days}天'
            }
        except Exception as e:
            raise Exception(f"获取小时分布统计失败: {str(e)}")
    
    def _find_peak_hours(self, hourly_data: List[Dict]) -> List[int]:
        """找出高峰时段"""
        if not hourly_data:
            return []
        
        # 计算平均值
        avg_count = np.mean([item['count'] for item in hourly_data])
        
        # 找出超过平均值的小时
        peak_hours = [item['hour'] for item in hourly_data if item['count'] > avg_count]
        
        return peak_hours
    
    def get_location_ranking(self, days: int = 7, limit: int = 10) -> Dict[str, Any]:
        """获取地点排行统计"""
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            with get_db_connection() as db:
                location_stats = db.get_list("""
                    SELECT location,
                           highway_section,
                           COUNT(*) as alarm_count,
                           AVG(vehicle_count) as avg_vehicles,
                           MAX(vehicle_count) as max_vehicles,
                           AVG(confidence_level) as avg_confidence
                    FROM alarm
                    WHERE create_time >= %s AND create_time <= %s
                    GROUP BY location, highway_section
                    ORDER BY alarm_count DESC
                    LIMIT %s
                """, (start_date, end_date, limit))
            
            return {
                'location_ranking': location_stats,
                'time_range': f'最近{days}天',
                'total_locations': len(location_stats)
            }
        except Exception as e:
            raise Exception(f"获取地点排行统计失败: {str(e)}")
    
    def generate_heatmap_data(self, monitor_id: int, days: int = 1) -> Dict[str, Any]:
        """生成热力图数据"""
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            with get_db_connection() as db:
                # 获取监控点信息
                monitor = db.get_one(
                    "SELECT location, highway_section FROM monitor WHERE id=%s",
                    (monitor_id,)
                )
                
                if not monitor:
                    raise Exception("监控点不存在")
                
                # 获取检测数据
                detection_data = db.get_list("""
                    SELECT create_time,
                           vehicle_count,
                           detection_details,
                           confidence_level
                    FROM alarm
                    WHERE pid = %s AND create_time >= %s AND create_time <= %s
                    ORDER BY create_time
                """, (monitor_id, start_date, end_date))
                
                # 处理热力图数据
                heatmap_points = []
                for item in detection_data:
                    if item['detection_details']:
                        try:
                            details = json.loads(item['detection_details'])
                            boxes = details.get('boxes', [])
                            
                            for box in boxes:
                                if len(box) >= 4:
                                    # 计算中心点
                                    center_x = (box[0] + box[2]) / 2
                                    center_y = (box[1] + box[3]) / 2
                                    
                                    heatmap_points.append({
                                        'x': center_x,
                                        'y': center_y,
                                        'weight': item['confidence_level'] or 1.0,
                                        'timestamp': item['create_time'].isoformat()
                                    })
                        except:
                            continue
            
            return {
                'monitor_info': monitor,
                'heatmap_points': heatmap_points,
                'total_points': len(heatmap_points),
                'time_range': {
                    'start': start_date.isoformat(),
                    'end': end_date.isoformat()
                }
            }
        except Exception as e:
            raise Exception(f"生成热力图数据失败: {str(e)}")
    
    def export_alarms(self, start_date: str, end_date: str, location: str = '', 
                     format_type: str = 'excel') -> str:
        """导出警报数据"""
        try:
            with get_db_connection() as db:
                # 构建查询条件
                where_conditions = ["a.create_time >= %s", "a.create_time <= %s"]
                params = [start_date, end_date]
                
                if location:
                    where_conditions.append("a.location LIKE %s")
                    params.append(f"%{location}%")
                
                where_clause = " AND ".join(where_conditions)
                
                # 获取警报数据
                alarms = db.get_list(f"""
                    SELECT a.id, a.location, a.highway_section, a.description,
                           a.vehicle_count, a.confidence_level, a.threshold,
                           a.create_time, a.remark, m.person as monitor_person
                    FROM alarm a
                    LEFT JOIN monitor m ON a.pid = m.id
                    WHERE {where_clause}
                    ORDER BY a.create_time DESC
                """, params)
            
            # 创建导出目录
            import os
            export_dir = 'exports'
            os.makedirs(export_dir, exist_ok=True)
            
            # 生成文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"alarms_export_{timestamp}"
            
            if format_type == 'excel':
                file_path = os.path.join(export_dir, f"{filename}.xlsx")
                df = pd.DataFrame(alarms)
                df.to_excel(file_path, index=False, engine='openpyxl')
            elif format_type == 'csv':
                file_path = os.path.join(export_dir, f"{filename}.csv")
                df = pd.DataFrame(alarms)
                df.to_csv(file_path, index=False, encoding='utf-8-sig')
            else:
                raise Exception(f"不支持的导出格式: {format_type}")
            
            return file_path
        except Exception as e:
            raise Exception(f"导出警报数据失败: {str(e)}")
    
    def predict_traffic_flow(self, monitor_id: Optional[int] = None, hours: int = 24) -> Dict[str, Any]:
        """预测交通流量"""
        try:
            # 这里可以实现更复杂的预测算法
            # 目前使用简单的历史数据平均值预测
            
            with get_db_connection() as db:
                # 获取历史数据
                where_condition = "create_time >= %s"
                params = [datetime.now() - timedelta(days=30)]
                
                if monitor_id:
                    where_condition += " AND pid = %s"
                    params.append(monitor_id)
                
                historical_data = db.get_list(f"""
                    SELECT HOUR(create_time) as hour,
                           AVG(vehicle_count) as avg_vehicles,
                           COUNT(*) as count
                    FROM alarm
                    WHERE {where_condition}
                    GROUP BY HOUR(create_time)
                    ORDER BY hour
                """, params)
            
            # 生成预测数据
            predictions = []
            current_time = datetime.now()
            
            # 创建小时平均值字典
            hour_avg = {item['hour']: item['avg_vehicles'] for item in historical_data}
            
            for i in range(hours):
                predict_time = current_time + timedelta(hours=i)
                hour = predict_time.hour
                
                # 使用历史平均值作为预测值，添加一些随机波动
                base_value = hour_avg.get(hour, 10)  # 默认值10
                predicted_value = max(0, base_value + np.random.normal(0, base_value * 0.1))
                
                predictions.append({
                    'timestamp': predict_time.isoformat(),
                    'hour': hour,
                    'predicted_vehicles': round(predicted_value, 2),
                    'confidence': 0.7  # 简单预测的置信度
                })
            
            return {
                'predictions': predictions,
                'model_info': {
                    'type': 'historical_average',
                    'training_data_days': 30,
                    'accuracy': 0.7
                },
                'monitor_id': monitor_id
            }
        except Exception as e:
            raise Exception(f"预测交通流量失败: {str(e)}")
    
    def generate_daily_report(self, date: str) -> Dict[str, Any]:
        """生成日报"""
        try:
            report_date = datetime.strptime(date, '%Y-%m-%d')
            next_date = report_date + timedelta(days=1)
            
            with get_db_connection() as db:
                # 基本统计
                basic_stats = db.get_one("""
                    SELECT COUNT(*) as total_alarms,
                           AVG(vehicle_count) as avg_vehicles,
                           MAX(vehicle_count) as max_vehicles,
                           MIN(vehicle_count) as min_vehicles,
                           AVG(confidence_level) as avg_confidence
                    FROM alarm
                    WHERE create_time >= %s AND create_time < %s
                """, (report_date, next_date))
                
                # 按小时统计
                hourly_stats = db.get_list("""
                    SELECT HOUR(create_time) as hour,
                           COUNT(*) as count,
                           AVG(vehicle_count) as avg_vehicles
                    FROM alarm
                    WHERE create_time >= %s AND create_time < %s
                    GROUP BY HOUR(create_time)
                    ORDER BY hour
                """, (report_date, next_date))
                
                # 按地点统计
                location_stats = db.get_list("""
                    SELECT location, highway_section,
                           COUNT(*) as count,
                           AVG(vehicle_count) as avg_vehicles
                    FROM alarm
                    WHERE create_time >= %s AND create_time < %s
                    GROUP BY location, highway_section
                    ORDER BY count DESC
                    LIMIT 10
                """, (report_date, next_date))
                
                # 活跃监控点
                active_monitors = db.get_list("""
                    SELECT COUNT(*) as monitor_count
                    FROM monitor
                    WHERE is_alarm = '开启'
                """)
            
            return {
                'report_date': date,
                'basic_statistics': basic_stats,
                'hourly_distribution': hourly_stats,
                'top_locations': location_stats,
                'active_monitors': active_monitors[0]['monitor_count'] if active_monitors else 0,
                'generated_at': datetime.now().isoformat()
            }
        except Exception as e:
            raise Exception(f"生成日报失败: {str(e)}")
    
    def generate_weekly_report(self, week_start: str) -> Dict[str, Any]:
        """生成周报"""
        try:
            start_date = datetime.strptime(week_start, '%Y-%m-%d')
            end_date = start_date + timedelta(days=7)
            
            with get_db_connection() as db:
                # 周统计
                weekly_stats = db.get_one("""
                    SELECT COUNT(*) as total_alarms,
                           AVG(vehicle_count) as avg_vehicles,
                           MAX(vehicle_count) as max_vehicles,
                           SUM(vehicle_count) as total_vehicles,
                           AVG(confidence_level) as avg_confidence
                    FROM alarm
                    WHERE create_time >= %s AND create_time < %s
                """, (start_date, end_date))
                
                # 按天统计
                daily_stats = db.get_list("""
                    SELECT DATE(create_time) as date,
                           COUNT(*) as count,
                           AVG(vehicle_count) as avg_vehicles,
                           MAX(vehicle_count) as max_vehicles
                    FROM alarm
                    WHERE create_time >= %s AND create_time < %s
                    GROUP BY DATE(create_time)
                    ORDER BY date
                """, (start_date, end_date))
                
                # 趋势分析
                trend_analysis = self._analyze_weekly_trend(daily_stats)
            
            return {
                'week_start': week_start,
                'week_end': (end_date - timedelta(days=1)).strftime('%Y-%m-%d'),
                'weekly_statistics': weekly_stats,
                'daily_breakdown': daily_stats,
                'trend_analysis': trend_analysis,
                'generated_at': datetime.now().isoformat()
            }
        except Exception as e:
            raise Exception(f"生成周报失败: {str(e)}")
    
    def _analyze_weekly_trend(self, daily_stats: List[Dict]) -> Dict[str, Any]:
        """分析周趋势"""
        if len(daily_stats) < 2:
            return {'trend': 'insufficient_data'}
        
        counts = [item['count'] for item in daily_stats]
        
        # 计算趋势
        if len(counts) >= 3:
            # 简单线性趋势
            x = np.arange(len(counts))
            slope = np.polyfit(x, counts, 1)[0]
            
            if slope > 0.5:
                trend = 'increasing'
            elif slope < -0.5:
                trend = 'decreasing'
            else:
                trend = 'stable'
        else:
            trend = 'stable'
        
        return {
            'trend': trend,
            'slope': float(slope) if 'slope' in locals() else 0,
            'max_day': max(daily_stats, key=lambda x: x['count'])['date'],
            'min_day': min(daily_stats, key=lambda x: x['count'])['date']
        }
