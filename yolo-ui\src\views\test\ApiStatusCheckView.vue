<template>
  <div class="api-status-check">
    <a-card title="API状态检查">
      <a-alert type="warning" show-icon style="margin-bottom: 16px;">
        <template #title>发现API问题</template>
        <div>
          <p>测试结果显示：成功率55.6%，多个API返回404错误</p>
          <p>主要问题：系统管理和数据分析模块的API端点不存在</p>
        </div>
      </a-alert>
      
      <div class="status-grid">
        <div 
          v-for="api in apiEndpoints" 
          :key="api.path"
          class="api-card"
          :class="{ 
            'success': api.status === 'success',
            'failed': api.status === 'failed',
            'testing': api.status === 'testing'
          }"
        >
          <div class="api-header">
            <h4>{{ api.name }}</h4>
            <a-tag :color="getStatusColor(api.status)">
              {{ getStatusText(api.status) }}
            </a-tag>
          </div>
          
          <div class="api-info">
            <p><strong>路径:</strong> <code>{{ api.path }}</code></p>
            <p><strong>方法:</strong> {{ api.method }}</p>
            <p><strong>模块:</strong> {{ api.module }}</p>
          </div>
          
          <div class="api-actions">
            <a-button 
              size="small" 
              @click="testApi(api)"
              :loading="api.status === 'testing'"
            >
              测试
            </a-button>
          </div>
          
          <div class="api-result" v-if="api.result">
            <div class="result-summary">
              <p><strong>状态码:</strong> {{ api.result.status }}</p>
              <p><strong>响应时间:</strong> {{ api.result.duration }}ms</p>
              <p v-if="api.result.error"><strong>错误:</strong> {{ api.result.error }}</p>
            </div>
            
            <a-collapse size="small" v-if="api.result.data">
              <a-collapse-item header="响应数据" key="data">
                <pre class="response-data">{{ JSON.stringify(api.result.data, null, 2) }}</pre>
              </a-collapse-item>
            </a-collapse>
          </div>
        </div>
      </div>
      
      <a-divider />
      
      <div class="actions">
        <a-space>
          <a-button @click="testAllApis" :loading="allTesting" type="primary">
            测试所有API
          </a-button>
          <a-button @click="generateBackendTodoList">
            生成后端待办清单
          </a-button>
          <a-button @click="copyFailedApis">
            复制失败的API列表
          </a-button>
        </a-space>
      </div>
      
      <!-- 统计信息 -->
      <div class="statistics" v-if="statistics.total > 0">
        <h3>测试统计</h3>
        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic title="总API数" :value="statistics.total" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="成功" :value="statistics.success" :value-style="{ color: '#52c41a' }" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="失败" :value="statistics.failed" :value-style="{ color: '#f5222d' }" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="成功率" :value="statistics.successRate" suffix="%" :precision="1" />
          </a-col>
        </a-row>
      </div>
      
      <!-- 后端待办事项 -->
      <div class="backend-todo" v-if="showTodoList">
        <h3>后端开发者待办事项</h3>
        <a-alert type="error" show-icon style="margin-bottom: 16px;">
          <template #title>需要实现的API端点</template>
        </a-alert>
        
        <div class="todo-list">
          <div v-for="todo in todoList" :key="todo.module" class="todo-module">
            <h4>{{ todo.module }}</h4>
            <ul>
              <li v-for="api in todo.apis" :key="api.path">
                <code>{{ api.method }} {{ api.path }}</code> - {{ api.description }}
              </li>
            </ul>
          </div>
        </div>
        
        <a-divider />
        
        <h4>实现建议</h4>
        <pre class="implementation-guide">
# FastAPI实现示例

# 1. 系统管理模块
@app.get("/api/v1/system/users")
async def get_users(db: Session = Depends(get_db)):
    users = db.query(User).all()
    return {
        "success": True,
        "data": {
            "users": [{"id": u.id, "username": u.username, "grade": u.grade} for u in users],
            "total": len(users)
        }
    }

# 2. 数据分析模块
@app.get("/api/v1/analysis/alarms")
async def get_alarm_stats(db: Session = Depends(get_db)):
    # 查询警报统计数据
    return {
        "success": True,
        "data": {
            "today_alarms": 5,
            "this_week_alarms": 23,
            "alarm_types": {"collision": 3, "breakdown": 2}
        }
    }

@app.get("/api/v1/analysis/traffic-flow")
async def get_traffic_flow(db: Session = Depends(get_db)):
    # 查询交通流量数据
    return {
        "success": True,
        "data": {
            "hourly_flow": [{"hour": 8, "count": 120}, {"hour": 9, "count": 150}],
            "daily_average": 1200
        }
    }
        </pre>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { Message } from '@arco-design/web-vue'
import axios from 'axios'

const allTesting = ref(false)
const showTodoList = ref(false)

// API端点配置
const apiEndpoints = reactive([
  // 认证模块 - 正常工作
  { name: '用户登录', path: '/api/v1/auth/login', method: 'POST', module: '认证模块', status: 'pending', result: null },
  { name: '获取用户信息', path: '/api/v1/auth/profile', method: 'GET', module: '认证模块', status: 'pending', result: null },
  
  // 监控管理 - 正常工作
  { name: '获取监控点列表', path: '/api/v1/monitor/list', method: 'GET', module: '监控管理', status: 'pending', result: null },
  
  // 数据分析 - 部分失败
  { name: '概览统计', path: '/api/v1/analysis/statistics/overview', method: 'GET', module: '数据分析', status: 'pending', result: null },
  { name: '警报统计', path: '/api/v1/analysis/alarms', method: 'GET', module: '数据分析', status: 'pending', result: null },
  { name: '交通流量', path: '/api/v1/analysis/traffic-flow', method: 'GET', module: '数据分析', status: 'pending', result: null },
  
  // 系统管理 - 失败
  { name: '获取用户列表', path: '/api/v1/system/users', method: 'GET', module: '系统管理', status: 'pending', result: null },
  { name: '获取系统配置', path: '/api/v1/system/config', method: 'GET', module: '系统管理', status: 'pending', result: null },
  
  // 检测中心
  { name: '获取检测任务', path: '/api/v1/detection/tasks', method: 'GET', module: '检测中心', status: 'pending', result: null },
  
  // 事故检测
  { name: '获取事故记录', path: '/api/v1/accident/records', method: 'GET', module: '事故检测', status: 'pending', result: null }
])

// 统计信息
const statistics = computed(() => {
  const total = apiEndpoints.length
  const success = apiEndpoints.filter(api => api.status === 'success').length
  const failed = apiEndpoints.filter(api => api.status === 'failed').length
  const successRate = total > 0 ? (success / total) * 100 : 0
  
  return { total, success, failed, successRate }
})

// 待办清单
const todoList = computed(() => {
  const failedApis = apiEndpoints.filter(api => api.status === 'failed')
  const grouped = failedApis.reduce((acc, api) => {
    if (!acc[api.module]) {
      acc[api.module] = []
    }
    acc[api.module].push({
      path: api.path,
      method: api.method,
      description: api.name
    })
    return acc
  }, {} as Record<string, any[]>)
  
  return Object.entries(grouped).map(([module, apis]) => ({ module, apis }))
})

// 获取状态颜色
const getStatusColor = (status: string) => {
  switch (status) {
    case 'success': return 'green'
    case 'failed': return 'red'
    case 'testing': return 'blue'
    default: return 'gray'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'success': return '成功'
    case 'failed': return '失败'
    case 'testing': return '测试中'
    default: return '未测试'
  }
}

// 测试单个API
const testApi = async (api: any) => {
  api.status = 'testing'
  const startTime = Date.now()
  
  try {
    const config = {
      method: api.method.toLowerCase(),
      url: api.path,
      timeout: 5000,
      headers: {
        'Authorization': 'Bearer test-token',
        'Content-Type': 'application/json'
      }
    }
    
    if (api.method === 'POST' && api.path.includes('login')) {
      config.data = { username: 'admin', password: '123456' }
    }
    
    const response = await axios(config)
    const duration = Date.now() - startTime
    
    api.result = {
      status: response.status,
      duration,
      data: response.data
    }
    
    api.status = 'success'
    
  } catch (error: any) {
    const duration = Date.now() - startTime
    
    api.result = {
      status: error.response?.status || 'Network Error',
      duration,
      error: error.message,
      data: error.response?.data
    }
    
    api.status = 'failed'
  }
}

// 测试所有API
const testAllApis = async () => {
  allTesting.value = true
  
  for (const api of apiEndpoints) {
    await testApi(api)
    await new Promise(resolve => setTimeout(resolve, 200))
  }
  
  allTesting.value = false
  Message.success(`测试完成！成功率: ${statistics.value.successRate.toFixed(1)}%`)
}

// 生成后端待办清单
const generateBackendTodoList = () => {
  showTodoList.value = true
  Message.info('已生成后端待办清单')
}

// 复制失败的API列表
const copyFailedApis = () => {
  const failedApis = apiEndpoints.filter(api => api.status === 'failed')
  const list = failedApis.map(api => `${api.method} ${api.path} - ${api.name} (${api.result?.status})`).join('\n')
  
  navigator.clipboard.writeText(list).then(() => {
    Message.success('失败的API列表已复制到剪贴板')
  }).catch(() => {
    Message.error('复制失败')
  })
}
</script>

<style scoped>
.api-status-check {
  padding: 20px;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.api-card {
  padding: 16px;
  border: 1px solid #e5e6eb;
  border-radius: 8px;
  background: #fff;
}

.api-card.success {
  border-color: #52c41a;
  background: #f6ffed;
}

.api-card.failed {
  border-color: #ff4d4f;
  background: #fff2f0;
}

.api-card.testing {
  border-color: #1890ff;
  background: #f0f8ff;
}

.api-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.api-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.api-info p {
  margin: 4px 0;
  font-size: 12px;
  color: #666;
}

.api-actions {
  margin: 12px 0;
}

.api-result {
  border-top: 1px solid #e5e6eb;
  padding-top: 12px;
  margin-top: 12px;
}

.result-summary p {
  margin: 4px 0;
  font-size: 12px;
}

.response-data {
  font-family: 'Courier New', monospace;
  font-size: 11px;
  max-height: 150px;
  overflow-y: auto;
  margin: 0;
}

.actions {
  text-align: center;
  margin: 24px 0;
}

.statistics {
  margin-top: 24px;
  padding: 16px;
  background: #f7f8fa;
  border-radius: 8px;
}

.statistics h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.backend-todo {
  margin-top: 24px;
  padding: 16px;
  background: #fff2f0;
  border-radius: 8px;
  border: 1px solid #ffccc7;
}

.backend-todo h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.todo-list {
  margin: 16px 0;
}

.todo-module {
  margin-bottom: 16px;
}

.todo-module h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #f5222d;
}

.todo-module ul {
  margin: 0;
  padding-left: 20px;
}

.todo-module li {
  margin: 4px 0;
  font-size: 12px;
}

.implementation-guide {
  background: #f7f8fa;
  padding: 12px;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  margin: 0;
  border: 1px solid #e5e6eb;
  overflow-x: auto;
}
</style>
