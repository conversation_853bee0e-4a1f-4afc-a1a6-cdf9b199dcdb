# 基于Yolov8与ByteTrack的高速公路智慧监控平台 - 依赖包列表
# 安装命令: pip install -r requirements.txt

# 核心机器学习和计算机视觉
ultralytics>=8.0.196
opencv-python>=********
opencv-contrib-python>=********
supervision>=0.16.0
numpy>=1.24.3
Pillow>=10.0.1
matplotlib>=3.7.2
scipy>=1.11.3

# PyTorch (根据CUDA版本选择)
torch>=2.0.0
torchvision>=0.15.0
torchaudio>=2.0.0

# Web框架
Flask>=2.3.3
Flask-CORS>=4.0.0
Flask-SocketIO>=5.3.6
Werkzeug>=2.3.7

# 数据库
PyMySQL>=1.1.0
SQLAlchemy>=2.0.23

# 缓存
redis>=5.0.1

# 数据处理
pandas>=2.0.3
openpyxl>=3.1.2

# 网络和通信
requests>=2.31.0
websockets>=11.0.3
python-socketio>=5.9.0
eventlet>=0.33.3

# 认证和安全
PyJWT>=2.8.0
bcrypt>=4.0.1
cryptography>=41.0.7

# 配置管理
python-dotenv>=1.0.0
configparser>=6.0.0

# 任务调度
APScheduler>=3.10.4
schedule>=1.2.0

# 系统监控
psutil>=5.9.6

# 文件处理
python-magic>=0.4.27

# 日志
loguru>=0.7.2

# HTTP服务器
gunicorn>=21.2.0

# 开发工具
pytest>=7.4.3
pytest-cov>=4.1.0
black>=23.9.1
flake8>=6.1.0

# GUI (可选)
PySide6>=6.4.2

# 其他工具
tqdm>=4.66.1
click>=8.1.7
python-dateutil>=2.8.2
pytz>=2023.3
thop>=0.1.1
lap>=0.4

# 可视化样式
mplcyberpunk>=0.7.0

