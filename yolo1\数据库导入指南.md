# 数据库导入指南

## 🔧 问题解决

您遇到的错误是因为数据库中已经存在相同ID的记录，导致主键冲突。

### 错误信息分析
```
[ERR] 1062 - Duplicate entry '1' for key 'alarm.PRIMARY'
```
这表示alarm表中已经存在ID=1的记录。

## 🚀 解决方案

### 方法1: 使用Navicat重置数据库（推荐）

1. **打开Navicat**
2. **右键点击yolo数据库** → 选择"删除数据库"
3. **创建新数据库**：
   - 右键点击连接 → "新建数据库"
   - 数据库名：`yolo`
   - 字符集：`utf8mb4`
   - 排序规则：`utf8mb4_unicode_ci`
4. **导入SQL文件**：
   - 右键点击yolo数据库 → "运行SQL文件"
   - 选择修复后的 `yolo.sql` 文件
   - 点击"开始"

### 方法2: 使用命令行重置

```bash
# 1. 连接到MySQL
mysql -u root -p

# 2. 重置数据库
source reset_database.sql

# 3. 退出MySQL
exit

# 4. 导入数据
mysql -u root -p yolo < yolo.sql
```

### 方法3: 使用重置脚本

```bash
# 直接运行重置脚本
mysql -u root -p < reset_database.sql

# 然后导入数据
mysql -u root -p yolo < yolo.sql
```

## ✅ 验证导入成功

导入完成后，运行以下SQL验证：

```sql
-- 检查表是否创建成功
SHOW TABLES;

-- 检查用户数据
SELECT * FROM user;

-- 检查监控点数据
SELECT id, location, highway_section FROM monitor LIMIT 5;

-- 检查警报数据
SELECT id, location, highway_section, vehicle_count FROM alarm LIMIT 5;

-- 检查数据统计
SELECT 
    (SELECT COUNT(*) FROM user) as user_count,
    (SELECT COUNT(*) FROM monitor) as monitor_count,
    (SELECT COUNT(*) FROM alarm) as alarm_count;
```

## 📊 预期结果

导入成功后应该看到：
- **3个用户**：admin, operator, viewer
- **15个监控点**：覆盖杭州至千岛湖高速全程
- **17条警报记录**：包含不同时间段的真实数据
- **8个系统配置**：默认系统参数
- **3个模型配置**：YOLO模型设置

## 🎯 数据库表结构

成功导入后将包含以下表：
1. `user` - 用户表
2. `monitor` - 监控点表
3. `alarm` - 警报表
4. `detection_task` - 检测任务表
5. `detection_result` - 检测结果表
6. `system_config` - 系统配置表
7. `system_log` - 系统日志表
8. `traffic_statistics` - 交通统计表
9. `violation_record` - 违规记录表
10. `model_config` - 模型配置表
11. `realtime_data` - 实时数据表

## 🔍 常见问题

### Q1: 仍然报重复键错误
**解决**：确保完全删除了旧数据库，重新创建后再导入

### Q2: 字符编码问题
**解决**：确保数据库字符集为utf8mb4，排序规则为utf8mb4_unicode_ci

### Q3: 权限问题
**解决**：确保MySQL用户有CREATE、DROP、INSERT权限

### Q4: 连接失败
**解决**：检查MySQL服务是否启动，用户名密码是否正确

## 📞 技术支持

如果仍有问题，请检查：
1. MySQL服务状态
2. 用户权限设置
3. 数据库连接配置
4. SQL文件完整性

导入成功后，您就可以启动系统了：
```bash
python start_server.py
```
