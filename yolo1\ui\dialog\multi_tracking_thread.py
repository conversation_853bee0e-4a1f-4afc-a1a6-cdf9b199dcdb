import cv2
import numpy as np
import time
from PySide6.QtCore import QThread, Signal, Slot
from PySide6.QtGui import QImage, QPixmap
import torch

class MultiTrackingThread(QThread):
    """多目标追踪线程，实现基于YOLOv8的多目标追踪"""

    # 定义信号
    update_signal = Signal(dict)  # 发送追踪结果的信号
    error_signal = Signal(str)    # 错误信号
    status_signal = Signal(str)   # 状态信号

    def __init__(self, video_source, model_path=None, conf_thres=0.4, iou_thres=0.5,
                 show_labels=True, show_trails=True, classes=None):
        super(MultiTrackingThread, self).__init__()

        # 视频源参数
        self.video_source = video_source  # 可以是摄像头索引、视频文件或RTSP URL

        # 追踪参数
        self.conf_thres = conf_thres    # 置信度阈值
        self.iou_thres = iou_thres      # IOU阈值
        self.show_labels = show_labels  # 是否显示标签
        self.show_trails = show_trails  # 是否显示轨迹
        self.classes = classes          # 要检测的类别列表，默认为所有

        # 内部对象
        self.model = None               # YOLO模型
        self.model_path = model_path    # 模型路径
        self.cap = None                 # 视频捕获对象
        self.is_running = False         # 运行状态标志

        # 追踪目标管理
        self.targets = {}               # 当前追踪的目标字典
        self.next_id = 1                # 下一个目标ID

        # 统计数据
        self.stats = {
            "total": 0,                 # 目标总数
            "class_counts": {},         # 各类别数量
            "avg_speed": 0              # 平均速度
        }

        # 类别映射汉化
        self.class_names_zh = {
            "car": "小汽车",
            "truck": "货车",
            "bus": "客车",
            "motorcycle": "摩托车"
        }

    def load_model(self):
        """加载YOLOv8模型"""
        try:
            # 使用ultralytics包加载YOLOv8模型
            from ultralytics import YOLO

            # 如果未指定模型路径，则使用默认的YOLOv8n模型
            if self.model_path is None:
                self.model_path = "yolov8n.pt"

            # 加载模型
            self.model = YOLO(self.model_path)

            # 发送模型加载状态
            self.status_signal.emit(f"模型 {self.model_path} 加载成功")
            return True

        except Exception as e:
            self.error_signal.emit(f"模型加载失败: {str(e)}")
            return False

    def open_video_source(self):
        """打开视频源"""
        try:
            # 初始化视频捕获对象
            self.cap = cv2.VideoCapture(self.video_source)

            # 检查视频源是否打开成功
            if not self.cap.isOpened():
                self.error_signal.emit(f"无法打开视频源: {self.video_source}")
                return False

            # 发送视频源打开状态
            self.status_signal.emit(f"视频源 {self.video_source} 打开成功")
            return True

        except Exception as e:
            self.error_signal.emit(f"打开视频源失败: {str(e)}")
            return False

    def run(self):
        """线程主函数，实现多目标追踪逻辑"""
        # 设置运行状态
        self.is_running = True

        # 加载模型
        if not self.load_model():
            self.is_running = False
            return

        # 打开视频源
        if not self.open_video_source():
            self.is_running = False
            return

        # FPS计算
        fps_start_time = time.time()
        fps_counter = 0
        fps = 0

        # 主追踪循环
        while self.is_running:
            # 读取一帧视频
            ret, frame = self.cap.read()

            # 如果读取失败，可能已经到达视频结尾
            if not ret:
                self.status_signal.emit("视频已结束或读取失败")
                break

            # 使用YOLO模型进行目标检测和追踪
            try:
                # 这里我们使用YOLOv8的追踪功能
                results = self.model.track(
                    source=frame,
                    conf=self.conf_thres,
                    iou=self.iou_thres,
                    persist=True,            # 持续追踪
                    classes=self.classes,    # 限制类别
                    verbose=False            # 不打印日志
                )

                # 如果有检测结果
                if results and len(results) > 0:
                    # 处理检测结果并更新目标信息
                    processed_results = self.process_results(results[0], frame)

                    # 发送结果信号
                    if processed_results:
                        self.update_signal.emit(processed_results)

            except Exception as e:
                self.error_signal.emit(f"目标追踪异常: {str(e)}")
                continue

            # 计算FPS
            fps_counter += 1
            if (time.time() - fps_start_time) > 1:
                fps = fps_counter
                fps_counter = 0
                fps_start_time = time.time()
                self.status_signal.emit(f"FPS: {fps}")

            # 避免线程运行过快占用过多资源
            QThread.msleep(10)

        # 清理资源
        if self.cap is not None and self.cap.isOpened():
            self.cap.release()

    def stop(self):
        """停止追踪线程"""
        self.is_running = False

    def process_results(self, results, frame):
        """处理YOLO检测结果并格式化为可用于显示的格式

        Args:
            results: YOLO模型的检测结果
            frame: 原始视频帧

        Returns:
            dict: 格式化的结果数据
        """
        try:
            # 准备处理后的帧图像
            processed_frame = frame.copy()

            # 获取目标框、类别和置信度
            boxes = results.boxes.cpu().numpy()

            # 检查是否有跟踪ID
            if hasattr(boxes, 'id') and boxes.id is not None:
                track_ids = boxes.id.astype(int)
            else:
                # 如果没有跟踪ID，则不处理或创建新ID
                self.error_signal.emit("缺少跟踪ID，请确保使用track方法")
                return None

            # 获取边界框、类别、置信度
            bboxes = boxes.xyxy.astype(int)
            classes = boxes.cls.astype(int)
            confs = boxes.conf

            # 准备处理结果列表
            processed_targets = []

            # 统计参数初始化
            class_counts = {"car": 0, "truck": 0, "bus": 0, "motorcycle": 0}
            total_speed = 0
            active_targets = 0

            # 遍历每个检测到的目标
            for i in range(len(bboxes)):
                # 获取数据
                track_id = track_ids[i]
                bbox = bboxes[i]
                class_id = classes[i]
                conf = confs[i]

                # 获取类别名称
                class_name = results.names[class_id]

                # 过滤非车辆类别 (可选的过滤处理)
                vehicle_classes = ['car', 'truck', 'bus', 'motorcycle']
                if class_name not in vehicle_classes:
                    continue

                # 获取中文类别名称
                class_name_zh = self.class_names_zh.get(class_name, class_name)

                # 更新类别计数
                if class_name in class_counts:
                    class_counts[class_name] += 1

                # 计算目标中心点
                x1, y1, x2, y2 = bbox
                center_x = (x1 + x2) // 2
                center_y = (y1 + y2) // 2
                center = (center_x, center_y)

                # 获取目标缩略图
                thumbnail = self.get_target_thumbnail(frame, bbox)

                # 检查目标是否已经存在于跟踪列表中
                is_new_target = False
                if track_id not in self.targets:
                    # 新目标，添加到跟踪列表
                    self.targets[track_id] = {
                        "class": class_name,
                        "class_zh": class_name_zh,
                        "track_points": [center],
                        "last_seen": time.time(),
                        "speed": 0,
                        "lost_counter": 0,
                        "color": self.generate_color(track_id)
                    }
                    is_new_target = True
                else:
                    # 更新现有目标
                    target = self.targets[track_id]
                    target["last_seen"] = time.time()
                    target["lost_counter"] = 0

                    # 添加轨迹点
                    if len(target["track_points"]) >= 50:
                        target["track_points"].pop(0)  # 移除最早的轨迹点
                    target["track_points"].append(center)

                    # 估算速度 (简化处理，实际应用中需要更复杂的处理)
                    if len(target["track_points"]) >= 2:
                        last_pt = target["track_points"][-2]
                        curr_pt = target["track_points"][-1]
                        # 像素距离转换为速度 (这里只是一个简化示例)
                        distance = ((curr_pt[0]-last_pt[0])**2 + (curr_pt[1]-last_pt[1])**2)**0.5
                        target["speed"] = distance * 0.5  # 简化的速度计算，只作示意

                # 当前目标信息
                current_target = self.targets[track_id]
                speed = current_target["speed"]
                total_speed += speed
                active_targets += 1

                # 在原始帧上绘制目标边界框和轨迹
                self.draw_tracking_info(processed_frame, track_id, bbox, current_target, class_name_zh, conf)

                # 添加目标到处理结果列表
                processed_targets.append({
                    "id": int(track_id),
                    "class": class_name_zh,
                    "box": bbox,
                    "confidence": float(conf),
                    "track_points": current_target["track_points"],
                    "speed": float(speed),
                    "thumbnail": thumbnail,
                    "is_lost": False
                })

            # 处理消失的目标
            current_time = time.time()
            lost_ids = []

            for track_id, target in self.targets.items():
                # 检查目标是否在当前帧中未被检测到
                if track_id not in track_ids:
                    time_since_last_seen = current_time - target["last_seen"]

                    # 如果目标超过3秒未被检测到，则认为丢失
                    if time_since_last_seen > 3.0:
                        lost_ids.append(track_id)
                    # 如果目标少于3秒未被检测到，则将其标记为暂时丢失
                    else:
                        target["lost_counter"] += 1

                        # 添加到处理结果列表，标记为暂时丢失
                        processed_targets.append({
                            "id": int(track_id),
                            "class": target["class_zh"],
                            "box": (0, 0, 0, 0),  # 没有有效的目标框
                            "confidence": 0.0,
                            "track_points": target["track_points"],
                            "speed": float(target["speed"]),
                            "is_lost": True
                        })

            # 移除完全丢失的目标
            for lost_id in lost_ids:
                del self.targets[lost_id]

            # 计算平均速度
            avg_speed = total_speed / active_targets if active_targets > 0 else 0

            # 更新统计信息
            self.stats = {
                "total": active_targets,
                "class_counts": class_counts,
                "avg_speed": avg_speed
            }

            # 返回格式化的结果
            return {
                "targets": processed_targets,
                "stats": self.stats,
                "frame": processed_frame
            }

        except Exception as e:
            self.error_signal.emit(f"处理检测结果异常: {str(e)}")
            import traceback
            traceback.print_exc()
            return None

    def draw_tracking_info(self, frame, track_id, bbox, target, class_name, confidence):
        """在帧上绘制跟踪信息

        Args:
            frame: 原始帧
            track_id: 跟踪ID
            bbox: 边界框
            target: 目标字典
            class_name: 目标类别名称
            confidence: 置信度
        """
        try:
            # 获取目标颜色
            color = target["color"]

            # 绘制边界框
            x1, y1, x2, y2 = bbox
            cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)

            # 如果需要显示标签
            if self.show_labels:
                # 创建标签文本
                label = f"ID:{track_id} {class_name} {confidence:.2f}"

                # 绘制标签背景
                text_size, _ = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)
                cv2.rectangle(frame, (x1, y1 - 20), (x1 + text_size[0], y1), color, -1)

                # 绘制标签文本
                cv2.putText(frame, label, (x1, y1 - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

            # 如果需要显示轨迹
            if self.show_trails and len(target["track_points"]) > 1:
                # 绘制轨迹线
                points = np.array(target["track_points"], dtype=np.int32)
                cv2.polylines(frame, [points], False, color, 2)

                # 绘制当前位置点
                current_point = target["track_points"][-1]
                cv2.circle(frame, current_point, 5, color, -1)

        except Exception as e:
            self.error_signal.emit(f"绘制跟踪信息异常: {str(e)}")

    def get_target_thumbnail(self, frame, bbox):
        """从原始帧中裁剪目标缩略图

        Args:
            frame: 原始帧
            bbox: 边界框 (x1, y1, x2, y2)

        Returns:
            numpy.ndarray: 目标缩略图
        """
        try:
            x1, y1, x2, y2 = bbox

            # 确保坐标有效
            h, w = frame.shape[:2]
            x1, y1 = max(0, x1), max(0, y1)
            x2, y2 = min(w, x2), min(h, y2)

            # 裁剪目标区域
            if x2 > x1 and y2 > y1:
                thumbnail = frame[y1:y2, x1:x2].copy()

                # 调整大小到固定尺寸
                thumbnail = cv2.resize(thumbnail, (80, 80), interpolation=cv2.INTER_AREA)
                return thumbnail

        except Exception as e:
            self.error_signal.emit(f"获取缩略图异常: {str(e)}")

        # 如果出错，返回一个空图像
        return np.zeros((80, 80, 3), dtype=np.uint8)

    def generate_color(self, target_id):
        """根据目标ID生成固定颜色

        Args:
            target_id: 目标ID

        Returns:
            tuple: BGR颜色元组 (B, G, R)
        """
        import random
        random.seed(target_id)

        # 生成全范围花品色彩，避免太暗或太亮
        r = random.randint(100, 255)
        g = random.randint(100, 255)
        b = random.randint(100, 255)

        return (b, g, r)  # 返回BGR格式元组
