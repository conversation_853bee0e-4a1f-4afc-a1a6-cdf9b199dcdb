# -*- coding: utf-8 -*-
# @Description : 事故检测服务
# @Date : 2025年6月20日

import json
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import numpy as np
from utils.database import get_db_connection

class AccidentService:
    """事故检测服务类"""
    
    def __init__(self):
        self.active_detectors = {}
        self.configs = {}
        self.detection_threads = {}
        self.accident_history = {}
        self.alert_configs = {}
        
    def update_config(self, monitor_id: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """更新事故检测配置"""
        try:
            self.configs[monitor_id] = config
            
            # 保存配置到数据库
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 检查是否已存在配置
            cursor.execute(
                "SELECT config_id FROM algorithm_configs WHERE monitor_id = %s AND config_type = 'accident'",
                (monitor_id,)
            )
            existing = cursor.fetchone()
            
            config_json = json.dumps(config)
            
            if existing:
                cursor.execute(
                    "UPDATE algorithm_configs SET config_params = %s, updated_at = %s WHERE config_id = %s",
                    (config_json, datetime.now(), existing[0])
                )
            else:
                cursor.execute(
                    "INSERT INTO algorithm_configs (monitor_id, config_type, algorithm_name, config_params, created_at, updated_at) VALUES (%s, %s, %s, %s, %s, %s)",
                    (monitor_id, 'accident', 'accident_detection', config_json, datetime.now(), datetime.now())
                )
            
            conn.commit()
            cursor.close()
            conn.close()
            
            return {'success': True, 'message': '事故检测配置更新成功'}
            
        except Exception as e:
            return {'success': False, 'message': f'配置更新失败: {str(e)}'}
    
    def start_detection(self, monitor_id: str) -> Dict[str, Any]:
        """启动事故检测"""
        try:
            if monitor_id in self.detection_threads and self.detection_threads[monitor_id].is_alive():
                return {'success': False, 'message': '事故检测已在运行中'}
            
            # 获取配置
            config = self.configs.get(monitor_id, self._get_default_config())
            
            # 启动检测线程
            thread = threading.Thread(
                target=self._detection_worker,
                args=(monitor_id, config),
                daemon=True
            )
            thread.start()
            self.detection_threads[monitor_id] = thread
            
            return {'success': True, 'message': '事故检测启动成功'}
            
        except Exception as e:
            return {'success': False, 'message': f'启动失败: {str(e)}'}
    
    def stop_detection(self, monitor_id: str) -> Dict[str, Any]:
        """停止事故检测"""
        try:
            if monitor_id in self.active_detectors:
                self.active_detectors[monitor_id] = False
            
            if monitor_id in self.detection_threads:
                # 等待线程结束
                self.detection_threads[monitor_id].join(timeout=5)
                del self.detection_threads[monitor_id]
            
            return {'success': True, 'message': '事故检测停止成功'}
            
        except Exception as e:
            return {'success': False, 'message': f'停止失败: {str(e)}'}
    
    def get_detection_status(self, monitor_id: str) -> Dict[str, Any]:
        """获取检测状态"""
        is_running = (
            monitor_id in self.detection_threads and 
            self.detection_threads[monitor_id].is_alive()
        )
        
        return {
            'monitor_id': monitor_id,
            'is_running': is_running,
            'config': self.configs.get(monitor_id, {}),
            'last_detection': self.accident_history.get(monitor_id, {}).get('last_detection')
        }
    
    def save_accident_record(self, monitor_id: str, accident_data: Dict[str, Any]) -> Dict[str, Any]:
        """保存事故记录"""
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute(
                """INSERT INTO accident_records 
                   (monitor_id, accident_type, severity, location, description, 
                    detection_time, confidence, status, metadata) 
                   VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)""",
                (
                    monitor_id,
                    accident_data.get('type', 'unknown'),
                    accident_data.get('severity', 'medium'),
                    json.dumps(accident_data.get('location', {})),
                    accident_data.get('description', ''),
                    accident_data.get('detection_time', datetime.now()),
                    accident_data.get('confidence', 0.0),
                    'detected',
                    json.dumps(accident_data.get('metadata', {}))
                )
            )
            
            record_id = cursor.lastrowid
            conn.commit()
            cursor.close()
            conn.close()
            
            # 触发预警
            self._trigger_alert(monitor_id, accident_data)
            
            return {'success': True, 'record_id': record_id}
            
        except Exception as e:
            return {'success': False, 'message': f'保存失败: {str(e)}'}
    
    def get_accident_records(self, monitor_id: Optional[str] = None, 
                           accident_type: Optional[str] = None,
                           start_date: Optional[str] = None,
                           end_date: Optional[str] = None,
                           page: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """获取事故记录"""
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 构建查询条件
            conditions = []
            params = []
            
            if monitor_id:
                conditions.append("monitor_id = %s")
                params.append(monitor_id)
            
            if accident_type:
                conditions.append("accident_type = %s")
                params.append(accident_type)
            
            if start_date:
                conditions.append("detection_time >= %s")
                params.append(start_date)
            
            if end_date:
                conditions.append("detection_time <= %s")
                params.append(end_date)
            
            where_clause = " WHERE " + " AND ".join(conditions) if conditions else ""
            
            # 获取总数
            cursor.execute(f"SELECT COUNT(*) FROM accident_records{where_clause}", params)
            total = cursor.fetchone()[0]
            
            # 获取记录
            offset = (page - 1) * page_size
            cursor.execute(
                f"""SELECT record_id, monitor_id, accident_type, severity, location, 
                           description, detection_time, confidence, status, metadata
                    FROM accident_records{where_clause}
                    ORDER BY detection_time DESC
                    LIMIT %s OFFSET %s""",
                params + [page_size, offset]
            )
            
            records = []
            for row in cursor.fetchall():
                records.append({
                    'record_id': row[0],
                    'monitor_id': row[1],
                    'accident_type': row[2],
                    'severity': row[3],
                    'location': json.loads(row[4]) if row[4] else {},
                    'description': row[5],
                    'detection_time': row[6].isoformat() if row[6] else None,
                    'confidence': float(row[7]) if row[7] else 0.0,
                    'status': row[8],
                    'metadata': json.loads(row[9]) if row[9] else {}
                })
            
            cursor.close()
            conn.close()
            
            return {
                'success': True,
                'records': records,
                'total': total,
                'page': page,
                'page_size': page_size,
                'total_pages': (total + page_size - 1) // page_size
            }
            
        except Exception as e:
            return {'success': False, 'message': f'查询失败: {str(e)}'}
    
    def get_accident_statistics(self, monitor_id: Optional[str] = None,
                              start_date: Optional[str] = None,
                              end_date: Optional[str] = None,
                              accident_type: Optional[str] = None) -> Dict[str, Any]:
        """获取事故统计数据"""
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 构建查询条件
            conditions = []
            params = []
            
            if monitor_id:
                conditions.append("monitor_id = %s")
                params.append(monitor_id)
            
            if start_date:
                conditions.append("detection_time >= %s")
                params.append(start_date)
            
            if end_date:
                conditions.append("detection_time <= %s")
                params.append(end_date)
            
            if accident_type:
                conditions.append("accident_type = %s")
                params.append(accident_type)
            
            where_clause = " WHERE " + " AND ".join(conditions) if conditions else ""
            
            # 总事故数
            cursor.execute(f"SELECT COUNT(*) FROM accident_records{where_clause}", params)
            total_accidents = cursor.fetchone()[0]
            
            # 按类型统计
            cursor.execute(
                f"SELECT accident_type, COUNT(*) FROM accident_records{where_clause} GROUP BY accident_type",
                params
            )
            by_type = {row[0]: row[1] for row in cursor.fetchall()}
            
            # 按严重程度统计
            cursor.execute(
                f"SELECT severity, COUNT(*) FROM accident_records{where_clause} GROUP BY severity",
                params
            )
            by_severity = {row[0]: row[1] for row in cursor.fetchall()}
            
            # 按日期统计（最近7天）
            if not start_date:
                start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
            
            cursor.execute(
                f"""SELECT DATE(detection_time) as date, COUNT(*) 
                    FROM accident_records{where_clause}
                    GROUP BY DATE(detection_time)
                    ORDER BY date""",
                params
            )
            by_date = {str(row[0]): row[1] for row in cursor.fetchall()}
            
            cursor.close()
            conn.close()
            
            return {
                'success': True,
                'statistics': {
                    'total_accidents': total_accidents,
                    'by_type': by_type,
                    'by_severity': by_severity,
                    'by_date': by_date
                }
            }
            
        except Exception as e:
            return {'success': False, 'message': f'统计失败: {str(e)}'}
    
    def update_alert_config(self, monitor_id: str, alert_config: Dict[str, Any]) -> Dict[str, Any]:
        """更新预警配置"""
        try:
            self.alert_configs[monitor_id] = alert_config
            
            # 保存到数据库
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute(
                "SELECT config_id FROM algorithm_configs WHERE monitor_id = %s AND config_type = 'accident_alert'",
                (monitor_id,)
            )
            existing = cursor.fetchone()
            
            config_json = json.dumps(alert_config)
            
            if existing:
                cursor.execute(
                    "UPDATE algorithm_configs SET config_params = %s, updated_at = %s WHERE config_id = %s",
                    (config_json, datetime.now(), existing[0])
                )
            else:
                cursor.execute(
                    "INSERT INTO algorithm_configs (monitor_id, config_type, algorithm_name, config_params, created_at, updated_at) VALUES (%s, %s, %s, %s, %s, %s)",
                    (monitor_id, 'accident_alert', 'alert_system', config_json, datetime.now(), datetime.now())
                )
            
            conn.commit()
            cursor.close()
            conn.close()
            
            return {'success': True, 'message': '预警配置更新成功'}
            
        except Exception as e:
            return {'success': False, 'message': f'配置更新失败: {str(e)}'}
    
    def _detection_worker(self, monitor_id: str, config: Dict[str, Any]):
        """事故检测工作线程"""
        self.active_detectors[monitor_id] = True
        
        while self.active_detectors.get(monitor_id, False):
            try:
                # 模拟事故检测逻辑
                detection_result = self._detect_accidents(monitor_id, config)
                
                if detection_result and detection_result.get('accident_detected'):
                    # 保存事故记录
                    self.save_accident_record(monitor_id, detection_result)
                
                # 更新历史记录
                if monitor_id not in self.accident_history:
                    self.accident_history[monitor_id] = {}
                self.accident_history[monitor_id]['last_detection'] = datetime.now().isoformat()
                
                time.sleep(1)  # 检测间隔
                
            except Exception as e:
                print(f"事故检测错误: {e}")
                time.sleep(5)
        
        self.active_detectors[monitor_id] = False
    
    def _detect_accidents(self, monitor_id: str, config: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """执行事故检测算法"""
        # 这里应该实现具体的事故检测算法
        # 包括碰撞检测、急停检测、拥堵检测等
        
        # 模拟检测结果
        import random
        if random.random() < 0.01:  # 1% 概率检测到事故
            accident_types = ['collision', 'sudden_stop', 'congestion']
            severities = ['low', 'medium', 'high']
            
            return {
                'accident_detected': True,
                'type': random.choice(accident_types),
                'severity': random.choice(severities),
                'location': {'x': random.randint(0, 1920), 'y': random.randint(0, 1080)},
                'description': f'检测到{random.choice(accident_types)}事故',
                'detection_time': datetime.now(),
                'confidence': random.uniform(0.7, 0.95),
                'metadata': {
                    'algorithm': 'accident_detection_v1',
                    'monitor_id': monitor_id
                }
            }
        
        return None
    
    def _trigger_alert(self, monitor_id: str, accident_data: Dict[str, Any]):
        """触发事故预警"""
        alert_config = self.alert_configs.get(monitor_id, {})
        
        if not alert_config.get('enabled', False):
            return
        
        # 检查严重程度阈值
        severity_threshold = alert_config.get('severity_threshold', 'medium')
        severity_levels = {'low': 1, 'medium': 2, 'high': 3}
        
        if severity_levels.get(accident_data.get('severity', 'low'), 1) < severity_levels.get(severity_threshold, 2):
            return
        
        # 这里应该实现具体的预警逻辑
        # 包括邮件、短信、Webhook等
        print(f"事故预警: 监控点{monitor_id}检测到{accident_data.get('type')}事故")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'collision_detection': {
                'enabled': True,
                'sensitivity': 0.7,
                'min_speed_diff': 20
            },
            'sudden_stop_detection': {
                'enabled': True,
                'deceleration_threshold': -5.0,
                'time_window': 2.0
            },
            'congestion_detection': {
                'enabled': True,
                'density_threshold': 0.8,
                'speed_threshold': 10.0
            }
        }