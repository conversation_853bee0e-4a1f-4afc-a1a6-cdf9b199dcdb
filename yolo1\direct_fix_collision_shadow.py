#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接修复车辆碰撞检测对话框的黑色阴影问题
"""

import os

def direct_fix_collision_shadow():
    """直接修复车辆碰撞检测对话框阴影问题"""
    print("正在直接修复车辆碰撞检测对话框阴影问题...")
    
    file_path = "ui/dialog/collision_detection_dialog.py"
    if not os.path.exists(file_path):
        print(f"错误：找不到文件 {file_path}")
        return False
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 查找并修改阴影效果
    shadow_found = False
    for i in range(len(lines)):
        # 查找阴影设置代码
        if "shadow.setColor(QColor(" in lines[i] and "0, 0, 0" in lines[i]:
            lines[i] = '        shadow.setColor(QColor(100, 180, 255, 35))  # 浅蓝色阴影\n'
            shadow_found = True
            print(f"在第 {i+1} 行找到并修改了阴影颜色")
        
        # 查找阴影模糊半径设置
        if "shadow.setBlurRadius(" in lines[i] and not shadow_found:
            blur_value = lines[i].strip()
            lines[i] = '        shadow.setBlurRadius(25)  # 减小模糊半径\n'
            print(f"在第 {i+1} 行找到并修改了模糊半径: {blur_value}")
            shadow_found = True
        
        # 查找阴影偏移设置
        if "shadow.setOffset(" in lines[i] and not shadow_found:
            offset_value = lines[i].strip()
            lines[i] = '        shadow.setOffset(0, 8)  # 减小偏移\n'
            print(f"在第 {i+1} 行找到并修改了偏移: {offset_value}")
            shadow_found = True
    
    if shadow_found:
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.writelines(lines)
        print("✅ 车辆碰撞检测对话框阴影已直接修改为浅蓝色")
        return True
    else:
        print("❌ 未找到阴影设置代码，无法修复")
        return False

if __name__ == "__main__":
    direct_fix_collision_shadow()