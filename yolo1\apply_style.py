# -*- coding: utf-8 -*-
# 应用UI美化和标题更新

import sys
import os
from PySide6.QtWidgets import QApplication, QGraphicsEffect, QPushButton, QFrame
from PySide6.QtCore import QFile, QTextStream
from PySide6.QtGui import QFont, QColor, QPalette

# 创建样式表内容
def create_style_sheet():
    style = """
    /* 全局样式 */
    #Main_QF {
        background-color: qlineargradient(x0:0, y0:1, x1:1, y1:1,stop:0.4 rgb(42, 62, 80), stop:1 rgb(26, 42, 58));
        border: 0px solid red;
        border-radius: 20px;
    }
    
    /* 标题样式 */
    #Title {
        font: 600 14pt "Microsoft YaHei";
        color: #2c3e50;
    }
    
    /* 信息卡片样式 */
    #Class_QF {
        background-color: rgba(149, 117, 205, 0.85);
        border-radius: 15px;
    }
    
    #Target_QF {
        background-color: rgba(255, 145, 124, 0.85);
        border-radius: 15px;
    }
    
    #Fps_QF {
        background-color: rgba(147, 129, 255, 0.85);
        border-radius: 15px;
    }
    
    #Model_QF {
        background-color: rgba(79, 195, 189, 0.85);
        border-radius: 15px;
    }
    
    /* 侧边栏按钮样式 - 完全清除阴影 */
    #LeftMenuBg {
        background-color: transparent;
        border: none;
    }
    
    #LeftMenuBg QPushButton {
        background-repeat: no-repeat;
        background-position: left center;
        border: none;
        border-left: 20px solid transparent;
        text-align: left;
        padding-left: 40px;
        color: white;
        font: 500 11pt "Microsoft YaHei";
        border-radius: 10px;
        margin: 2px 10px;
        background-color: transparent;
        box-shadow: none;
    }
    
    #LeftMenuBg QPushButton:hover {
        background-color: rgba(255, 255, 255, 0.2);
    }
    
    #LeftMenuBg QPushButton:pressed {
        background-color: rgba(255, 255, 255, 0.3);
    }
    
    /* 进度条样式 */
    QProgressBar {
        border: none;
        background-color: rgba(255, 255, 255, 0.3);
        height: 10px;
        border-radius: 5px;
    }
    
    QProgressBar::chunk {
        background-color: #ff6b6b;
        border-radius: 5px;
    }
    
    /* 开关样式 */
    QCheckBox {
        spacing: 8px;
        color: white;
        font: 400 10pt "Microsoft YaHei";
    }
    
    QCheckBox::indicator {
        width: 30px;
        height: 15px;
        border-radius: 8px;
        background-color: rgba(255, 255, 255, 0.3);
    }
    
    QCheckBox::indicator:checked {
        background-color: #4fd1c5;
    }
    """
    return style

# 应用样式到应用程序
def apply_style_to_app(app):
    # 设置全局字体
    font = QFont("Microsoft YaHei", 10)
    app.setFont(font)
    
    # 设置样式表
    app.setStyleSheet(create_style_sheet())
    
    # 设置全局色调
    app.setStyle("Fusion")
    palette = QPalette()
    palette.setColor(QPalette.Window, QColor(240, 245, 248))
    palette.setColor(QPalette.WindowText, QColor(44, 62, 80))
    palette.setColor(QPalette.Button, QColor(79, 209, 197))
    palette.setColor(QPalette.ButtonText, QColor(255, 255, 255))
    palette.setColor(QPalette.Highlight, QColor(79, 209, 197))
    app.setPalette(palette)

# 修改标题文本
def update_title(window):
    # 找到标题标签并修改文本
    if hasattr(window, 'Title'):
        window.Title.setText("基于Yolov8与ByteTrack的高速公路智慧监控平台项目")

# 加载CSS文件的辅助函数
def load_css_file(window, css_file_path):
    if os.path.exists(css_file_path):
        css_file = QFile(css_file_path)
        if css_file.open(QFile.ReadOnly | QFile.Text):
            stream = QTextStream(css_file)
            css_content = stream.readAll()
            
            # 将CSS应用到窗口
            if hasattr(window, 'LeftMenuBg'):
                window.LeftMenuBg.setStyleSheet(css_content)
                print(f"成功加载CSS文件: {css_file_path}")
            css_file.close()
            return True
    print(f"无法加载CSS文件: {css_file_path}")
    return False

# 移除左侧导航栏按钮上的阴影 - 增强版
def remove_shadow_from_leftmenu(window):
    # 尝试加载专门的CSS文件
    css_file_path = "ui/leftmenu_style.css"
    
    # 强制应用自定义CSS样式
    if hasattr(window, 'LeftMenuBg'):
        # 首先移除任何现有的图形效果
        window.LeftMenuBg.setGraphicsEffect(None)
        
        # 读取并应用CSS文件
        if os.path.exists(css_file_path):
            css_file = QFile(css_file_path)
            if css_file.open(QFile.ReadOnly | QFile.Text):
                stream = QTextStream(css_file)
                css_content = stream.readAll()
                window.LeftMenuBg.setStyleSheet(css_content)
                print(f"成功加载CSS文件: {css_file_path}")
                css_file.close()
            else:
                print(f"无法打开CSS文件: {css_file_path}")
        else:
            print(f"CSS文件不存在: {css_file_path}")
        
        # 找到所有导航栏按钮并确保它们应用自定义样式
        buttons = window.LeftMenuBg.findChildren(QPushButton)
        for button in buttons:
            # 移除任何图形效果
            button.setGraphicsEffect(None)
        
        # 找到所有框架并移除它们的阴影
        frames = window.LeftMenuBg.findChildren(QFrame)
        for frame in frames:
            frame.setGraphicsEffect(None)
        
        # 强制更新UI
        window.LeftMenuBg.update()
        
        print("完成导航栏样式应用")
    else:
        print("找不到LeftMenuBg控件")

# 实例化 QApplication
if __name__ == "__main__":
    print("UI美化脚本准备就绪，请将以下代码添加到main.py的main函数中：")
    print("""
    # 在main.py的if __name__ == "__main__"部分修改为：
    app = QApplication(sys.argv)
    
    # 应用美化样式
    from apply_style import apply_style_to_app, remove_shadow_from_leftmenu
    apply_style_to_app(app)
    
    Home = MainWindow()
    
    # 更新标题
    from apply_style import update_title
    update_title(Home)
    
    # 移除导航栏按钮上的阴影
    remove_shadow_from_leftmenu(Home)
    
    Home.show()
    sys.exit(app.exec())
    """)
    
    print("\n或者直接修改main.py文件，在 if __name__ == \"__main__\" 部分之前添加：")
    print("""
    # 修改标题的方法
    def update_window_title(self):
        self.Title.setText("基于yolov8模型的多源多感知检测系统")
    
    # 在MainWindow类的__init__方法末尾添加：
    self.update_window_title()  # 更新窗口标题
    """)
    
    print("\n然后替换阴影样式为：")
    print("""
    # 设置阴影和样式，但不设置左侧菜单栏的阴影
    UIFuncitons.shadow_style(self, self.Class_QF, QColor(149, 117, 205))
    UIFuncitons.shadow_style(self, self.Target_QF, QColor(255, 145, 124))
    UIFuncitons.shadow_style(self, self.Fps_QF, QColor(147, 129, 255))
    UIFuncitons.shadow_style(self, self.Model_QF, QColor(79, 195, 189))
    
    # 设置主背景样式
    self.Main_QF.setStyleSheet(u"QFrame#Main_QF{\n"
    "    background-color: qlineargradient(x0:0, y0:1, x1:1, y1:1,stop:0.4 rgb(240, 245, 248), stop:1 rgb(214, 227, 231));\n"
    "    border:0px solid red;\n"
    "    border-radius:20px\n"
    "}")
    """)
