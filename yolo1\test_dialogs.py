#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试对话框UI修复
"""

import sys
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

def test_pedestrian_dialog():
    """测试行人检测对话框"""
    try:
        from ui.dialog.simple_pedestrian_dialog import SimplePedestrianDialog
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口作为父窗口
        main_window = QMainWindow()
        main_window.setWindowTitle("测试主窗口")
        main_window.setGeometry(100, 100, 800, 600)
        main_window.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgb(30, 41, 59),
                    stop:1 rgb(15, 23, 42));
            }
        """)
        main_window.show()
        
        # 创建并显示行人检测对话框
        dialog = SimplePedestrianDialog(main_window, None)
        dialog.show()
        
        print("✅ 行人检测对话框创建成功")
        return True
        
    except Exception as e:
        print(f"❌ 行人检测对话框测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_collision_dialog():
    """测试碰撞检测对话框"""
    try:
        from ui.dialog.collision_detection_dialog import CollisionDetectionDialog
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口作为父窗口
        main_window = QMainWindow()
        main_window.setWindowTitle("测试主窗口")
        main_window.setGeometry(100, 100, 800, 600)
        main_window.show()
        
        # 创建并显示碰撞检测对话框
        dialog = CollisionDetectionDialog(main_window)
        dialog.show()
        
        print("✅ 碰撞检测对话框创建成功")
        return True
        
    except Exception as e:
        print(f"❌ 碰撞检测对话框测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_road_warning_dialog():
    """测试道路预警对话框"""
    try:
        from ui.dialog.road_warning_dialog import RoadWarningDialog
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口作为父窗口
        main_window = QMainWindow()
        main_window.setWindowTitle("测试主窗口")
        main_window.setGeometry(100, 100, 800, 600)
        main_window.show()
        
        # 创建并显示道路预警对话框
        dialog = RoadWarningDialog(main_window)
        dialog.show()
        
        print("✅ 道路预警对话框创建成功")
        return True
        
    except Exception as e:
        print(f"❌ 道路预警对话框测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 开始测试对话框UI修复...")
    
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    # 测试结果
    results = []
    
    print("\n1. 测试行人检测对话框...")
    results.append(test_pedestrian_dialog())
    
    print("\n2. 测试碰撞检测对话框...")
    results.append(test_collision_dialog())
    
    print("\n3. 测试道路预警对话框...")
    results.append(test_road_warning_dialog())
    
    # 显示测试结果
    print("\n" + "="*50)
    print("📊 测试结果汇总:")
    print(f"✅ 成功: {sum(results)}")
    print(f"❌ 失败: {len(results) - sum(results)}")
    
    if all(results):
        print("🎉 所有对话框测试通过！")
    else:
        print("⚠️ 部分对话框存在问题，请检查错误信息")
    
    # 保持应用运行以查看对话框
    print("\n💡 对话框已显示，请检查UI效果。按 Ctrl+C 退出。")
    try:
        sys.exit(app.exec())
    except KeyboardInterrupt:
        print("\n👋 测试结束")

if __name__ == "__main__":
    main()
