<svg width="1200" height="800" viewBox="0 0 1200 800" xmlns="http://www.w3.org/2000/svg" style="background-color:#f0f2f5; font-family: 'Segoe UI', 'Roboto', sans-serif;">

  <!-- Main Title -->
  <text x="50" y="50" font-size="28" font-weight="bold" fill="#333">多目标追踪系统 (Multi-Target Tracking System)</text>

  <!-- Video Grid -->
  <g id="video-grid">
    <rect x="50" y="80" width="700" height="500" fill="#fff" stroke="#ddd" stroke-width="1" rx="8"/>
    <text x="60" y="105" font-size="16" font-weight="600" fill="#555">视频网格 (Video Grid)</text>
    <g class="video-cell">
      <rect x="60" y="120" width="335" height="230" fill="#000" rx="4"/>
      <text x="70" y="140" fill="#fff">Camera 1</text>
    </g>
    <g class="video-cell">
      <rect x="405" y="120" width="335" height="230" fill="#000" rx="4"/>
      <text x="415" y="140" fill="#fff">Camera 2</text>
    </g>
    <g class="video-cell">
      <rect x="60" y="360" width="335" height="230" fill="#000" rx="4"/>
      <text x="70" y="380" fill="#fff">Camera 3</text>
    </g>
    <g class="video-cell">
      <rect x="405" y="360" width="335" height="230" fill="#000" rx="4"/>
      <text x="415" y="380" fill="#fff">Camera 4</text>
    </g>
  </g>

  <!-- Global Controls -->
  <g id="global-controls">
    <rect x="50" y="600" width="700" height="150" fill="#fff" stroke="#ddd" stroke-width="1" rx="8"/>
    <text x="60" y="625" font-size="16" font-weight="600" fill="#555">全局控制 (Global Controls)</text>
    <text x="70" y="660" font-size="14" fill="#333">活跃目标总数: 128</text>
    
    <text x="70" y="700" font-size="14" fill="#333">筛选器:</text>
    <text x="150" y="700" font-size="14" fill="#333">目标类型:</text>
    <rect x="220" y="685" width="100" height="25" fill="#fff" stroke="#ccc" rx="4"/>
    <text x="230" y="702" font-size="12" fill="#333">Car</text>
    <path d="M310 695 l5 5 l5 -5" stroke="#333" fill="none"/>

    <text x="350" y="700" font-size="14" fill="#333">置信度 > </text>
    <rect x="420" y="685" width="60" height="25" fill="#fff" stroke="#ccc" rx="4"/>
    <text x="430" y="702" font-size="12" fill="#333">0.85</text>
  </g>

  <!-- Right Panel -->
  <g id="right-panel">
    <!-- Target List -->
    <g id="target-list">
      <rect x="780" y="80" width="370" height="340" fill="#fff" stroke="#ddd" stroke-width="1" rx="8"/>
      <text x="790" y="105" font-size="16" font-weight="600" fill="#555">目标列表 (Target List)</text>
      <!-- List Header -->
      <rect x="790" y="120" width="350" height="30" fill="#f9f9f9"/>
      <text x="800" y="140" font-size="12" font-weight="bold">ID</text>
      <text x="850" y="140" font-size="12" font-weight="bold">Type</text>
      <text x="910" y="140" font-size="12" font-weight="bold">Confidence</text>
      <text x="1000" y="140" font-size="12" font-weight="bold">Last Seen</text>
      <!-- List Items -->
      <g class="list-item" style="cursor:pointer;">
        <rect x="790" y="150" width="350" height="30" fill="#e9f5ff"/>
        <text x="800" y="170" font-size="12">101</text>
        <text x="850" y="170" font-size="12">Car</text>
        <text x="910" y="170" font-size="12">0.98</text>
        <text x="1000" y="170" font-size="12">Cam 2</text>
      </g>
      <g class="list-item">
        <rect x="790" y="180" width="350" height="30" fill="#fff"/>
        <text x="800" y="200" font-size="12">102</text>
        <text x="850" y="200" font-size="12">Bus</text>
        <text x="910" y="200" font-size="12">0.95</text>
        <text x="1000" y="200" font-size="12">Cam 1</text>
      </g>
      <g class="list-item">
        <rect x="790" y="210" width="350" height="30" fill="#fff"/>
        <text x="800" y="230" font-size="12">103</text>
        <text x="850" y="230" font-size="12">Car</text>
        <text x="910" y="230" font-size="12">0.92</text>
        <text x="1000" y="230" font-size="12">Cam 4</text>
      </g>
      <!-- ... more items ... -->
      <text x="920" y="400" font-size="12" fill="#999">... more items ...</text>
    </g>

    <!-- Target Details -->
    <g id="target-details">
      <rect x="780" y="440" width="370" height="310" fill="#fff" stroke="#ddd" stroke-width="1" rx="8"/>
      <text x="790" y="465" font-size="16" font-weight="600" fill="#555">目标详情 (Target Details) - ID: 101</text>
      
      <!-- Snapshot -->
      <rect x="790" y="485" width="150" height="100" fill="#ccc" rx="4"/>
      <text x="830" y="535" font-size="12" fill="#fff">Snapshot</text>

      <!-- Info -->
      <g class="info-item">
        <text x="960" y="500" font-size="14">类型: Car</text>
        <text x="960" y="525" font-size="14">置信度: 0.98</text>
        <text x="960" y="550" font-size="14">首次出现: 10:32:15</text>
        <text x="960" y="575" font-size="14">最后更新: 10:34:50</text>
      </g>

      <!-- History Trail -->
      <text x="790" y="615" font-size="14" font-weight="600">历史轨迹 (History Trail):</text>
      <rect x="790" y="630" width="350" height="100" fill="#f9f9f9" stroke="#eee" rx="4"/>
      <text x="800" y="650" font-size="12">- 10:32:15 @ Cam 1 (Entrance A)</text>
      <text x="800" y="670" font-size="12">- 10:33:40 @ Cam 3 (Crossroad)</text>
      <text x="800" y="690" font-size="12">- 10:34:50 @ Cam 2 (Exit B)</text>
    </g>
  </g>

</svg>